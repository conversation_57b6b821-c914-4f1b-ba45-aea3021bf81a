.wizlet.wizletRoles .psn-container {
  overflow: hidden;
}
.wizlet.wizletRoles .psn-info {
  float: left;
  margin-top: -0.5rem;
}
.wizlet.wizletRoles .psn-info_Wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 1rem;
}
.wizlet.wizletRoles .psn-info_image {
  border-radius: 50% !important;
  border: 1px solid #cc4a3d;
  width: 300px;
  height: 300px;
}
.wizlet.wizletRoles .psn-info .card-content {
  overflow: hidden;
}
.wizlet.wizletRoles .psn-info .card-content.row {
  margin: 0;
}
.wizlet.wizletRoles .psn-info .card-content.card-content {
  background-color: #FFFFFF;
  border-radius: 10px;
}
.wizlet.wizletRoles .psn-info .card-content .card-title {
  font-family: clientBolder, clientBold, Arial;
  font-size: 150%;
  line-height: 100%;
  text-align: left;
  padding-bottom: 5px;
  border-bottom: 3px solid #cc4a3d;
  margin-bottom: 15px;
}
.wizlet.wizletRoles .psn-info .card-content .card-title.activator, .wizlet.wizletRoles .psn-info .card-content .card-title.reveal {
  position: relative;
}
.wizlet.wizletRoles .psn-info .card-content .card-title.activator i.material-icons.right, .wizlet.wizletRoles .psn-info .card-content .card-title.reveal i.material-icons.right {
  position: absolute;
  top: 0;
  right: -5px;
}
.wizlet.wizletRoles .psn-info .card-content p {
  text-align: left;
}
.wizlet.wizletRoles .psn-info .card-content p:not(.flow-text) {
  font-size: inherit;
}
@media only screen and (max-width : 992px) {
  .wizlet.wizletRoles .psn-info .card-content {
    padding: 12px;
  }
}
.wizlet.wizletRoles .psn-info .card-content img.embed {
  clear: both;
  margin-bottom: 1rem;
}
.wizlet.wizletRoles .psn-info .card-content img.embed.left {
  float: left;
  margin-right: 1.5rem;
}
.wizlet.wizletRoles .psn-info .card-content img.embed.right {
  float: right;
  margin-left: 1.5rem;
}
.wizlet.wizletRoles .psn-info .card-content img.embed.verytiny {
  width: 100px;
  height: 100px;
}
@media only screen and (max-width : 992px) {
  .wizlet.wizletRoles .psn-info .card-content img.embed.verytiny {
    width: 75px;
    height: 75px;
  }
}
.wizlet.wizletRoles .psn-info .card-content img.embed.tiny {
  width: 150px;
  height: 150px;
}
@media only screen and (max-width : 992px) {
  .wizlet.wizletRoles .psn-info .card-content img.embed.tiny {
    width: 100px;
    height: 100px;
  }
}
.wizlet.wizletRoles .psn-info .card-content img.embed.small {
  width: 200px;
  height: 200px;
}
@media only screen and (max-width : 992px) {
  .wizlet.wizletRoles .psn-info .card-content img.embed.small {
    width: 150px;
    height: 150px;
  }
}
.wizlet.wizletRoles .psn-info .card-content img.embed.medium {
  width: 300px;
  height: 300px;
}
@media only screen and (max-width : 992px) {
  .wizlet.wizletRoles .psn-info .card-content img.embed.medium {
    width: 200px;
    height: 200px;
  }
}
.wizlet.wizletRoles .psn-info .card-content img.embed.large {
  width: 400px;
  height: 400px;
}
@media only screen and (max-width : 992px) {
  .wizlet.wizletRoles .psn-info .card-content img.embed.large {
    width: 300px;
    height: 300px;
  }
}
.wizlet.wizletRoles .psn-info .card-content img.embed.extralarge {
  width: 500px;
  height: 500px;
}
@media only screen and (max-width : 992px) {
  .wizlet.wizletRoles .psn-info .card-content img.embed.extralarge {
    width: 400px;
    height: 400px;
  }
}
.wizlet.wizletRoles .psn-info .card-image {
  display: block;
  min-width: 40%;
  max-width: 60%;
  padding: 24px 0px;
}
@media only screen and (min-width : 993px) {
  .wizlet.wizletRoles .psn-info .card-image {
    min-width: 30%;
    max-width: 40%;
  }
}
.wizlet.wizletRoles .psn-info .card-image.small {
  width: 40%;
}
@media only screen and (min-width : 993px) {
  .wizlet.wizletRoles .psn-info .card-image.small {
    width: 30%;
  }
}
.wizlet.wizletRoles .psn-info .card-image.right {
  padding-right: 24px;
}
.wizlet.wizletRoles .psn-info .card-image.left {
  padding-left: 24px;
}
.wizlet.wizletRoles .psn-thumbnails {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-flex-flow: wrap;
      -ms-flex-flow: wrap;
          flex-flow: wrap;
  text-align: center;
  margin-bottom: 1rem !important;
}
.wizlet.wizletRoles .psn-thumbnail {
  padding: 0 0.5rem;
}
.wizlet.wizletRoles .psn-thumbnail:after {
  content: "\f107";
  font-family: "fa-solid-900";
  font-size: 12px;
  color: #FFFFFF;
  line-height: 17px;
  height: 15px;
  width: 15px;
  display: block;
  margin: -5px auto 0;
  border-radius: 50%;
  background-color: #24466b;
  -webkit-box-shadow: 0px -2px 5px rgba(0, 0, 0, 0.7);
          box-shadow: 0px -2px 5px rgba(0, 0, 0, 0.7);
  opacity: 0;
}
.wizlet.wizletRoles .psn-thumbnail_name {
  display: none;
}
.wizlet.wizletRoles .psn-thumbnail_icon {
  margin: auto;
  width: 50px;
  height: 50px;
  border: 2px solid #ededed;
  border-radius: 50%;
  background-position: center;
  -webkit-background-size: cover;
          background-size: cover;
  background-repeat: no-repeat;
  cursor: pointer;
  -webkit-box-shadow: inset -2px -2px 5px rgba(0, 0, 0, 0.5);
          box-shadow: inset -2px -2px 5px rgba(0, 0, 0, 0.5);
  -webkit-filter: saturate(0.3);
  filter: saturate(0.3);
}
.wizlet.wizletRoles .psn-thumbnail:hover .psn-thumbnail_icon {
  -webkit-filter: none;
          filter: none;
  border-color: #24466b;
}
.wizlet.wizletRoles .psn-thumbnail--highlight {
  -webkit-transform: args;
  -ms-transform: args;
  -o-transform: args;
     transform: args;
  -webkit-transition: args;
  -o-transition: args;
  transition: args;
}
.wizlet.wizletRoles .psn-thumbnail--highlight:after {
  opacity: 1;
}
.wizlet.wizletRoles .psn-thumbnail--highlight .psn-thumbnail_icon {
  border: 2px solid #24466b;
  -webkit-box-shadow: none;
          box-shadow: none;
  -webkit-filter: none;
  filter: none;
}
@media only screen and (max-width : 992px) {
  .wizlet.wizletRoles .psn-thumbnail {
    -webkit-transform: none !important;
        -ms-transform: none !important;
         -o-transform: none !important;
            transform: none !important;
    padding: 0;
  }
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletRoles .psn-info_Wrapper {
    padding: 0.5rem 1.3rem !important;
    -webkit-flex-flow: wrap;
        -ms-flex-flow: wrap;
            flex-flow: wrap;
  }
}