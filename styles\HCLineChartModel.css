.wizlet.wizletHCLineChartModel .card .card-content {
  padding: 6px 12px;
}
.wizlet.wizletHCLineChartModel .card .card-content .row {
  margin: 0;
}
.wizlet.wizletHCLineChartModel .highcharts-container > svg {
  width: 100% !important;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-background {
  fill: transparent;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-title {
  font-size: 1.25rem;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-subtitle {
  font-size: 1rem;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-label,
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-axis-labels {
  color: initial;
  fill: initial;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-label > text, .wizlet.wizletHCLineChartModel .highcharts-container .highcharts-label > span,
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-axis-labels > text,
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-axis-labels > span {
  font-size: 1rem;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-label > text:last-child:not([transform]), .wizlet.wizletHCLineChartModel .highcharts-container .highcharts-label > span:last-child:not([transform]),
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-axis-labels > text:last-child:not([transform]),
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-axis-labels > span:last-child:not([transform]) {
  -webkit-transform: translateX(-4px);
      -ms-transform: translateX(-4px);
       -o-transform: translateX(-4px);
          transform: translateX(-4px);
  -webkit-transform: translateY(2px);
      -ms-transform: translateY(2px);
       -o-transform: translateY(2px);
          transform: translateY(2px);
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-data-label span {
  color: #24466b;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-data-label text {
  fill: #24466b;
  text-shadow: 1px 1px #ffffff;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-axis .highcharts-axis-title tspan {
  font-weight: bold;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-color-0 {
  fill: #cc4a3d;
  stroke: #cc4a3d;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-color-1 {
  fill: #24466b;
  stroke: #24466b;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-color-2 {
  fill: #107ab0;
  stroke: #107ab0;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-color-3 {
  fill: #363737;
  stroke: #363737;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-color-4 {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-color-5 {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-color-6 {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-color-7 {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-color-8 {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-color-9 {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-color-10 {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-color-11 {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCLineChartModel .highcharts-container .highcharts-tooltip table tr, .wizlet.wizletHCLineChartModel .highcharts-container .highcharts-tooltip table td {
  padding: 0;
}
.wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color.categoria1 .highcharts-color-0 .highcharts-point, .wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color.categoria1 .highcharts-color-0.highcharts-legend-item .highcharts-graph {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color.categoria1 .highcharts-color-0.highcharts-spline-series .highcharts-graph {
  stroke: #16B2AB;
}
.wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color.categoria2 .highcharts-color-0 .highcharts-point, .wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color.categoria2 .highcharts-color-0.highcharts-legend-item .highcharts-graph {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color.categoria2 .highcharts-color-0.highcharts-spline-series .highcharts-graph {
  stroke: #727FAE;
}
.wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color.categoria3 .highcharts-color-0 .highcharts-point, .wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color.categoria3 .highcharts-color-0.highcharts-legend-item .highcharts-graph {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color.categoria3 .highcharts-color-0.highcharts-spline-series .highcharts-graph {
  stroke: #72BF44;
}
.wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color.categoria4 .highcharts-color-0 .highcharts-point, .wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color.categoria4 .highcharts-color-0.highcharts-legend-item .highcharts-graph {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color.categoria4 .highcharts-color-0.highcharts-spline-series .highcharts-graph {
  stroke: #0AB9F0;
}
.wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color1.categoria1 .highcharts-color-1 .highcharts-point, .wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color1.categoria1 .highcharts-color-1.highcharts-legend-item .highcharts-graph {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color1.categoria1 .highcharts-color-1.highcharts-spline-series .highcharts-graph {
  stroke: #727FAE;
}
.wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color1.categoria2 .highcharts-color-1 .highcharts-point, .wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color1.categoria2 .highcharts-color-1.highcharts-legend-item .highcharts-graph {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color1.categoria2 .highcharts-color-1.highcharts-spline-series .highcharts-graph {
  stroke: #72BF44;
}
.wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color1.categoria3 .highcharts-color-1 .highcharts-point, .wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color1.categoria3 .highcharts-color-1.highcharts-legend-item .highcharts-graph {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCLineChartModel .bach-content-lineChart--container.color1.categoria3 .highcharts-color-1.highcharts-spline-series .highcharts-graph {
  stroke: #0AB9F0;
}