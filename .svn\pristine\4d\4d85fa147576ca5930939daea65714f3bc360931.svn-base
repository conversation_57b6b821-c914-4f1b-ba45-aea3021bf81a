.wizlet.wizletPostits .container .droppable-container img {
  -o-object-fit: contain;
     object-fit: contain;
  width: 100%;
}
.wizlet.wizletPostits .container .droppable-container.wider {
  width: 120%;
  margin-left: -10%;
}
.wizlet.wizletPostits .container .droppable-container #droppable-container {
  padding: 0;
}
.wizlet.wizletPostits .container .droppable-container.disabled {
  pointer-events: none;
}
.wizlet.wizletPostits .container .droppable-container.solution #droppable-container, .wizlet.wizletPostits .container .droppable-container.positioned #droppable-container {
  width: 90% !important;
  margin: 0 5% 20px 5%;
}
.wizlet.wizletPostits .container .droppable-container.solution .draggable-container-left,
.wizlet.wizletPostits .container .droppable-container.solution .draggable-container-center,
.wizlet.wizletPostits .container .droppable-container.solution .draggable-container-right, .wizlet.wizletPostits .container .droppable-container.positioned .draggable-container-left,
.wizlet.wizletPostits .container .droppable-container.positioned .draggable-container-center,
.wizlet.wizletPostits .container .droppable-container.positioned .draggable-container-right {
  height: 0 !important;
}
.wizlet.wizletPostits .container .droppable-container.solution {
  pointer-events: none;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row.stacked {
  position: absolute;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable {
  min-height: 10vw;
  -webkit-background-size: 100% 100%;
          background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-color: transparent;
  cursor: -webkit-grab;
  border: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  margin: 0;
  z-index: 1 !important;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable.bounce {
  -o-animation-name: bounceInfinite;
  -webkit-animation-name: bounceInfinite;
  animation-name: bounceInfinite;
  -o-animation-duration: 5s;
  -webkit-animation-duration: 5s;
  animation-duration: 5s;
  -o-animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -o-animation-timing-function: ease;
  -webkit-animation-timing-function: ease;
  animation-timing-function: ease;
  -o-animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable i.check-icon {
  position: absolute;
  z-index: 1;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable i.check-icon.scaled-out {
  -webkit-transform: scale(0);
      -ms-transform: scale(0);
       -o-transform: scale(0);
          transform: scale(0);
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletPostits .container .droppable-container .draggable-row .draggable i.check-icon {
    font-size: 3rem;
  }
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable.rotated.rotatedLeft {
  background-image: url("../images/postits/positL.png");
  padding: 25px 20px 10px 15px;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable.rotated.rotatedLeft .text {
  -webkit-transform: rotate(-2deg);
      -ms-transform: rotate(-2deg);
       -o-transform: rotate(-2deg);
          transform: rotate(-2deg);
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable.rotated.rotatedLeft i.check-icon.left {
  left: -5px;
  top: 5px;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable.rotated.rotatedLeft i.check-icon.right {
  right: 0;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable.rotated.rotatedRight {
  background-image: url("../images/postits/positR.png");
  padding: 25px 10px 10px 20px;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable.rotated.rotatedRight .text {
  -webkit-transform: rotate(2deg);
      -ms-transform: rotate(2deg);
       -o-transform: rotate(2deg);
          transform: rotate(2deg);
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable.rotated.rotatedRight i.check-icon.left {
  left: 2px;
  top: 0px;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable.rotated.rotatedRight i.check-icon.right {
  right: 0;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable:not(.rotated) {
  padding: 5px;
  border: outset 5px #cc4a3d;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable:not(.rotated) i.check-icon {
  top: -15px;
  right: -15px;
  margin: 0;
  background-color: #FFFFFF;
  border-radius: 50%;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable.empty {
  background: none;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable.ui-state-disabled {
  opacity: 1;
}
.wizlet.wizletPostits .container .droppable-container .draggable-row .draggable .text {
  font-size: 100%;
}
.wizlet.wizletPostits .container .droppable-container.solution .draggable-row, .wizlet.wizletPostits .container .droppable-container.positioned .draggable-row {
  position: absolute;
  width: 18%;
}
.wizlet.wizletPostits .container .droppable-container .slots-container .header {
  color: #cc4a3d;
}
.wizlet.wizletPostits .container .droppable-container .slots-container .slot.wrapper {
  width: 100%;
  margin: 0;
  padding: 10px;
  -webkit-background-size: 100% 100%;
          background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-image: url("../images/postits/whiteBoard.png");
}
.wizlet.wizletPostits .container .droppable-container .slots-container .slot.wrapper .slot.droppable {
  height: 100%;
  width: 100%;
  -webkit-background-size: 90% 90%;
          background-size: 90% 90%;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.75;
}
.wizlet.wizletPostits .container .droppable-container .slots-container.bold > .header {
  font-weight: bold;
}
.wizlet.wizletPostits .container .droppable-container .slots-container.title-color.color-green > .header {
  color: #72BF44;
}
.wizlet.wizletPostits .container .droppable-container .slots-container.title-color.color-red > .header {
  color: #fb054b;
}
.wizlet.wizletPostits .container .droppable-container.absolute_position {
  position: relative;
}
.wizlet.wizletPostits .container .droppable-container.absolute_position:not(.with_grid) {
  width: 100% !important;
  margin: 0;
}
.wizlet.wizletPostits .container .droppable-container.absolute_position > section {
  position: absolute;
}
.wizlet.wizletPostits .container .droppable-container.absolute_position #droppable-container {
  width: 100% !important;
  height: 100%;
  top: 0;
  left: 0;
  padding: 0;
  margin: 0;
}
.wizlet.wizletPostits .container .droppable-container.absolute_position #droppable-container > section {
  position: absolute;
  margin: 0;
  padding: 0;
}
.wizlet.wizletPostits .container .droppable-container.absolute_position #droppable-container > section .draggable-row {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  margin: 0;
  padding: 0;
}
.wizlet.wizletPostits .container .droppable-container.absolute_position #droppable-container > section .draggable-row .draggable {
  min-height: auto;
  text-align: center;
  height: inherit;
  width: inherit;
  padding: 0;
}
.wizlet.wizletPostits .container .droppable-container.absolute_position #droppable-container > section .draggable-row .draggable img {
  width: 100%;
  height: 100%;
}
.wizlet.wizletPostits .container .droppable-container.absolute_position #droppable-container .slots-container {
  position: absolute;
  margin: 0;
  padding: 0;
}
.wizlet.wizletPostits .container .droppable-container.absolute_position #droppable-container .slots-container .wrapper {
  height: 100%;
  padding: 0;
}
.wizlet.wizletPostits .container .droppable-container.absolute_position.solution .draggable-row, .wizlet.wizletPostits .container .droppable-container.absolute_position.positioned .draggable-row {
  width: 100%;
}
.wizlet.wizletPostits .container .droppable-container.no_img_board .slots-container .slot.wrapper {
  background: none;
}
.wizlet.wizletPostits .container .droppable-container.no_img_board .slots-container .slot.wrapper .slot.droppable {
  height: 100%;
  width: 100%;
  background: none;
}
.wizlet.wizletPostits .container .droppable-container.no_img_postit .draggable-row .draggable {
  background: none;
  background-color: #FFFFFF;
}
.wizlet.wizletPostits .container .droppable-container.no_img_postit .draggable-row .draggable.rotated {
  background-image: none;
  padding: 0;
}
.wizlet.wizletPostits .container .droppable-container.no_img_postit .draggable-row .draggable.rotated .text {
  -webkit-transform: none;
      -ms-transform: none;
       -o-transform: none;
          transform: none;
}
.wizlet.wizletPostits .container .droppable-container.no_img_postit .draggable-row .draggable.rotated i.check-icon.left {
  left: 0;
  top: 0;
}
.wizlet.wizletPostits .container .droppable-container.no_img_postit .draggable-row .draggable.rotated i.check-icon.right {
  right: 0;
}
.wizlet.wizletPostits .container .droppable-container.transparent {
  padding-top: 30px;
}
.wizlet.wizletPostits .container .droppable-container.transparent .draggable-row .draggable {
  padding: 5px;
  border: none;
  background-color: transparent;
}
.wizlet.wizletPostits .container .row.submit {
  padding-top: 5px;
  margin-right: 0;
  text-align: right;
}
.wizlet.wizletPostits .container .row.submit #checkBtn[hidden] {
  display: inline-block !important;
  visibility: hidden;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletPostits .container .row.submit .btn {
    font-size: 0;
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
  .wizlet.wizletPostits .container .row.submit .btn i {
    width: inherit;
    display: inline-block;
    text-align: center;
    color: #fff;
    font-size: 1.6rem;
    line-height: 40px;
  }
}