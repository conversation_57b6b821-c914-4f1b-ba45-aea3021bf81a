
define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', './api/wizer-api-extended'], function ($, Q, WizerApi, WizletBase, doT, wizerApiExt) {
    
    var MyCode = function () {
        this.type = 'MyCode';
        this.level = 1;
    };

    MyCode.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        this.wizerApiExt = wizerApiExt.getRegistration(wizerApi);
        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];

        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    MyCode.prototype.unloadHandler = function () {
        WizletBase.unloadHandler({ wizlet: this });
    };

    
    MyCode.prototype.render = function (options) {
        
        var self = this;
        
        return self.templateDefer.promise.then(function (template) {
  
            if (! self.iAmFollower()) {

                //need to wait until Model is loaded in Pulse
                $(document).one('wizer:action:init', function(e, currentAction) { 

                    self.getDBVotes (self.wizletInfo.setquestions).then(function(values){
                        
                        console.log('>Updating '+self.wizletInfo.setquestions.length+' tlInputs from Pulse into the Model...');

                        self.wizerApiExt.newCalcBinderCache.setCalcValues(self.wizletInfo.model, values).then(function (result) {   
                            
                            if (result) {  
                                console.log('*Model updated from all inputs*');
                            } else {
                                console.log('*No inputs for model*');
                            }

                            if (self.wizletInfo.getquestions) {
                                
                                console.log('>Getting '+self.wizletInfo.getquestions.length+' tlOutputs from the Model...');
                                self.getModelVotes (self.wizletInfo.model, self.wizletInfo.getquestions).then(function(modelVotes){
                                    
                                    console.log('>Saving '+modelVotes.length+' tlOutputs from the Model into the Pulse...');
                                    self.wizerApi.addVotes({votes:modelVotes}).then(function (result2) {   
                                        // console.log(modelVotes);
                                        console.log('*All votes from model included*');
                                    }); 
                                });
                            }
                        }); 
                        

                    });

                });
            }
 

        })
        .fail(this.wizerApi.showError)
    };

    
    MyCode.prototype.getDBVotes = function(questions) {
        var self = this;

        var defer = new Q.defer();   

        var values = {};
     
        var questionIds = [];
        $.each(questions, function (idx, question) {
            questionIds.push( self.wizerApi.getQuestionIdByName(question.bind) );
        });

        self.wizerApi.getMyVotes(questionIds).then(function (response) { 
            
            $.each(questionIds, function (idx, questionId) {
                if ( response.votes[questionId] && response.votes[questionId][0] ) {
                    values[questions[idx].tlInput] = response.votes[questionId][0];
                } 
            });
            
            defer.resolve(values);
        });
        
        return defer.promise;        
    }



    MyCode.prototype.getModelVotes = function(model, questions) {
        var self = this;

        var defer = new Q.defer();   

        var votes = [];
        var counter = 1;
     
        var tlOutputs = [];
        
        questions.forEach(function(question, idx) {
            tlOutputs.push(question.tlOutput)
        });

        self.wizerApiExt.getCalcValues(model, tlOutputs).then(function(values){
                    
            questions.forEach(function(question, idx) {

                //console.log(counter+'/'+questions.length,question.bind,values [idx])

                votes.push({
                    questionId: self.wizerApi.getQuestionIdByName(question.bind), 
                    responseText: values [idx]              
                })

                if (counter == questions.length) {
                    defer.resolve(votes); //when all model votes loaded
                } else {
                    counter ++;
                }
            });

        })

        return defer.promise;
        
    }

    MyCode.prototype.getModelVotes_ = function(model, questions) {
        var self = this;

        var defer = new Q.defer();   

        var votes = [];
        var counter = 1;
     
        questions.forEach(function(question, idx) {

            // console.log(question.bind)

            self.wizerApiExt.getCalcValue(model, question.tlOutput).then(function(value){
            
                console.log(counter+'/'+questions.length,question.bind,value)
                votes.push({
                    questionId: self.wizerApi.getQuestionIdByName(question.bind), 
                    responseText: value                
                })

                if (counter == questions.length) {
                    defer.resolve(votes); //when all model votes loaded
                } else {
                    counter ++;
                }
            })
        });

        return defer.promise;
        
    }



    MyCode.prototype.iAmFollower = function () {
        var self = this;
        return (self.wizletInfo.isFollower && 
                self.wizletInfo.DB[self.wizletInfo.isFollower] > 0);
    };


    MyCode.getRegistration = function () {
        return new MyCode();
    };

    return MyCode;

});
