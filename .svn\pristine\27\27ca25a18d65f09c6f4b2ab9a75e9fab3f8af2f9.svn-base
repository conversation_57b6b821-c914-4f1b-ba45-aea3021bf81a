
define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', './api/wizer-api-extended'], function ($, Q, WizerApi, WizletBase, doT, wizerApiExt) {
    
    var MyCode = function () {
        this.type = 'MyCode';
        this.level = 1;
    };

    MyCode.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        this.wizerApiExt = wizerApiExt.getRegistration(wizerApi);
        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];

        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    MyCode.prototype.unloadHandler = function () {
        WizletBase.unloadHandler({ wizlet: this });
    };

    
    MyCode.prototype.render = function (options) {
        
        var self = this;
        
        return self.templateDefer.promise.then(function (template) {
  
            if (! self.iAmFollower()) {

                //need to wait until Model is loaded in Pulse
                $(document).one('wizer:action:init', function(e, currentAction) { 

                    self.getModelVotes (self.wizletInfo.model, self.wizletInfo.getquestions).then(function(modelVotes){
                        
                        self.wizerApi.addVotes({votes:modelVotes}).then(function (result) {   
                            if (result)     
                                console.log('*All votes from model included*');
                        }); 
                    });

                });
            }
 

        })
        .fail(this.wizerApi.showError)
    };

    

    MyCode.prototype.getModelVotes = function(model, questions) {
        var self = this;

        var defer = new Q.defer();   

        var votes = [];
        var counter = 1;
     
        questions.forEach(function(question, idx) {
            
            self.wizerApiExt.getCalcValue(model, question.tlOutput).then(function(value){
            
                console.log(counter+'/'+questions.length,question.bind,value)
                votes.push({
                    questionId: self.wizerApi.getQuestionIdByName(question.bind), 
                    responseText: value                
                })

                if (counter == questions.length) {
                    defer.resolve(votes); //when all model votes loaded
                } else {
                    counter ++;
                }
            })
        });

        return defer.promise;
        
    }


    MyCode.prototype.iAmFollower = function () {
        var self = this;
        return (self.wizletInfo.isFollower && 
                self.wizletInfo.DB[self.wizletInfo.isFollower] > 0);
    };


    MyCode.getRegistration = function () {
        return new MyCode();
    };

    return MyCode;

});
