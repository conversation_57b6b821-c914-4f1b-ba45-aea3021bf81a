/****      rangeSlider CSS      ****/
@import url("rangeSlider/rangeSlider.css");
@import url("rangeSlider/rangeSlider.skinHTML5.css");
.wizlet.wizletRange .header.with-label,
.wizlet.wizletRange_Result .header.with-label {
  position: relative;
  padding-left: 2.5rem;
  margin-bottom: 1rem;
}
.wizlet.wizletRange .header.with-label .badge.header-label,
.wizlet.wizletRange_Result .header.with-label .badge.header-label {
  position: absolute;
  left: 0;
  top: 0;
  background-color: #cc4a3d;
  min-width: 2rem;
  min-height: 2rem;
  font-size: 1.25rem;
  font-weight: bold;
  border-radius: 50%;
  line-height: 2rem;
  margin: 0;
  margin-top: -5px;
}
.wizlet.wizletRange .card .card-content.card-content,
.wizlet.wizletRange_Result .card .card-content.card-content {
  background-color: #FFFFFF;
  border-radius: 10px;
}
.wizlet.wizletRange .card .card-content .card-title,
.wizlet.wizletRange_Result .card .card-content .card-title {
  font-family: clientBolder, clientBold, Arial;
  font-size: 150%;
  line-height: 100%;
  text-align: left;
  padding-bottom: 5px;
  border-bottom: 3px solid #cc4a3d;
  margin-bottom: 15px;
}
.wizlet.wizletRange .card .card-content .card-title.activator, .wizlet.wizletRange .card .card-content .card-title.reveal,
.wizlet.wizletRange_Result .card .card-content .card-title.activator,
.wizlet.wizletRange_Result .card .card-content .card-title.reveal {
  position: relative;
}
.wizlet.wizletRange .card .card-content .card-title.activator i.material-icons.right, .wizlet.wizletRange .card .card-content .card-title.reveal i.material-icons.right,
.wizlet.wizletRange_Result .card .card-content .card-title.activator i.material-icons.right,
.wizlet.wizletRange_Result .card .card-content .card-title.reveal i.material-icons.right {
  position: absolute;
  top: 0;
  right: -5px;
}
.wizlet.wizletRange .card .card-content .card-title,
.wizlet.wizletRange_Result .card .card-content .card-title {
  margin-bottom: 15px;
}
.wizlet.wizletRange .card .card-content .card-title:not(:first-child),
.wizlet.wizletRange_Result .card .card-content .card-title:not(:first-child) {
  margin-top: 2rem;
}
.wizlet.wizletRange .card .card-content .sliderTitle,
.wizlet.wizletRange_Result .card .card-content .sliderTitle {
  margin-bottom: 15px;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
.wizlet.wizletRange .card .card-content .sliderTitle img,
.wizlet.wizletRange_Result .card .card-content .sliderTitle img {
  height: 48px;
  width: 48px;
  -o-object-fit: contain;
     object-fit: contain;
  margin-right: 10px;
}
.wizlet.wizletRange .card .card-content .row,
.wizlet.wizletRange_Result .card .card-content .row {
  margin: 0;
}
.wizlet.wizletRange .card .card-content .header.with-label,
.wizlet.wizletRange_Result .card .card-content .header.with-label {
  position: relative;
  padding-left: 2.5rem;
  margin-bottom: 1rem;
}
.wizlet.wizletRange .card .card-content .header.with-label .badge.header-label,
.wizlet.wizletRange_Result .card .card-content .header.with-label .badge.header-label {
  position: absolute;
  left: 0;
  top: 0;
  background-color: #cc4a3d;
  min-width: 2rem;
  min-height: 2rem;
  font-size: 1.25rem;
  font-weight: bold;
  border-radius: 50%;
  line-height: 2rem;
  margin: 0;
  margin-top: -5px;
}
.wizlet.wizletRange .card .card-content .header,
.wizlet.wizletRange_Result .card .card-content .header {
  margin-top: 0;
}
.wizlet.wizletRange .card .card-content .header.with-label,
.wizlet.wizletRange_Result .card .card-content .header.with-label {
  margin-bottom: 2rem;
}
.wizlet.wizletRange .card .card-content .header:not(:first-child),
.wizlet.wizletRange_Result .card .card-content .header:not(:first-child) {
  margin-top: 2rem;
}
.wizlet.wizletRange .card .card-content i.check-icon,
.wizlet.wizletRange_Result .card .card-content i.check-icon {
  position: absolute;
  z-index: 1;
  -webkit-transform: translate(50%, -50%);
      -ms-transform: translate(50%, -50%);
       -o-transform: translate(50%, -50%);
          transform: translate(50%, -50%);
}
.wizlet.wizletRange .card .card-content i.check-icon.left,
.wizlet.wizletRange_Result .card .card-content i.check-icon.left {
  left: 0;
}
.wizlet.wizletRange .card .card-content i.check-icon.right,
.wizlet.wizletRange_Result .card .card-content i.check-icon.right {
  right: 15px;
}
.wizlet.wizletRange .row.sideTitle,
.wizlet.wizletRange_Result .row.sideTitle {
  margin-bottom: 20px !important;
}
.wizlet.wizletRange .row.sideTitle .sideTitle,
.wizlet.wizletRange_Result .row.sideTitle .sideTitle {
  font-size: 90%;
  margin-top: 20px;
}
.wizlet.wizletRange .row.sideTitle .sideTitle.left,
.wizlet.wizletRange_Result .row.sideTitle .sideTitle.left {
  text-align: left;
}
.wizlet.wizletRange .row.sideTitle .sideTitle.right,
.wizlet.wizletRange_Result .row.sideTitle .sideTitle.right {
  text-align: right;
}
.wizlet.wizletRange .range .irs .irs-single, .wizlet.wizletRange .range .irs .irs-from, .wizlet.wizletRange .range .irs .irs-to,
.wizlet.wizletRange_Result .range .irs .irs-single,
.wizlet.wizletRange_Result .range .irs .irs-from,
.wizlet.wizletRange_Result .range .irs .irs-to {
  background: #cc4a3d;
}
.wizlet.wizletRange .range .irs .irs-min, .wizlet.wizletRange .range .irs .irs-max,
.wizlet.wizletRange_Result .range .irs .irs-min,
.wizlet.wizletRange_Result .range .irs .irs-max {
  top: 10px;
}
.wizlet.wizletRange .range .irs .irs-single,
.wizlet.wizletRange_Result .range .irs .irs-single {
  padding: 5px 10px;
  -webkit-transform: translateY(-10px);
      -ms-transform: translateY(-10px);
       -o-transform: translateY(-10px);
          transform: translateY(-10px);
}
.wizlet.wizletRange .range.nominmax .irs .irs-min, .wizlet.wizletRange .range.nominmax .irs .irs-max,
.wizlet.wizletRange_Result .range.nominmax .irs .irs-min,
.wizlet.wizletRange_Result .range.nominmax .irs .irs-max {
  display: none;
}
.wizlet.wizletRange .range.solution, .wizlet.wizletRange .range.disabled, .wizlet.wizletRange .range.blocked,
.wizlet.wizletRange_Result .range.solution,
.wizlet.wizletRange_Result .range.disabled,
.wizlet.wizletRange_Result .range.blocked {
  pointer-events: none;
}
.wizlet.wizletRange .range.disabled,
.wizlet.wizletRange_Result .range.disabled {
  opacity: 0.8;
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.wizlet.wizletRange .range.color.aux1 .irs-bar, .wizlet.wizletRange .range.color.aux1 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.aux1 .irs-bar,
.wizlet.wizletRange_Result .range.color.aux1 .irs-bar-edge {
  border-color: #cc4a3d;
  background: #cc4a3d;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(204, 74, 61, 0.5)), color-stop(100%, #cc4a3d));
  background: -webkit-linear-gradient(top, rgba(204, 74, 61, 0.5) 0%, #cc4a3d 100%);
  background: -o-linear-gradient(top, rgba(204, 74, 61, 0.5) 0%, #cc4a3d 100%);
  background: ms-linear-gradient(to bottom, rgba(204, 74, 61, 0.5) 0%, #cc4a3d 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(204, 74, 61, 0.5)), to(#cc4a3d));
  background: linear-gradient(to bottom, rgba(204, 74, 61, 0.5) 0%, #cc4a3d 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(204, 74, 61, 0.5), endColorstr=#cc4a3d);
}
.wizlet.wizletRange .range.color.aux2 .irs-bar, .wizlet.wizletRange .range.color.aux2 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.aux2 .irs-bar,
.wizlet.wizletRange_Result .range.color.aux2 .irs-bar-edge {
  border-color: #24466b;
  background: #24466b;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(36, 70, 107, 0.5)), color-stop(100%, #24466b));
  background: -webkit-linear-gradient(top, rgba(36, 70, 107, 0.5) 0%, #24466b 100%);
  background: -o-linear-gradient(top, rgba(36, 70, 107, 0.5) 0%, #24466b 100%);
  background: ms-linear-gradient(to bottom, rgba(36, 70, 107, 0.5) 0%, #24466b 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(36, 70, 107, 0.5)), to(#24466b));
  background: linear-gradient(to bottom, rgba(36, 70, 107, 0.5) 0%, #24466b 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(36, 70, 107, 0.5), endColorstr=#24466b);
}
.wizlet.wizletRange .range.color.aux3 .irs-bar, .wizlet.wizletRange .range.color.aux3 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.aux3 .irs-bar,
.wizlet.wizletRange_Result .range.color.aux3 .irs-bar-edge {
  border-color: #107ab0;
  background: #107ab0;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(16, 122, 176, 0.5)), color-stop(100%, #107ab0));
  background: -webkit-linear-gradient(top, rgba(16, 122, 176, 0.5) 0%, #107ab0 100%);
  background: -o-linear-gradient(top, rgba(16, 122, 176, 0.5) 0%, #107ab0 100%);
  background: ms-linear-gradient(to bottom, rgba(16, 122, 176, 0.5) 0%, #107ab0 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(16, 122, 176, 0.5)), to(#107ab0));
  background: linear-gradient(to bottom, rgba(16, 122, 176, 0.5) 0%, #107ab0 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(16, 122, 176, 0.5), endColorstr=#107ab0);
}
.wizlet.wizletRange .range.color.aux4 .irs-bar, .wizlet.wizletRange .range.color.aux4 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.aux4 .irs-bar,
.wizlet.wizletRange_Result .range.color.aux4 .irs-bar-edge {
  border-color: #363737;
  background: #363737;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(54, 55, 55, 0.5)), color-stop(100%, #363737));
  background: -webkit-linear-gradient(top, rgba(54, 55, 55, 0.5) 0%, #363737 100%);
  background: -o-linear-gradient(top, rgba(54, 55, 55, 0.5) 0%, #363737 100%);
  background: ms-linear-gradient(to bottom, rgba(54, 55, 55, 0.5) 0%, #363737 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(54, 55, 55, 0.5)), to(#363737));
  background: linear-gradient(to bottom, rgba(54, 55, 55, 0.5) 0%, #363737 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(54, 55, 55, 0.5), endColorstr=#363737);
}
.wizlet.wizletRange .range.color.aux5 .irs-bar, .wizlet.wizletRange .range.color.aux5 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.aux5 .irs-bar,
.wizlet.wizletRange_Result .range.color.aux5 .irs-bar-edge {
  border-color: #727FAE;
  background: #727FAE;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(114, 127, 174, 0.5)), color-stop(100%, #727FAE));
  background: -webkit-linear-gradient(top, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  background: -o-linear-gradient(top, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  background: ms-linear-gradient(to bottom, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(114, 127, 174, 0.5)), to(#727FAE));
  background: linear-gradient(to bottom, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(114, 127, 174, 0.5), endColorstr=#727FAE);
}
.wizlet.wizletRange .range.color.aux6 .irs-bar, .wizlet.wizletRange .range.color.aux6 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.aux6 .irs-bar,
.wizlet.wizletRange_Result .range.color.aux6 .irs-bar-edge {
  border-color: #72BF44;
  background: #72BF44;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(114, 191, 68, 0.5)), color-stop(100%, #72BF44));
  background: -webkit-linear-gradient(top, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  background: -o-linear-gradient(top, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  background: ms-linear-gradient(to bottom, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(114, 191, 68, 0.5)), to(#72BF44));
  background: linear-gradient(to bottom, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(114, 191, 68, 0.5), endColorstr=#72BF44);
}
.wizlet.wizletRange .range.color.chart1 .irs-bar, .wizlet.wizletRange .range.color.chart1 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.chart1 .irs-bar,
.wizlet.wizletRange_Result .range.color.chart1 .irs-bar-edge {
  border-color: #cc4a3d;
  background: #cc4a3d;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(204, 74, 61, 0.5)), color-stop(100%, #cc4a3d));
  background: -webkit-linear-gradient(top, rgba(204, 74, 61, 0.5) 0%, #cc4a3d 100%);
  background: -o-linear-gradient(top, rgba(204, 74, 61, 0.5) 0%, #cc4a3d 100%);
  background: ms-linear-gradient(to bottom, rgba(204, 74, 61, 0.5) 0%, #cc4a3d 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(204, 74, 61, 0.5)), to(#cc4a3d));
  background: linear-gradient(to bottom, rgba(204, 74, 61, 0.5) 0%, #cc4a3d 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(204, 74, 61, 0.5), endColorstr=#cc4a3d);
}
.wizlet.wizletRange .range.color.chart2 .irs-bar, .wizlet.wizletRange .range.color.chart2 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.chart2 .irs-bar,
.wizlet.wizletRange_Result .range.color.chart2 .irs-bar-edge {
  border-color: #24466b;
  background: #24466b;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(36, 70, 107, 0.5)), color-stop(100%, #24466b));
  background: -webkit-linear-gradient(top, rgba(36, 70, 107, 0.5) 0%, #24466b 100%);
  background: -o-linear-gradient(top, rgba(36, 70, 107, 0.5) 0%, #24466b 100%);
  background: ms-linear-gradient(to bottom, rgba(36, 70, 107, 0.5) 0%, #24466b 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(36, 70, 107, 0.5)), to(#24466b));
  background: linear-gradient(to bottom, rgba(36, 70, 107, 0.5) 0%, #24466b 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(36, 70, 107, 0.5), endColorstr=#24466b);
}
.wizlet.wizletRange .range.color.chart3 .irs-bar, .wizlet.wizletRange .range.color.chart3 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.chart3 .irs-bar,
.wizlet.wizletRange_Result .range.color.chart3 .irs-bar-edge {
  border-color: #107ab0;
  background: #107ab0;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(16, 122, 176, 0.5)), color-stop(100%, #107ab0));
  background: -webkit-linear-gradient(top, rgba(16, 122, 176, 0.5) 0%, #107ab0 100%);
  background: -o-linear-gradient(top, rgba(16, 122, 176, 0.5) 0%, #107ab0 100%);
  background: ms-linear-gradient(to bottom, rgba(16, 122, 176, 0.5) 0%, #107ab0 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(16, 122, 176, 0.5)), to(#107ab0));
  background: linear-gradient(to bottom, rgba(16, 122, 176, 0.5) 0%, #107ab0 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(16, 122, 176, 0.5), endColorstr=#107ab0);
}
.wizlet.wizletRange .range.color.chart4 .irs-bar, .wizlet.wizletRange .range.color.chart4 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.chart4 .irs-bar,
.wizlet.wizletRange_Result .range.color.chart4 .irs-bar-edge {
  border-color: #363737;
  background: #363737;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(54, 55, 55, 0.5)), color-stop(100%, #363737));
  background: -webkit-linear-gradient(top, rgba(54, 55, 55, 0.5) 0%, #363737 100%);
  background: -o-linear-gradient(top, rgba(54, 55, 55, 0.5) 0%, #363737 100%);
  background: ms-linear-gradient(to bottom, rgba(54, 55, 55, 0.5) 0%, #363737 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(54, 55, 55, 0.5)), to(#363737));
  background: linear-gradient(to bottom, rgba(54, 55, 55, 0.5) 0%, #363737 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(54, 55, 55, 0.5), endColorstr=#363737);
}
.wizlet.wizletRange .range.color.chart5 .irs-bar, .wizlet.wizletRange .range.color.chart5 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.chart5 .irs-bar,
.wizlet.wizletRange_Result .range.color.chart5 .irs-bar-edge {
  border-color: #727FAE;
  background: #727FAE;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(114, 127, 174, 0.5)), color-stop(100%, #727FAE));
  background: -webkit-linear-gradient(top, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  background: -o-linear-gradient(top, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  background: ms-linear-gradient(to bottom, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(114, 127, 174, 0.5)), to(#727FAE));
  background: linear-gradient(to bottom, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(114, 127, 174, 0.5), endColorstr=#727FAE);
}
.wizlet.wizletRange .range.color.chart6 .irs-bar, .wizlet.wizletRange .range.color.chart6 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.chart6 .irs-bar,
.wizlet.wizletRange_Result .range.color.chart6 .irs-bar-edge {
  border-color: #72BF44;
  background: #72BF44;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(114, 191, 68, 0.5)), color-stop(100%, #72BF44));
  background: -webkit-linear-gradient(top, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  background: -o-linear-gradient(top, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  background: ms-linear-gradient(to bottom, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(114, 191, 68, 0.5)), to(#72BF44));
  background: linear-gradient(to bottom, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(114, 191, 68, 0.5), endColorstr=#72BF44);
}
.wizlet.wizletRange .range.color.chart7 .irs-bar, .wizlet.wizletRange .range.color.chart7 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.chart7 .irs-bar,
.wizlet.wizletRange_Result .range.color.chart7 .irs-bar-edge {
  border-color: #0AB9F0;
  background: #0AB9F0;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(10, 185, 240, 0.5)), color-stop(100%, #0AB9F0));
  background: -webkit-linear-gradient(top, rgba(10, 185, 240, 0.5) 0%, #0AB9F0 100%);
  background: -o-linear-gradient(top, rgba(10, 185, 240, 0.5) 0%, #0AB9F0 100%);
  background: ms-linear-gradient(to bottom, rgba(10, 185, 240, 0.5) 0%, #0AB9F0 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(10, 185, 240, 0.5)), to(#0AB9F0));
  background: linear-gradient(to bottom, rgba(10, 185, 240, 0.5) 0%, #0AB9F0 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(10, 185, 240, 0.5), endColorstr=#0AB9F0);
}
.wizlet.wizletRange .range.color.chart8 .irs-bar, .wizlet.wizletRange .range.color.chart8 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.chart8 .irs-bar,
.wizlet.wizletRange_Result .range.color.chart8 .irs-bar-edge {
  border-color: #2E739E;
  background: #2E739E;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(46, 115, 158, 0.5)), color-stop(100%, #2E739E));
  background: -webkit-linear-gradient(top, rgba(46, 115, 158, 0.5) 0%, #2E739E 100%);
  background: -o-linear-gradient(top, rgba(46, 115, 158, 0.5) 0%, #2E739E 100%);
  background: ms-linear-gradient(to bottom, rgba(46, 115, 158, 0.5) 0%, #2E739E 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(46, 115, 158, 0.5)), to(#2E739E));
  background: linear-gradient(to bottom, rgba(46, 115, 158, 0.5) 0%, #2E739E 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(46, 115, 158, 0.5), endColorstr=#2E739E);
}
.wizlet.wizletRange .range.color.chart9 .irs-bar, .wizlet.wizletRange .range.color.chart9 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.chart9 .irs-bar,
.wizlet.wizletRange_Result .range.color.chart9 .irs-bar-edge {
  border-color: #E8CD41;
  background: #E8CD41;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(232, 205, 65, 0.5)), color-stop(100%, #E8CD41));
  background: -webkit-linear-gradient(top, rgba(232, 205, 65, 0.5) 0%, #E8CD41 100%);
  background: -o-linear-gradient(top, rgba(232, 205, 65, 0.5) 0%, #E8CD41 100%);
  background: ms-linear-gradient(to bottom, rgba(232, 205, 65, 0.5) 0%, #E8CD41 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(232, 205, 65, 0.5)), to(#E8CD41));
  background: linear-gradient(to bottom, rgba(232, 205, 65, 0.5) 0%, #E8CD41 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(232, 205, 65, 0.5), endColorstr=#E8CD41);
}
.wizlet.wizletRange .range.color.chart10 .irs-bar, .wizlet.wizletRange .range.color.chart10 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.chart10 .irs-bar,
.wizlet.wizletRange_Result .range.color.chart10 .irs-bar-edge {
  border-color: #16B2AB;
  background: #16B2AB;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(22, 178, 171, 0.5)), color-stop(100%, #16B2AB));
  background: -webkit-linear-gradient(top, rgba(22, 178, 171, 0.5) 0%, #16B2AB 100%);
  background: -o-linear-gradient(top, rgba(22, 178, 171, 0.5) 0%, #16B2AB 100%);
  background: ms-linear-gradient(to bottom, rgba(22, 178, 171, 0.5) 0%, #16B2AB 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(22, 178, 171, 0.5)), to(#16B2AB));
  background: linear-gradient(to bottom, rgba(22, 178, 171, 0.5) 0%, #16B2AB 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(22, 178, 171, 0.5), endColorstr=#16B2AB);
}
.wizlet.wizletRange .range.color.chart11 .irs-bar, .wizlet.wizletRange .range.color.chart11 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.chart11 .irs-bar,
.wizlet.wizletRange_Result .range.color.chart11 .irs-bar-edge {
  border-color: #727FAE;
  background: #727FAE;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(114, 127, 174, 0.5)), color-stop(100%, #727FAE));
  background: -webkit-linear-gradient(top, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  background: -o-linear-gradient(top, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  background: ms-linear-gradient(to bottom, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(114, 127, 174, 0.5)), to(#727FAE));
  background: linear-gradient(to bottom, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(114, 127, 174, 0.5), endColorstr=#727FAE);
}
.wizlet.wizletRange .range.color.chart12 .irs-bar, .wizlet.wizletRange .range.color.chart12 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.chart12 .irs-bar,
.wizlet.wizletRange_Result .range.color.chart12 .irs-bar-edge {
  border-color: #72BF44;
  background: #72BF44;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(114, 191, 68, 0.5)), color-stop(100%, #72BF44));
  background: -webkit-linear-gradient(top, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  background: -o-linear-gradient(top, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  background: ms-linear-gradient(to bottom, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(114, 191, 68, 0.5)), to(#72BF44));
  background: linear-gradient(to bottom, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(114, 191, 68, 0.5), endColorstr=#72BF44);
}
.wizlet.wizletRange .range.color.categoria1 .irs-bar, .wizlet.wizletRange .range.color.categoria1 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.categoria1 .irs-bar,
.wizlet.wizletRange_Result .range.color.categoria1 .irs-bar-edge {
  border-color: #16B2AB;
  background: #16B2AB;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(22, 178, 171, 0.5)), color-stop(100%, #16B2AB));
  background: -webkit-linear-gradient(top, rgba(22, 178, 171, 0.5) 0%, #16B2AB 100%);
  background: -o-linear-gradient(top, rgba(22, 178, 171, 0.5) 0%, #16B2AB 100%);
  background: ms-linear-gradient(to bottom, rgba(22, 178, 171, 0.5) 0%, #16B2AB 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(22, 178, 171, 0.5)), to(#16B2AB));
  background: linear-gradient(to bottom, rgba(22, 178, 171, 0.5) 0%, #16B2AB 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(22, 178, 171, 0.5), endColorstr=#16B2AB);
}
.wizlet.wizletRange .range.color.categoria2 .irs-bar, .wizlet.wizletRange .range.color.categoria2 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.categoria2 .irs-bar,
.wizlet.wizletRange_Result .range.color.categoria2 .irs-bar-edge {
  border-color: #727FAE;
  background: #727FAE;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(114, 127, 174, 0.5)), color-stop(100%, #727FAE));
  background: -webkit-linear-gradient(top, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  background: -o-linear-gradient(top, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  background: ms-linear-gradient(to bottom, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(114, 127, 174, 0.5)), to(#727FAE));
  background: linear-gradient(to bottom, rgba(114, 127, 174, 0.5) 0%, #727FAE 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(114, 127, 174, 0.5), endColorstr=#727FAE);
}
.wizlet.wizletRange .range.color.categoria3 .irs-bar, .wizlet.wizletRange .range.color.categoria3 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.categoria3 .irs-bar,
.wizlet.wizletRange_Result .range.color.categoria3 .irs-bar-edge {
  border-color: #72BF44;
  background: #72BF44;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(114, 191, 68, 0.5)), color-stop(100%, #72BF44));
  background: -webkit-linear-gradient(top, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  background: -o-linear-gradient(top, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  background: ms-linear-gradient(to bottom, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(114, 191, 68, 0.5)), to(#72BF44));
  background: linear-gradient(to bottom, rgba(114, 191, 68, 0.5) 0%, #72BF44 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(114, 191, 68, 0.5), endColorstr=#72BF44);
}
.wizlet.wizletRange .range.color.categoria4 .irs-bar, .wizlet.wizletRange .range.color.categoria4 .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.categoria4 .irs-bar,
.wizlet.wizletRange_Result .range.color.categoria4 .irs-bar-edge {
  border-color: #0AB9F0;
  background: #0AB9F0;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(10, 185, 240, 0.5)), color-stop(100%, #0AB9F0));
  background: -webkit-linear-gradient(top, rgba(10, 185, 240, 0.5) 0%, #0AB9F0 100%);
  background: -o-linear-gradient(top, rgba(10, 185, 240, 0.5) 0%, #0AB9F0 100%);
  background: ms-linear-gradient(to bottom, rgba(10, 185, 240, 0.5) 0%, #0AB9F0 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(10, 185, 240, 0.5)), to(#0AB9F0));
  background: linear-gradient(to bottom, rgba(10, 185, 240, 0.5) 0%, #0AB9F0 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=rgba(10, 185, 240, 0.5), endColorstr=#0AB9F0);
}
.wizlet.wizletRange .range.color.foreBack .irs-bar, .wizlet.wizletRange .range.color.foreBack .irs-bar-edge,
.wizlet.wizletRange_Result .range.color.foreBack .irs-bar,
.wizlet.wizletRange_Result .range.color.foreBack .irs-bar-edge {
  border-color: #0AB9F0;
  background: #0AB9F0;
}
.wizlet.wizletRange .range.color.foreBack .irs-line-mid, .wizlet.wizletRange .range.color.foreBack .irs-line-right,
.wizlet.wizletRange_Result .range.color.foreBack .irs-line-mid,
.wizlet.wizletRange_Result .range.color.foreBack .irs-line-right {
  border-color: #2E739E;
  background: #2E739E;
}
.wizlet.wizletRange .range.custom.image,
.wizlet.wizletRange_Result .range.custom.image {
  margin-top: 20px;
  -webkit-background-size: 100% 100%;
          background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position-y: -15px;
}
.wizlet.wizletRange .range.custom.image .irs-slider,
.wizlet.wizletRange_Result .range.custom.image .irs-slider {
  -webkit-background-size: 100% 100%;
          background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 8.3vw;
  min-width: 53px;
  max-width: 100px;
  height: 7vw;
  min-height: 63px;
  max-height: 84px;
  border: none;
  border-radius: 0px;
  -webkit-box-shadow: none;
          box-shadow: none;
  -webkit-transform: translateY(-20%);
      -ms-transform: translateY(-20%);
       -o-transform: translateY(-20%);
          transform: translateY(-20%);
}
.wizlet.wizletRange .range.custom.image .irs-slider.state_hover, .wizlet.wizletRange .range.custom.image .irs-slider:hover,
.wizlet.wizletRange_Result .range.custom.image .irs-slider.state_hover,
.wizlet.wizletRange_Result .range.custom.image .irs-slider:hover {
  background-color: transparent !important;
}
.wizlet.wizletRange .range.custom.image .irs-single, .wizlet.wizletRange .range.custom.image .irs-from, .wizlet.wizletRange .range.custom.image .irs-to,
.wizlet.wizletRange_Result .range.custom.image .irs-single,
.wizlet.wizletRange_Result .range.custom.image .irs-from,
.wizlet.wizletRange_Result .range.custom.image .irs-to {
  visibility: hidden;
}
.wizlet.wizletRange .range.custom.image .irs-bar, .wizlet.wizletRange .range.custom.image .irs-bar-edge, .wizlet.wizletRange .range.custom.image .irs-line,
.wizlet.wizletRange_Result .range.custom.image .irs-bar,
.wizlet.wizletRange_Result .range.custom.image .irs-bar-edge,
.wizlet.wizletRange_Result .range.custom.image .irs-line {
  border: none;
  background: none;
}
.wizlet.wizletRange .range.custom.image .irs-grid > .irs-grid-text,
.wizlet.wizletRange_Result .range.custom.image .irs-grid > .irs-grid-text {
  visibility: hidden;
}
.wizlet.wizletRange .range.adjustIcon .irs.with-icon > .irs-bar,
.wizlet.wizletRange_Result .range.adjustIcon .irs.with-icon > .irs-bar {
  -webkit-transform: translateX(-6px);
      -ms-transform: translateX(-6px);
       -o-transform: translateX(-6px);
          transform: translateX(-6px);
}
.wizlet.wizletRange .gauge.total.card-panel,
.wizlet.wizletRange_Result .gauge.total.card-panel {
  padding: 8px 12px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-left: 12px;
}
.wizlet.wizletRange .gauge.total.card-panel.position-up,
.wizlet.wizletRange_Result .gauge.total.card-panel.position-up {
  position: absolute;
  right: 5px;
}
.wizlet.wizletRange .gauge.total.card-panel .gauge-label,
.wizlet.wizletRange_Result .gauge.total.card-panel .gauge-label {
  margin-right: 5px;
}
.wizlet.wizletRange .gauge.total.card-panel .gauge-suffix,
.wizlet.wizletRange_Result .gauge.total.card-panel .gauge-suffix {
  margin-left: 5px;
}
.wizlet.wizletRange .row.submit,
.wizlet.wizletRange_Result .row.submit {
  padding-top: 5px;
  margin-right: 0;
  text-align: right;
}
.wizlet.wizletRange .row.submit #checkBtn[hidden],
.wizlet.wizletRange_Result .row.submit #checkBtn[hidden] {
  display: inline-block !important;
  visibility: hidden;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletRange .row.submit .btn,
  .wizlet.wizletRange_Result .row.submit .btn {
    font-size: 0;
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
  .wizlet.wizletRange .row.submit .btn i,
  .wizlet.wizletRange_Result .row.submit .btn i {
    width: inherit;
    display: inline-block;
    text-align: center;
    color: #fff;
    font-size: 1.6rem;
    line-height: 40px;
  }
}