.modal-content .tab-component .wizlet.wizletLottie > .container .card {
  width: 97%;
}

.wizlet.wizletLottie .container.borders {
  border-width: 10px;
  border-color: #cc4a3d;
  border-radius: 10px;
  float: none !important;
}
.wizlet.wizletLottie .container.borders.top {
  border-top-style: double;
}
.wizlet.wizletLottie .container.borders.right {
  border-right-style: double;
}
.wizlet.wizletLottie .container.borders.left {
  border-left-style: double;
}
.wizlet.wizletLottie .container.borders.bottom {
  border-bottom-style: double;
  margin-bottom: 50px;
}
.wizlet.wizletLottie .card.no-header {
  margin-top: -20px;
  margin-bottom: 4rem;
}
.wizlet.wizletLottie .card.transparent {
  background: transparent;
  border: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.wizlet.wizletLottie .card.transparent .card-image {
  background: transparent;
}
.wizlet.wizletLottie .card.horizontal .card-content {
  width: 100%;
}
.wizlet.wizletLottie .card .card-image {
  padding: 24px 0px;
}
.wizlet.wizletLottie .card.up .card-image {
  padding-right: 24px;
}
.wizlet.wizletLottie .card.down .card-image {
  padding-left: 24px;
}
.wizlet.wizletLottie .card-image {
  min-width: 40%;
  max-width: 60%;
  display: block;
}
@media only screen and (min-width : 993px) {
  .wizlet.wizletLottie .card-image {
    min-width: 30%;
    max-width: 40%;
  }
}
.wizlet.wizletLottie .card-image.small {
  width: 40%;
}
@media only screen and (min-width : 993px) {
  .wizlet.wizletLottie .card-image.small {
    width: 30%;
  }
}
.wizlet.wizletLottie .card-image.full-width {
  max-width: 100%;
}
.wizlet.wizletLottie .card-image.borders {
  border-width: 2px;
  border-color: #cc4a3d;
  border-radius: 10px;
  float: none !important;
}
.wizlet.wizletLottie .card-image.borders.top {
  border-top-style: solid;
}
.wizlet.wizletLottie .card-image.borders.right {
  border-right-style: solid;
}
.wizlet.wizletLottie .card-image.borders.left {
  border-left-style: solid;
}
.wizlet.wizletLottie .card-image.borders.bottom {
  border-bottom-style: solid;
}
.wizlet.wizletLottie .card-image,
.wizlet.wizletLottie .card-content {
  background-color: #FFFFFF;
}
.wizlet.wizletLottie .card-content.card-content {
  background-color: #FFFFFF;
  border-radius: 10px;
}
.wizlet.wizletLottie .card-content .card-title {
  font-family: clientBolder, clientBold, Arial;
  font-size: 150%;
  line-height: 100%;
  text-align: left;
  padding-bottom: 5px;
  border-bottom: 3px solid #cc4a3d;
  margin-bottom: 15px;
}
.wizlet.wizletLottie .card-content .card-title.activator, .wizlet.wizletLottie .card-content .card-title.reveal {
  position: relative;
}
.wizlet.wizletLottie .card-content .card-title.activator i.material-icons.right, .wizlet.wizletLottie .card-content .card-title.reveal i.material-icons.right {
  position: absolute;
  top: 0;
  right: -5px;
}
.wizlet.wizletLottie .card-content p:not(.flow-text) {
  font-size: inherit;
}
@media only screen and (max-width : 992px) {
  .wizlet.wizletLottie .card-content {
    padding: 12px;
  }
}
.wizlet.wizletLottie .card-content.centered {
  text-align: center;
}
.wizlet.wizletLottie .card-content.centered span, .wizlet.wizletLottie .card-content.centered p {
  text-align: center;
}
.wizlet.wizletLottie .card-content.borders {
  border-width: 10px;
  border-color: #cc4a3d;
  border-radius: 10px;
  float: none !important;
}
.wizlet.wizletLottie .card-content.borders.top {
  border-top-style: outset;
}
.wizlet.wizletLottie .card-content.borders.right {
  border-right-style: outset;
}
.wizlet.wizletLottie .card-content.borders.left {
  border-left-style: outset;
}
.wizlet.wizletLottie .card-content.borders.bottom {
  border-bottom-style: outset;
}
.wizlet.wizletLottie .card-content.onlyImage {
  padding: 0;
}
.wizlet.wizletLottie .card-content .embed {
  clear: both;
  width: 40%;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletLottie .card-content .embed {
    width: 100%;
  }
}
.wizlet.wizletLottie .card-content .embed.left {
  float: left;
  margin-right: 12px;
}
.wizlet.wizletLottie .card-content .embed.right {
  float: right;
  margin-left: 12px;
}
.wizlet.wizletLottie .card-content .embed.xtiny {
  width: 10%;
}
.wizlet.wizletLottie .card-content .embed.tiny {
  width: 20%;
}
.wizlet.wizletLottie .card-content .embed.small {
  width: 30%;
}
.wizlet.wizletLottie .card-content .embed.large {
  width: 50%;
}
.wizlet.wizletLottie .card-content .embed.extralarge {
  width: 70%;
}
.wizlet.wizletLottie .card-content .body {
  display: table;
}