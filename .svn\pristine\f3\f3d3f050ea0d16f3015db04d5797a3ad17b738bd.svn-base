.confetti-wrapper {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.confetti-wrapper [class|=confetti] {
  position: absolute;
}
.confetti-wrapper .confetti-0 {
  width: 6px;
  height: 2.4px;
  background-color: #cddc39;
  top: -10%;
  left: 29%;
  opacity: 0.9332520261;
  -webkit-transform: rotate(150.6712992556deg);
      -ms-transform: rotate(150.6712992556deg);
       -o-transform: rotate(150.6712992556deg);
          transform: rotate(150.6712992556deg);
  -webkit-animation: drop-0 4.4157300515s 0.4989694948s infinite;
       -o-animation: drop-0 4.4157300515s 0.4989694948s infinite;
          animation: drop-0 4.4157300515s 0.4989694948s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-0 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@-o-keyframes drop-0 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@keyframes drop-0 {
  100% {
    top: 110%;
    left: 39%;
  }
}
.confetti-wrapper .confetti-1 {
  width: 8px;
  height: 3.2px;
  background-color: #03a9f4;
  top: -10%;
  left: 66%;
  opacity: 0.876891133;
  -webkit-transform: rotate(40.5986501828deg);
      -ms-transform: rotate(40.5986501828deg);
       -o-transform: rotate(40.5986501828deg);
          transform: rotate(40.5986501828deg);
  -webkit-animation: drop-1 4.5270458433s 0.2676298463s infinite;
       -o-animation: drop-1 4.5270458433s 0.2676298463s infinite;
          animation: drop-1 4.5270458433s 0.2676298463s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-1 {
  100% {
    top: 110%;
    left: 69%;
  }
}
@-o-keyframes drop-1 {
  100% {
    top: 110%;
    left: 69%;
  }
}
@keyframes drop-1 {
  100% {
    top: 110%;
    left: 69%;
  }
}
.confetti-wrapper .confetti-2 {
  width: 4px;
  height: 1.6px;
  background-color: #00bcd4;
  top: -10%;
  left: 27%;
  opacity: 1.0912642985;
  -webkit-transform: rotate(192.6558388504deg);
      -ms-transform: rotate(192.6558388504deg);
       -o-transform: rotate(192.6558388504deg);
          transform: rotate(192.6558388504deg);
  -webkit-animation: drop-2 4.066609151s 0.4934741145s infinite;
       -o-animation: drop-2 4.066609151s 0.4934741145s infinite;
          animation: drop-2 4.066609151s 0.4934741145s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-2 {
  100% {
    top: 110%;
    left: 34%;
  }
}
@-o-keyframes drop-2 {
  100% {
    top: 110%;
    left: 34%;
  }
}
@keyframes drop-2 {
  100% {
    top: 110%;
    left: 34%;
  }
}
.confetti-wrapper .confetti-3 {
  width: 2px;
  height: 0.8px;
  background-color: #03a9f4;
  top: -10%;
  left: 10%;
  opacity: 0.6887483407;
  -webkit-transform: rotate(194.0003662217deg);
      -ms-transform: rotate(194.0003662217deg);
       -o-transform: rotate(194.0003662217deg);
          transform: rotate(194.0003662217deg);
  -webkit-animation: drop-3 4.0421071888s 0.2898478252s infinite;
       -o-animation: drop-3 4.0421071888s 0.2898478252s infinite;
          animation: drop-3 4.0421071888s 0.2898478252s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-3 {
  100% {
    top: 110%;
    left: 16%;
  }
}
@-o-keyframes drop-3 {
  100% {
    top: 110%;
    left: 16%;
  }
}
@keyframes drop-3 {
  100% {
    top: 110%;
    left: 16%;
  }
}
.confetti-wrapper .confetti-4 {
  width: 7px;
  height: 2.8px;
  background-color: #3f51b5;
  top: -10%;
  left: 48%;
  opacity: 0.6290667489;
  -webkit-transform: rotate(152.7165320832deg);
      -ms-transform: rotate(152.7165320832deg);
       -o-transform: rotate(152.7165320832deg);
          transform: rotate(152.7165320832deg);
  -webkit-animation: drop-4 4.4021104776s 0.0259265959s infinite;
       -o-animation: drop-4 4.4021104776s 0.0259265959s infinite;
          animation: drop-4 4.4021104776s 0.0259265959s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-4 {
  100% {
    top: 110%;
    left: 50%;
  }
}
@-o-keyframes drop-4 {
  100% {
    top: 110%;
    left: 50%;
  }
}
@keyframes drop-4 {
  100% {
    top: 110%;
    left: 50%;
  }
}
.confetti-wrapper .confetti-5 {
  width: 10px;
  height: 4px;
  background-color: #e91e63;
  top: -10%;
  left: -4%;
  opacity: 1.3893224063;
  -webkit-transform: rotate(347.5799154921deg);
      -ms-transform: rotate(347.5799154921deg);
       -o-transform: rotate(347.5799154921deg);
          transform: rotate(347.5799154921deg);
  -webkit-animation: drop-5 4.6277407055s 0.9806785195s infinite;
       -o-animation: drop-5 4.6277407055s 0.9806785195s infinite;
          animation: drop-5 4.6277407055s 0.9806785195s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-5 {
  100% {
    top: 110%;
    left: 0%;
  }
}
@-o-keyframes drop-5 {
  100% {
    top: 110%;
    left: 0%;
  }
}
@keyframes drop-5 {
  100% {
    top: 110%;
    left: 0%;
  }
}
.confetti-wrapper .confetti-6 {
  width: 9px;
  height: 3.6px;
  background-color: #cddc39;
  top: -10%;
  left: 26%;
  opacity: 1.2337314255;
  -webkit-transform: rotate(116.9715753899deg);
      -ms-transform: rotate(116.9715753899deg);
       -o-transform: rotate(116.9715753899deg);
          transform: rotate(116.9715753899deg);
  -webkit-animation: drop-6 4.8959437672s 0.1692111032s infinite;
       -o-animation: drop-6 4.8959437672s 0.1692111032s infinite;
          animation: drop-6 4.8959437672s 0.1692111032s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-6 {
  100% {
    top: 110%;
    left: 37%;
  }
}
@-o-keyframes drop-6 {
  100% {
    top: 110%;
    left: 37%;
  }
}
@keyframes drop-6 {
  100% {
    top: 110%;
    left: 37%;
  }
}
.confetti-wrapper .confetti-7 {
  width: 5px;
  height: 2px;
  background-color: #3f51b5;
  top: -10%;
  left: 99%;
  opacity: 0.5384024692;
  -webkit-transform: rotate(150.473306637deg);
      -ms-transform: rotate(150.473306637deg);
       -o-transform: rotate(150.473306637deg);
          transform: rotate(150.473306637deg);
  -webkit-animation: drop-7 4.0324636002s 0.7362209406s infinite;
       -o-animation: drop-7 4.0324636002s 0.7362209406s infinite;
          animation: drop-7 4.0324636002s 0.7362209406s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-7 {
  100% {
    top: 110%;
    left: 108%;
  }
}
@-o-keyframes drop-7 {
  100% {
    top: 110%;
    left: 108%;
  }
}
@keyframes drop-7 {
  100% {
    top: 110%;
    left: 108%;
  }
}
.confetti-wrapper .confetti-8 {
  width: 6px;
  height: 2.4px;
  background-color: #673ab7;
  top: -10%;
  left: 84%;
  opacity: 0.5102906736;
  -webkit-transform: rotate(243.7064371253deg);
      -ms-transform: rotate(243.7064371253deg);
       -o-transform: rotate(243.7064371253deg);
          transform: rotate(243.7064371253deg);
  -webkit-animation: drop-8 4.8440551124s 0.1732219872s infinite;
       -o-animation: drop-8 4.8440551124s 0.1732219872s infinite;
          animation: drop-8 4.8440551124s 0.1732219872s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-8 {
  100% {
    top: 110%;
    left: 89%;
  }
}
@-o-keyframes drop-8 {
  100% {
    top: 110%;
    left: 89%;
  }
}
@keyframes drop-8 {
  100% {
    top: 110%;
    left: 89%;
  }
}
.confetti-wrapper .confetti-9 {
  width: 2px;
  height: 0.8px;
  background-color: #e91e63;
  top: -10%;
  left: 49%;
  opacity: 1.0719446961;
  -webkit-transform: rotate(70.9074668435deg);
      -ms-transform: rotate(70.9074668435deg);
       -o-transform: rotate(70.9074668435deg);
          transform: rotate(70.9074668435deg);
  -webkit-animation: drop-9 4.3621373161s 0.3752083847s infinite;
       -o-animation: drop-9 4.3621373161s 0.3752083847s infinite;
          animation: drop-9 4.3621373161s 0.3752083847s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-9 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@-o-keyframes drop-9 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@keyframes drop-9 {
  100% {
    top: 110%;
    left: 64%;
  }
}
.confetti-wrapper .confetti-10 {
  width: 2px;
  height: 0.8px;
  background-color: #03a9f4;
  top: -10%;
  left: 14%;
  opacity: 1.3673382786;
  -webkit-transform: rotate(4.3114779931deg);
      -ms-transform: rotate(4.3114779931deg);
       -o-transform: rotate(4.3114779931deg);
          transform: rotate(4.3114779931deg);
  -webkit-animation: drop-10 4.8707493562s 0.880320458s infinite;
       -o-animation: drop-10 4.8707493562s 0.880320458s infinite;
          animation: drop-10 4.8707493562s 0.880320458s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-10 {
  100% {
    top: 110%;
    left: 20%;
  }
}
@-o-keyframes drop-10 {
  100% {
    top: 110%;
    left: 20%;
  }
}
@keyframes drop-10 {
  100% {
    top: 110%;
    left: 20%;
  }
}
.confetti-wrapper .confetti-11 {
  width: 2px;
  height: 0.8px;
  background-color: #009688;
  top: -10%;
  left: 4%;
  opacity: 1.1929150619;
  -webkit-transform: rotate(249.649093802deg);
      -ms-transform: rotate(249.649093802deg);
       -o-transform: rotate(249.649093802deg);
          transform: rotate(249.649093802deg);
  -webkit-animation: drop-11 4.7027737736s 0.7827050435s infinite;
       -o-animation: drop-11 4.7027737736s 0.7827050435s infinite;
          animation: drop-11 4.7027737736s 0.7827050435s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-11 {
  100% {
    top: 110%;
    left: 12%;
  }
}
@-o-keyframes drop-11 {
  100% {
    top: 110%;
    left: 12%;
  }
}
@keyframes drop-11 {
  100% {
    top: 110%;
    left: 12%;
  }
}
.confetti-wrapper .confetti-12 {
  width: 7px;
  height: 2.8px;
  background-color: #4CAF50;
  top: -10%;
  left: 61%;
  opacity: 1.1746887965;
  -webkit-transform: rotate(139.8545989646deg);
      -ms-transform: rotate(139.8545989646deg);
       -o-transform: rotate(139.8545989646deg);
          transform: rotate(139.8545989646deg);
  -webkit-animation: drop-12 4.0670995558s 0.2075656985s infinite;
       -o-animation: drop-12 4.0670995558s 0.2075656985s infinite;
          animation: drop-12 4.0670995558s 0.2075656985s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-12 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@-o-keyframes drop-12 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@keyframes drop-12 {
  100% {
    top: 110%;
    left: 62%;
  }
}
.confetti-wrapper .confetti-13 {
  width: 1px;
  height: 0.4px;
  background-color: #4CAF50;
  top: -10%;
  left: 47%;
  opacity: 1.3298869854;
  -webkit-transform: rotate(13.8423995395deg);
      -ms-transform: rotate(13.8423995395deg);
       -o-transform: rotate(13.8423995395deg);
          transform: rotate(13.8423995395deg);
  -webkit-animation: drop-13 4.8786357927s 0.0405632124s infinite;
       -o-animation: drop-13 4.8786357927s 0.0405632124s infinite;
          animation: drop-13 4.8786357927s 0.0405632124s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-13 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@-o-keyframes drop-13 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@keyframes drop-13 {
  100% {
    top: 110%;
    left: 53%;
  }
}
.confetti-wrapper .confetti-14 {
  width: 6px;
  height: 2.4px;
  background-color: #03a9f4;
  top: -10%;
  left: 88%;
  opacity: 1.0166355949;
  -webkit-transform: rotate(298.4517234903deg);
      -ms-transform: rotate(298.4517234903deg);
       -o-transform: rotate(298.4517234903deg);
          transform: rotate(298.4517234903deg);
  -webkit-animation: drop-14 4.8309809588s 0.8543195548s infinite;
       -o-animation: drop-14 4.8309809588s 0.8543195548s infinite;
          animation: drop-14 4.8309809588s 0.8543195548s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-14 {
  100% {
    top: 110%;
    left: 92%;
  }
}
@-o-keyframes drop-14 {
  100% {
    top: 110%;
    left: 92%;
  }
}
@keyframes drop-14 {
  100% {
    top: 110%;
    left: 92%;
  }
}
.confetti-wrapper .confetti-15 {
  width: 9px;
  height: 3.6px;
  background-color: #8bc34a;
  top: -10%;
  left: 69%;
  opacity: 0.7951168584;
  -webkit-transform: rotate(333.394942174deg);
      -ms-transform: rotate(333.394942174deg);
       -o-transform: rotate(333.394942174deg);
          transform: rotate(333.394942174deg);
  -webkit-animation: drop-15 4.5588338569s 0.2202537481s infinite;
       -o-animation: drop-15 4.5588338569s 0.2202537481s infinite;
          animation: drop-15 4.5588338569s 0.2202537481s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-15 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@-o-keyframes drop-15 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@keyframes drop-15 {
  100% {
    top: 110%;
    left: 83%;
  }
}
.confetti-wrapper .confetti-16 {
  width: 9px;
  height: 3.6px;
  background-color: #cddc39;
  top: -10%;
  left: 40%;
  opacity: 1.1483209558;
  -webkit-transform: rotate(234.9980178558deg);
      -ms-transform: rotate(234.9980178558deg);
       -o-transform: rotate(234.9980178558deg);
          transform: rotate(234.9980178558deg);
  -webkit-animation: drop-16 4.8329186253s 0.0147710377s infinite;
       -o-animation: drop-16 4.8329186253s 0.0147710377s infinite;
          animation: drop-16 4.8329186253s 0.0147710377s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-16 {
  100% {
    top: 110%;
    left: 54%;
  }
}
@-o-keyframes drop-16 {
  100% {
    top: 110%;
    left: 54%;
  }
}
@keyframes drop-16 {
  100% {
    top: 110%;
    left: 54%;
  }
}
.confetti-wrapper .confetti-17 {
  width: 9px;
  height: 3.6px;
  background-color: #e91e63;
  top: -10%;
  left: 67%;
  opacity: 0.8523591006;
  -webkit-transform: rotate(157.9141418775deg);
      -ms-transform: rotate(157.9141418775deg);
       -o-transform: rotate(157.9141418775deg);
          transform: rotate(157.9141418775deg);
  -webkit-animation: drop-17 4.6820076131s 0.1782469691s infinite;
       -o-animation: drop-17 4.6820076131s 0.1782469691s infinite;
          animation: drop-17 4.6820076131s 0.1782469691s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-17 {
  100% {
    top: 110%;
    left: 79%;
  }
}
@-o-keyframes drop-17 {
  100% {
    top: 110%;
    left: 79%;
  }
}
@keyframes drop-17 {
  100% {
    top: 110%;
    left: 79%;
  }
}
.confetti-wrapper .confetti-18 {
  width: 5px;
  height: 2px;
  background-color: #9c27b0;
  top: -10%;
  left: 11%;
  opacity: 0.973600513;
  -webkit-transform: rotate(152.4301161977deg);
      -ms-transform: rotate(152.4301161977deg);
       -o-transform: rotate(152.4301161977deg);
          transform: rotate(152.4301161977deg);
  -webkit-animation: drop-18 4.5305193153s 0.9211827518s infinite;
       -o-animation: drop-18 4.5305193153s 0.9211827518s infinite;
          animation: drop-18 4.5305193153s 0.9211827518s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-18 {
  100% {
    top: 110%;
    left: 21%;
  }
}
@-o-keyframes drop-18 {
  100% {
    top: 110%;
    left: 21%;
  }
}
@keyframes drop-18 {
  100% {
    top: 110%;
    left: 21%;
  }
}
.confetti-wrapper .confetti-19 {
  width: 4px;
  height: 1.6px;
  background-color: #ff5722;
  top: -10%;
  left: 16%;
  opacity: 0.5190976831;
  -webkit-transform: rotate(200.2712534568deg);
      -ms-transform: rotate(200.2712534568deg);
       -o-transform: rotate(200.2712534568deg);
          transform: rotate(200.2712534568deg);
  -webkit-animation: drop-19 4.9924762792s 0.3605190721s infinite;
       -o-animation: drop-19 4.9924762792s 0.3605190721s infinite;
          animation: drop-19 4.9924762792s 0.3605190721s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-19 {
  100% {
    top: 110%;
    left: 30%;
  }
}
@-o-keyframes drop-19 {
  100% {
    top: 110%;
    left: 30%;
  }
}
@keyframes drop-19 {
  100% {
    top: 110%;
    left: 30%;
  }
}
.confetti-wrapper .confetti-20 {
  width: 8px;
  height: 3.2px;
  background-color: #8bc34a;
  top: -10%;
  left: 56%;
  opacity: 0.7623601091;
  -webkit-transform: rotate(355.0934066934deg);
      -ms-transform: rotate(355.0934066934deg);
       -o-transform: rotate(355.0934066934deg);
          transform: rotate(355.0934066934deg);
  -webkit-animation: drop-20 4.5032546816s 0.1599929727s infinite;
       -o-animation: drop-20 4.5032546816s 0.1599929727s infinite;
          animation: drop-20 4.5032546816s 0.1599929727s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-20 {
  100% {
    top: 110%;
    left: 57%;
  }
}
@-o-keyframes drop-20 {
  100% {
    top: 110%;
    left: 57%;
  }
}
@keyframes drop-20 {
  100% {
    top: 110%;
    left: 57%;
  }
}
.confetti-wrapper .confetti-21 {
  width: 4px;
  height: 1.6px;
  background-color: #4CAF50;
  top: -10%;
  left: 55%;
  opacity: 1.4441076077;
  -webkit-transform: rotate(312.7503620369deg);
      -ms-transform: rotate(312.7503620369deg);
       -o-transform: rotate(312.7503620369deg);
          transform: rotate(312.7503620369deg);
  -webkit-animation: drop-21 4.4458739003s 0.0656888171s infinite;
       -o-animation: drop-21 4.4458739003s 0.0656888171s infinite;
          animation: drop-21 4.4458739003s 0.0656888171s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-21 {
  100% {
    top: 110%;
    left: 70%;
  }
}
@-o-keyframes drop-21 {
  100% {
    top: 110%;
    left: 70%;
  }
}
@keyframes drop-21 {
  100% {
    top: 110%;
    left: 70%;
  }
}
.confetti-wrapper .confetti-22 {
  width: 9px;
  height: 3.6px;
  background-color: #ffeb3b;
  top: -10%;
  left: 13%;
  opacity: 0.8715655655;
  -webkit-transform: rotate(351.5107913521deg);
      -ms-transform: rotate(351.5107913521deg);
       -o-transform: rotate(351.5107913521deg);
          transform: rotate(351.5107913521deg);
  -webkit-animation: drop-22 4.9503356248s 0.5322803915s infinite;
       -o-animation: drop-22 4.9503356248s 0.5322803915s infinite;
          animation: drop-22 4.9503356248s 0.5322803915s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-22 {
  100% {
    top: 110%;
    left: 18%;
  }
}
@-o-keyframes drop-22 {
  100% {
    top: 110%;
    left: 18%;
  }
}
@keyframes drop-22 {
  100% {
    top: 110%;
    left: 18%;
  }
}
.confetti-wrapper .confetti-23 {
  width: 4px;
  height: 1.6px;
  background-color: #03a9f4;
  top: -10%;
  left: 53%;
  opacity: 1.264026915;
  -webkit-transform: rotate(66.390111023deg);
      -ms-transform: rotate(66.390111023deg);
       -o-transform: rotate(66.390111023deg);
          transform: rotate(66.390111023deg);
  -webkit-animation: drop-23 4.4843610038s 0.6914720413s infinite;
       -o-animation: drop-23 4.4843610038s 0.6914720413s infinite;
          animation: drop-23 4.4843610038s 0.6914720413s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-23 {
  100% {
    top: 110%;
    left: 66%;
  }
}
@-o-keyframes drop-23 {
  100% {
    top: 110%;
    left: 66%;
  }
}
@keyframes drop-23 {
  100% {
    top: 110%;
    left: 66%;
  }
}
.confetti-wrapper .confetti-24 {
  width: 3px;
  height: 1.2px;
  background-color: #9c27b0;
  top: -10%;
  left: 13%;
  opacity: 1.2571447987;
  -webkit-transform: rotate(14.7855324738deg);
      -ms-transform: rotate(14.7855324738deg);
       -o-transform: rotate(14.7855324738deg);
          transform: rotate(14.7855324738deg);
  -webkit-animation: drop-24 4.0211407907s 0.2222538251s infinite;
       -o-animation: drop-24 4.0211407907s 0.2222538251s infinite;
          animation: drop-24 4.0211407907s 0.2222538251s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-24 {
  100% {
    top: 110%;
    left: 15%;
  }
}
@-o-keyframes drop-24 {
  100% {
    top: 110%;
    left: 15%;
  }
}
@keyframes drop-24 {
  100% {
    top: 110%;
    left: 15%;
  }
}
.confetti-wrapper .confetti-25 {
  width: 9px;
  height: 3.6px;
  background-color: #673ab7;
  top: -10%;
  left: -1%;
  opacity: 1.2078685699;
  -webkit-transform: rotate(245.9335956451deg);
      -ms-transform: rotate(245.9335956451deg);
       -o-transform: rotate(245.9335956451deg);
          transform: rotate(245.9335956451deg);
  -webkit-animation: drop-25 4.5629890617s 0.5732568111s infinite;
       -o-animation: drop-25 4.5629890617s 0.5732568111s infinite;
          animation: drop-25 4.5629890617s 0.5732568111s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-25 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@-o-keyframes drop-25 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@keyframes drop-25 {
  100% {
    top: 110%;
    left: 9%;
  }
}
.confetti-wrapper .confetti-26 {
  width: 7px;
  height: 2.8px;
  background-color: #673ab7;
  top: -10%;
  left: 33%;
  opacity: 0.8371413874;
  -webkit-transform: rotate(341.6546606731deg);
      -ms-transform: rotate(341.6546606731deg);
       -o-transform: rotate(341.6546606731deg);
          transform: rotate(341.6546606731deg);
  -webkit-animation: drop-26 4.0677816523s 0.326013409s infinite;
       -o-animation: drop-26 4.0677816523s 0.326013409s infinite;
          animation: drop-26 4.0677816523s 0.326013409s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-26 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@-o-keyframes drop-26 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@keyframes drop-26 {
  100% {
    top: 110%;
    left: 43%;
  }
}
.confetti-wrapper .confetti-27 {
  width: 4px;
  height: 1.6px;
  background-color: #ff5722;
  top: -10%;
  left: 70%;
  opacity: 1.087217035;
  -webkit-transform: rotate(39.5244096372deg);
      -ms-transform: rotate(39.5244096372deg);
       -o-transform: rotate(39.5244096372deg);
          transform: rotate(39.5244096372deg);
  -webkit-animation: drop-27 4.7635001285s 0.4354810343s infinite;
       -o-animation: drop-27 4.7635001285s 0.4354810343s infinite;
          animation: drop-27 4.7635001285s 0.4354810343s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-27 {
  100% {
    top: 110%;
    left: 71%;
  }
}
@-o-keyframes drop-27 {
  100% {
    top: 110%;
    left: 71%;
  }
}
@keyframes drop-27 {
  100% {
    top: 110%;
    left: 71%;
  }
}
.confetti-wrapper .confetti-28 {
  width: 3px;
  height: 1.2px;
  background-color: #9c27b0;
  top: -10%;
  left: 39%;
  opacity: 0.9214374869;
  -webkit-transform: rotate(339.0361851132deg);
      -ms-transform: rotate(339.0361851132deg);
       -o-transform: rotate(339.0361851132deg);
          transform: rotate(339.0361851132deg);
  -webkit-animation: drop-28 4.2764438224s 0.6411094758s infinite;
       -o-animation: drop-28 4.2764438224s 0.6411094758s infinite;
          animation: drop-28 4.2764438224s 0.6411094758s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-28 {
  100% {
    top: 110%;
    left: 46%;
  }
}
@-o-keyframes drop-28 {
  100% {
    top: 110%;
    left: 46%;
  }
}
@keyframes drop-28 {
  100% {
    top: 110%;
    left: 46%;
  }
}
.confetti-wrapper .confetti-29 {
  width: 6px;
  height: 2.4px;
  background-color: #4CAF50;
  top: -10%;
  left: 76%;
  opacity: 0.8793796384;
  -webkit-transform: rotate(122.3964341824deg);
      -ms-transform: rotate(122.3964341824deg);
       -o-transform: rotate(122.3964341824deg);
          transform: rotate(122.3964341824deg);
  -webkit-animation: drop-29 4.1413376416s 0.1281812582s infinite;
       -o-animation: drop-29 4.1413376416s 0.1281812582s infinite;
          animation: drop-29 4.1413376416s 0.1281812582s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-29 {
  100% {
    top: 110%;
    left: 79%;
  }
}
@-o-keyframes drop-29 {
  100% {
    top: 110%;
    left: 79%;
  }
}
@keyframes drop-29 {
  100% {
    top: 110%;
    left: 79%;
  }
}
.confetti-wrapper .confetti-30 {
  width: 9px;
  height: 3.6px;
  background-color: #9c27b0;
  top: -10%;
  left: -5%;
  opacity: 1.4260966261;
  -webkit-transform: rotate(132.7710448027deg);
      -ms-transform: rotate(132.7710448027deg);
       -o-transform: rotate(132.7710448027deg);
          transform: rotate(132.7710448027deg);
  -webkit-animation: drop-30 4.6543662646s 0.4124561029s infinite;
       -o-animation: drop-30 4.6543662646s 0.4124561029s infinite;
          animation: drop-30 4.6543662646s 0.4124561029s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-30 {
  100% {
    top: 110%;
    left: 5%;
  }
}
@-o-keyframes drop-30 {
  100% {
    top: 110%;
    left: 5%;
  }
}
@keyframes drop-30 {
  100% {
    top: 110%;
    left: 5%;
  }
}
.confetti-wrapper .confetti-31 {
  width: 10px;
  height: 4px;
  background-color: #cddc39;
  top: -10%;
  left: 13%;
  opacity: 0.6632241416;
  -webkit-transform: rotate(42.7880361345deg);
      -ms-transform: rotate(42.7880361345deg);
       -o-transform: rotate(42.7880361345deg);
          transform: rotate(42.7880361345deg);
  -webkit-animation: drop-31 4.241997366s 0.2831000592s infinite;
       -o-animation: drop-31 4.241997366s 0.2831000592s infinite;
          animation: drop-31 4.241997366s 0.2831000592s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-31 {
  100% {
    top: 110%;
    left: 15%;
  }
}
@-o-keyframes drop-31 {
  100% {
    top: 110%;
    left: 15%;
  }
}
@keyframes drop-31 {
  100% {
    top: 110%;
    left: 15%;
  }
}
.confetti-wrapper .confetti-32 {
  width: 8px;
  height: 3.2px;
  background-color: #9c27b0;
  top: -10%;
  left: 61%;
  opacity: 0.5179798687;
  -webkit-transform: rotate(284.4529005575deg);
      -ms-transform: rotate(284.4529005575deg);
       -o-transform: rotate(284.4529005575deg);
          transform: rotate(284.4529005575deg);
  -webkit-animation: drop-32 4.1511486659s 0.3839774796s infinite;
       -o-animation: drop-32 4.1511486659s 0.3839774796s infinite;
          animation: drop-32 4.1511486659s 0.3839774796s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-32 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@-o-keyframes drop-32 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@keyframes drop-32 {
  100% {
    top: 110%;
    left: 76%;
  }
}
.confetti-wrapper .confetti-33 {
  width: 1px;
  height: 0.4px;
  background-color: #cddc39;
  top: -10%;
  left: 2%;
  opacity: 0.7787110451;
  -webkit-transform: rotate(283.6911204347deg);
      -ms-transform: rotate(283.6911204347deg);
       -o-transform: rotate(283.6911204347deg);
          transform: rotate(283.6911204347deg);
  -webkit-animation: drop-33 4.9799968544s 0.0872417861s infinite;
       -o-animation: drop-33 4.9799968544s 0.0872417861s infinite;
          animation: drop-33 4.9799968544s 0.0872417861s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-33 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@-o-keyframes drop-33 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@keyframes drop-33 {
  100% {
    top: 110%;
    left: 10%;
  }
}
.confetti-wrapper .confetti-34 {
  width: 1px;
  height: 0.4px;
  background-color: #673ab7;
  top: -10%;
  left: 100%;
  opacity: 1.2922100964;
  -webkit-transform: rotate(98.6094932236deg);
      -ms-transform: rotate(98.6094932236deg);
       -o-transform: rotate(98.6094932236deg);
          transform: rotate(98.6094932236deg);
  -webkit-animation: drop-34 4.7579131494s 0.5247267495s infinite;
       -o-animation: drop-34 4.7579131494s 0.5247267495s infinite;
          animation: drop-34 4.7579131494s 0.5247267495s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-34 {
  100% {
    top: 110%;
    left: 101%;
  }
}
@-o-keyframes drop-34 {
  100% {
    top: 110%;
    left: 101%;
  }
}
@keyframes drop-34 {
  100% {
    top: 110%;
    left: 101%;
  }
}
.confetti-wrapper .confetti-35 {
  width: 6px;
  height: 2.4px;
  background-color: #ff5722;
  top: -10%;
  left: 50%;
  opacity: 1.0347180308;
  -webkit-transform: rotate(329.962413792deg);
      -ms-transform: rotate(329.962413792deg);
       -o-transform: rotate(329.962413792deg);
          transform: rotate(329.962413792deg);
  -webkit-animation: drop-35 4.9220368333s 0.0186153662s infinite;
       -o-animation: drop-35 4.9220368333s 0.0186153662s infinite;
          animation: drop-35 4.9220368333s 0.0186153662s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-35 {
  100% {
    top: 110%;
    left: 58%;
  }
}
@-o-keyframes drop-35 {
  100% {
    top: 110%;
    left: 58%;
  }
}
@keyframes drop-35 {
  100% {
    top: 110%;
    left: 58%;
  }
}
.confetti-wrapper .confetti-36 {
  width: 1px;
  height: 0.4px;
  background-color: #cddc39;
  top: -10%;
  left: 68%;
  opacity: 0.9231592937;
  -webkit-transform: rotate(307.603201747deg);
      -ms-transform: rotate(307.603201747deg);
       -o-transform: rotate(307.603201747deg);
          transform: rotate(307.603201747deg);
  -webkit-animation: drop-36 4.4264081548s 0.4314307989s infinite;
       -o-animation: drop-36 4.4264081548s 0.4314307989s infinite;
          animation: drop-36 4.4264081548s 0.4314307989s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-36 {
  100% {
    top: 110%;
    left: 77%;
  }
}
@-o-keyframes drop-36 {
  100% {
    top: 110%;
    left: 77%;
  }
}
@keyframes drop-36 {
  100% {
    top: 110%;
    left: 77%;
  }
}
.confetti-wrapper .confetti-37 {
  width: 10px;
  height: 4px;
  background-color: #F44336;
  top: -10%;
  left: 56%;
  opacity: 1.2235487561;
  -webkit-transform: rotate(24.3240149332deg);
      -ms-transform: rotate(24.3240149332deg);
       -o-transform: rotate(24.3240149332deg);
          transform: rotate(24.3240149332deg);
  -webkit-animation: drop-37 4.9842077883s 0.6699263161s infinite;
       -o-animation: drop-37 4.9842077883s 0.6699263161s infinite;
          animation: drop-37 4.9842077883s 0.6699263161s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-37 {
  100% {
    top: 110%;
    left: 61%;
  }
}
@-o-keyframes drop-37 {
  100% {
    top: 110%;
    left: 61%;
  }
}
@keyframes drop-37 {
  100% {
    top: 110%;
    left: 61%;
  }
}
.confetti-wrapper .confetti-38 {
  width: 7px;
  height: 2.8px;
  background-color: #3f51b5;
  top: -10%;
  left: 35%;
  opacity: 0.605601087;
  -webkit-transform: rotate(329.4057253305deg);
      -ms-transform: rotate(329.4057253305deg);
       -o-transform: rotate(329.4057253305deg);
          transform: rotate(329.4057253305deg);
  -webkit-animation: drop-38 4.6740916211s 0.8642836343s infinite;
       -o-animation: drop-38 4.6740916211s 0.8642836343s infinite;
          animation: drop-38 4.6740916211s 0.8642836343s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-38 {
  100% {
    top: 110%;
    left: 48%;
  }
}
@-o-keyframes drop-38 {
  100% {
    top: 110%;
    left: 48%;
  }
}
@keyframes drop-38 {
  100% {
    top: 110%;
    left: 48%;
  }
}
.confetti-wrapper .confetti-39 {
  width: 4px;
  height: 1.6px;
  background-color: #673ab7;
  top: -10%;
  left: 81%;
  opacity: 0.7475742205;
  -webkit-transform: rotate(226.3743483739deg);
      -ms-transform: rotate(226.3743483739deg);
       -o-transform: rotate(226.3743483739deg);
          transform: rotate(226.3743483739deg);
  -webkit-animation: drop-39 4.5832288316s 0.9768252148s infinite;
       -o-animation: drop-39 4.5832288316s 0.9768252148s infinite;
          animation: drop-39 4.5832288316s 0.9768252148s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-39 {
  100% {
    top: 110%;
    left: 85%;
  }
}
@-o-keyframes drop-39 {
  100% {
    top: 110%;
    left: 85%;
  }
}
@keyframes drop-39 {
  100% {
    top: 110%;
    left: 85%;
  }
}
.confetti-wrapper .confetti-40 {
  width: 1px;
  height: 0.4px;
  background-color: #F44336;
  top: -10%;
  left: 42%;
  opacity: 0.9769724327;
  -webkit-transform: rotate(343.6378177923deg);
      -ms-transform: rotate(343.6378177923deg);
       -o-transform: rotate(343.6378177923deg);
          transform: rotate(343.6378177923deg);
  -webkit-animation: drop-40 4.4769847339s 0.1416481333s infinite;
       -o-animation: drop-40 4.4769847339s 0.1416481333s infinite;
          animation: drop-40 4.4769847339s 0.1416481333s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-40 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@-o-keyframes drop-40 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@keyframes drop-40 {
  100% {
    top: 110%;
    left: 43%;
  }
}
.confetti-wrapper .confetti-41 {
  width: 8px;
  height: 3.2px;
  background-color: #3f51b5;
  top: -10%;
  left: 60%;
  opacity: 1.1156147974;
  -webkit-transform: rotate(87.7373271513deg);
      -ms-transform: rotate(87.7373271513deg);
       -o-transform: rotate(87.7373271513deg);
          transform: rotate(87.7373271513deg);
  -webkit-animation: drop-41 4.5469458327s 0.8636305851s infinite;
       -o-animation: drop-41 4.5469458327s 0.8636305851s infinite;
          animation: drop-41 4.5469458327s 0.8636305851s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-41 {
  100% {
    top: 110%;
    left: 63%;
  }
}
@-o-keyframes drop-41 {
  100% {
    top: 110%;
    left: 63%;
  }
}
@keyframes drop-41 {
  100% {
    top: 110%;
    left: 63%;
  }
}
.confetti-wrapper .confetti-42 {
  width: 9px;
  height: 3.6px;
  background-color: #9c27b0;
  top: -10%;
  left: 2%;
  opacity: 0.6510433819;
  -webkit-transform: rotate(41.946620409deg);
      -ms-transform: rotate(41.946620409deg);
       -o-transform: rotate(41.946620409deg);
          transform: rotate(41.946620409deg);
  -webkit-animation: drop-42 4.6063810141s 0.4881514283s infinite;
       -o-animation: drop-42 4.6063810141s 0.4881514283s infinite;
          animation: drop-42 4.6063810141s 0.4881514283s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-42 {
  100% {
    top: 110%;
    left: 4%;
  }
}
@-o-keyframes drop-42 {
  100% {
    top: 110%;
    left: 4%;
  }
}
@keyframes drop-42 {
  100% {
    top: 110%;
    left: 4%;
  }
}
.confetti-wrapper .confetti-43 {
  width: 10px;
  height: 4px;
  background-color: #009688;
  top: -10%;
  left: -8%;
  opacity: 1.4868300172;
  -webkit-transform: rotate(174.8938902389deg);
      -ms-transform: rotate(174.8938902389deg);
       -o-transform: rotate(174.8938902389deg);
          transform: rotate(174.8938902389deg);
  -webkit-animation: drop-43 4.0605182124s 0.639731266s infinite;
       -o-animation: drop-43 4.0605182124s 0.639731266s infinite;
          animation: drop-43 4.0605182124s 0.639731266s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-43 {
  100% {
    top: 110%;
    left: -2%;
  }
}
@-o-keyframes drop-43 {
  100% {
    top: 110%;
    left: -2%;
  }
}
@keyframes drop-43 {
  100% {
    top: 110%;
    left: -2%;
  }
}
.confetti-wrapper .confetti-44 {
  width: 7px;
  height: 2.8px;
  background-color: #ff9800;
  top: -10%;
  left: 92%;
  opacity: 1.1843160721;
  -webkit-transform: rotate(173.8147498003deg);
      -ms-transform: rotate(173.8147498003deg);
       -o-transform: rotate(173.8147498003deg);
          transform: rotate(173.8147498003deg);
  -webkit-animation: drop-44 4.0833662459s 0.7810552908s infinite;
       -o-animation: drop-44 4.0833662459s 0.7810552908s infinite;
          animation: drop-44 4.0833662459s 0.7810552908s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-44 {
  100% {
    top: 110%;
    left: 106%;
  }
}
@-o-keyframes drop-44 {
  100% {
    top: 110%;
    left: 106%;
  }
}
@keyframes drop-44 {
  100% {
    top: 110%;
    left: 106%;
  }
}
.confetti-wrapper .confetti-45 {
  width: 1px;
  height: 0.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 63%;
  opacity: 1.291872834;
  -webkit-transform: rotate(155.4085170512deg);
      -ms-transform: rotate(155.4085170512deg);
       -o-transform: rotate(155.4085170512deg);
          transform: rotate(155.4085170512deg);
  -webkit-animation: drop-45 4.7003531565s 0.2789798503s infinite;
       -o-animation: drop-45 4.7003531565s 0.2789798503s infinite;
          animation: drop-45 4.7003531565s 0.2789798503s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-45 {
  100% {
    top: 110%;
    left: 71%;
  }
}
@-o-keyframes drop-45 {
  100% {
    top: 110%;
    left: 71%;
  }
}
@keyframes drop-45 {
  100% {
    top: 110%;
    left: 71%;
  }
}
.confetti-wrapper .confetti-46 {
  width: 3px;
  height: 1.2px;
  background-color: #F44336;
  top: -10%;
  left: 39%;
  opacity: 1.4506156198;
  -webkit-transform: rotate(240.6389673525deg);
      -ms-transform: rotate(240.6389673525deg);
       -o-transform: rotate(240.6389673525deg);
          transform: rotate(240.6389673525deg);
  -webkit-animation: drop-46 4.6635116272s 0.6170753982s infinite;
       -o-animation: drop-46 4.6635116272s 0.6170753982s infinite;
          animation: drop-46 4.6635116272s 0.6170753982s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-46 {
  100% {
    top: 110%;
    left: 50%;
  }
}
@-o-keyframes drop-46 {
  100% {
    top: 110%;
    left: 50%;
  }
}
@keyframes drop-46 {
  100% {
    top: 110%;
    left: 50%;
  }
}
.confetti-wrapper .confetti-47 {
  width: 7px;
  height: 2.8px;
  background-color: #ffeb3b;
  top: -10%;
  left: 5%;
  opacity: 0.6943168284;
  -webkit-transform: rotate(53.5033266903deg);
      -ms-transform: rotate(53.5033266903deg);
       -o-transform: rotate(53.5033266903deg);
          transform: rotate(53.5033266903deg);
  -webkit-animation: drop-47 4.1457138299s 0.2412352853s infinite;
       -o-animation: drop-47 4.1457138299s 0.2412352853s infinite;
          animation: drop-47 4.1457138299s 0.2412352853s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-47 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@-o-keyframes drop-47 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@keyframes drop-47 {
  100% {
    top: 110%;
    left: 9%;
  }
}
.confetti-wrapper .confetti-48 {
  width: 7px;
  height: 2.8px;
  background-color: #ffc107;
  top: -10%;
  left: 57%;
  opacity: 0.7555200524;
  -webkit-transform: rotate(2.5666196909deg);
      -ms-transform: rotate(2.5666196909deg);
       -o-transform: rotate(2.5666196909deg);
          transform: rotate(2.5666196909deg);
  -webkit-animation: drop-48 4.4104156688s 0.2821262036s infinite;
       -o-animation: drop-48 4.4104156688s 0.2821262036s infinite;
          animation: drop-48 4.4104156688s 0.2821262036s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-48 {
  100% {
    top: 110%;
    left: 65%;
  }
}
@-o-keyframes drop-48 {
  100% {
    top: 110%;
    left: 65%;
  }
}
@keyframes drop-48 {
  100% {
    top: 110%;
    left: 65%;
  }
}
.confetti-wrapper .confetti-49 {
  width: 3px;
  height: 1.2px;
  background-color: #8bc34a;
  top: -10%;
  left: 64%;
  opacity: 0.9015536359;
  -webkit-transform: rotate(95.219601266deg);
      -ms-transform: rotate(95.219601266deg);
       -o-transform: rotate(95.219601266deg);
          transform: rotate(95.219601266deg);
  -webkit-animation: drop-49 4.0056935717s 0.4051975186s infinite;
       -o-animation: drop-49 4.0056935717s 0.4051975186s infinite;
          animation: drop-49 4.0056935717s 0.4051975186s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-49 {
  100% {
    top: 110%;
    left: 79%;
  }
}
@-o-keyframes drop-49 {
  100% {
    top: 110%;
    left: 79%;
  }
}
@keyframes drop-49 {
  100% {
    top: 110%;
    left: 79%;
  }
}
.confetti-wrapper .confetti-50 {
  width: 8px;
  height: 3.2px;
  background-color: #00bcd4;
  top: -10%;
  left: 29%;
  opacity: 0.8264006405;
  -webkit-transform: rotate(110.0815237842deg);
      -ms-transform: rotate(110.0815237842deg);
       -o-transform: rotate(110.0815237842deg);
          transform: rotate(110.0815237842deg);
  -webkit-animation: drop-50 4.9318255127s 0.3610963806s infinite;
       -o-animation: drop-50 4.9318255127s 0.3610963806s infinite;
          animation: drop-50 4.9318255127s 0.3610963806s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-50 {
  100% {
    top: 110%;
    left: 38%;
  }
}
@-o-keyframes drop-50 {
  100% {
    top: 110%;
    left: 38%;
  }
}
@keyframes drop-50 {
  100% {
    top: 110%;
    left: 38%;
  }
}
.confetti-wrapper .confetti-51 {
  width: 2px;
  height: 0.8px;
  background-color: #ffc107;
  top: -10%;
  left: -2%;
  opacity: 0.587257108;
  -webkit-transform: rotate(264.1285568211deg);
      -ms-transform: rotate(264.1285568211deg);
       -o-transform: rotate(264.1285568211deg);
          transform: rotate(264.1285568211deg);
  -webkit-animation: drop-51 4.928921491s 0.9119281468s infinite;
       -o-animation: drop-51 4.928921491s 0.9119281468s infinite;
          animation: drop-51 4.928921491s 0.9119281468s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-51 {
  100% {
    top: 110%;
    left: 12%;
  }
}
@-o-keyframes drop-51 {
  100% {
    top: 110%;
    left: 12%;
  }
}
@keyframes drop-51 {
  100% {
    top: 110%;
    left: 12%;
  }
}
.confetti-wrapper .confetti-52 {
  width: 4px;
  height: 1.6px;
  background-color: #4CAF50;
  top: -10%;
  left: 69%;
  opacity: 0.8991772352;
  -webkit-transform: rotate(4.2141984432deg);
      -ms-transform: rotate(4.2141984432deg);
       -o-transform: rotate(4.2141984432deg);
          transform: rotate(4.2141984432deg);
  -webkit-animation: drop-52 4.6676404244s 0.04167767s infinite;
       -o-animation: drop-52 4.6676404244s 0.04167767s infinite;
          animation: drop-52 4.6676404244s 0.04167767s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-52 {
  100% {
    top: 110%;
    left: 75%;
  }
}
@-o-keyframes drop-52 {
  100% {
    top: 110%;
    left: 75%;
  }
}
@keyframes drop-52 {
  100% {
    top: 110%;
    left: 75%;
  }
}
.confetti-wrapper .confetti-53 {
  width: 8px;
  height: 3.2px;
  background-color: #009688;
  top: -10%;
  left: -4%;
  opacity: 1.1873498643;
  -webkit-transform: rotate(347.7602798397deg);
      -ms-transform: rotate(347.7602798397deg);
       -o-transform: rotate(347.7602798397deg);
          transform: rotate(347.7602798397deg);
  -webkit-animation: drop-53 4.3335781162s 0.4194977029s infinite;
       -o-animation: drop-53 4.3335781162s 0.4194977029s infinite;
          animation: drop-53 4.3335781162s 0.4194977029s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-53 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@-o-keyframes drop-53 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@keyframes drop-53 {
  100% {
    top: 110%;
    left: 9%;
  }
}
.confetti-wrapper .confetti-54 {
  width: 1px;
  height: 0.4px;
  background-color: #cddc39;
  top: -10%;
  left: 30%;
  opacity: 1.2053846285;
  -webkit-transform: rotate(231.8059288717deg);
      -ms-transform: rotate(231.8059288717deg);
       -o-transform: rotate(231.8059288717deg);
          transform: rotate(231.8059288717deg);
  -webkit-animation: drop-54 4.3693536456s 0.3999596864s infinite;
       -o-animation: drop-54 4.3693536456s 0.3999596864s infinite;
          animation: drop-54 4.3693536456s 0.3999596864s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-54 {
  100% {
    top: 110%;
    left: 36%;
  }
}
@-o-keyframes drop-54 {
  100% {
    top: 110%;
    left: 36%;
  }
}
@keyframes drop-54 {
  100% {
    top: 110%;
    left: 36%;
  }
}
.confetti-wrapper .confetti-55 {
  width: 8px;
  height: 3.2px;
  background-color: #cddc39;
  top: -10%;
  left: 84%;
  opacity: 1.2801333266;
  -webkit-transform: rotate(224.5098189438deg);
      -ms-transform: rotate(224.5098189438deg);
       -o-transform: rotate(224.5098189438deg);
          transform: rotate(224.5098189438deg);
  -webkit-animation: drop-55 4.8823007018s 0.9651390954s infinite;
       -o-animation: drop-55 4.8823007018s 0.9651390954s infinite;
          animation: drop-55 4.8823007018s 0.9651390954s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-55 {
  100% {
    top: 110%;
    left: 97%;
  }
}
@-o-keyframes drop-55 {
  100% {
    top: 110%;
    left: 97%;
  }
}
@keyframes drop-55 {
  100% {
    top: 110%;
    left: 97%;
  }
}
.confetti-wrapper .confetti-56 {
  width: 3px;
  height: 1.2px;
  background-color: #ff5722;
  top: -10%;
  left: 99%;
  opacity: 0.5312816141;
  -webkit-transform: rotate(117.239168693deg);
      -ms-transform: rotate(117.239168693deg);
       -o-transform: rotate(117.239168693deg);
          transform: rotate(117.239168693deg);
  -webkit-animation: drop-56 4.5992965411s 0.4970409521s infinite;
       -o-animation: drop-56 4.5992965411s 0.4970409521s infinite;
          animation: drop-56 4.5992965411s 0.4970409521s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-56 {
  100% {
    top: 110%;
    left: 109%;
  }
}
@-o-keyframes drop-56 {
  100% {
    top: 110%;
    left: 109%;
  }
}
@keyframes drop-56 {
  100% {
    top: 110%;
    left: 109%;
  }
}
.confetti-wrapper .confetti-57 {
  width: 7px;
  height: 2.8px;
  background-color: #ffc107;
  top: -10%;
  left: 16%;
  opacity: 0.8312928011;
  -webkit-transform: rotate(89.8532591461deg);
      -ms-transform: rotate(89.8532591461deg);
       -o-transform: rotate(89.8532591461deg);
          transform: rotate(89.8532591461deg);
  -webkit-animation: drop-57 4.8009488164s 0.5647680026s infinite;
       -o-animation: drop-57 4.8009488164s 0.5647680026s infinite;
          animation: drop-57 4.8009488164s 0.5647680026s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-57 {
  100% {
    top: 110%;
    left: 28%;
  }
}
@-o-keyframes drop-57 {
  100% {
    top: 110%;
    left: 28%;
  }
}
@keyframes drop-57 {
  100% {
    top: 110%;
    left: 28%;
  }
}
.confetti-wrapper .confetti-58 {
  width: 1px;
  height: 0.4px;
  background-color: #ffc107;
  top: -10%;
  left: 21%;
  opacity: 1.3804459281;
  -webkit-transform: rotate(205.6360242387deg);
      -ms-transform: rotate(205.6360242387deg);
       -o-transform: rotate(205.6360242387deg);
          transform: rotate(205.6360242387deg);
  -webkit-animation: drop-58 4.5943958664s 0.7976587741s infinite;
       -o-animation: drop-58 4.5943958664s 0.7976587741s infinite;
          animation: drop-58 4.5943958664s 0.7976587741s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-58 {
  100% {
    top: 110%;
    left: 26%;
  }
}
@-o-keyframes drop-58 {
  100% {
    top: 110%;
    left: 26%;
  }
}
@keyframes drop-58 {
  100% {
    top: 110%;
    left: 26%;
  }
}
.confetti-wrapper .confetti-59 {
  width: 4px;
  height: 1.6px;
  background-color: #3f51b5;
  top: -10%;
  left: 26%;
  opacity: 0.6478580251;
  -webkit-transform: rotate(199.417300345deg);
      -ms-transform: rotate(199.417300345deg);
       -o-transform: rotate(199.417300345deg);
          transform: rotate(199.417300345deg);
  -webkit-animation: drop-59 4.8786048037s 0.8164866847s infinite;
       -o-animation: drop-59 4.8786048037s 0.8164866847s infinite;
          animation: drop-59 4.8786048037s 0.8164866847s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-59 {
  100% {
    top: 110%;
    left: 29%;
  }
}
@-o-keyframes drop-59 {
  100% {
    top: 110%;
    left: 29%;
  }
}
@keyframes drop-59 {
  100% {
    top: 110%;
    left: 29%;
  }
}
.confetti-wrapper .confetti-60 {
  width: 3px;
  height: 1.2px;
  background-color: #F44336;
  top: -10%;
  left: 61%;
  opacity: 0.5314886637;
  -webkit-transform: rotate(77.9602368386deg);
      -ms-transform: rotate(77.9602368386deg);
       -o-transform: rotate(77.9602368386deg);
          transform: rotate(77.9602368386deg);
  -webkit-animation: drop-60 4.7448913463s 0.7196877235s infinite;
       -o-animation: drop-60 4.7448913463s 0.7196877235s infinite;
          animation: drop-60 4.7448913463s 0.7196877235s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-60 {
  100% {
    top: 110%;
    left: 73%;
  }
}
@-o-keyframes drop-60 {
  100% {
    top: 110%;
    left: 73%;
  }
}
@keyframes drop-60 {
  100% {
    top: 110%;
    left: 73%;
  }
}
.confetti-wrapper .confetti-61 {
  width: 1px;
  height: 0.4px;
  background-color: #ff5722;
  top: -10%;
  left: 7%;
  opacity: 0.9113207233;
  -webkit-transform: rotate(303.3427449768deg);
      -ms-transform: rotate(303.3427449768deg);
       -o-transform: rotate(303.3427449768deg);
          transform: rotate(303.3427449768deg);
  -webkit-animation: drop-61 4.5312639731s 0.9317620626s infinite;
       -o-animation: drop-61 4.5312639731s 0.9317620626s infinite;
          animation: drop-61 4.5312639731s 0.9317620626s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-61 {
  100% {
    top: 110%;
    left: 13%;
  }
}
@-o-keyframes drop-61 {
  100% {
    top: 110%;
    left: 13%;
  }
}
@keyframes drop-61 {
  100% {
    top: 110%;
    left: 13%;
  }
}
.confetti-wrapper .confetti-62 {
  width: 5px;
  height: 2px;
  background-color: #8bc34a;
  top: -10%;
  left: 98%;
  opacity: 0.6746600875;
  -webkit-transform: rotate(44.7373823293deg);
      -ms-transform: rotate(44.7373823293deg);
       -o-transform: rotate(44.7373823293deg);
          transform: rotate(44.7373823293deg);
  -webkit-animation: drop-62 4.0439471268s 0.7967277613s infinite;
       -o-animation: drop-62 4.0439471268s 0.7967277613s infinite;
          animation: drop-62 4.0439471268s 0.7967277613s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-62 {
  100% {
    top: 110%;
    left: 103%;
  }
}
@-o-keyframes drop-62 {
  100% {
    top: 110%;
    left: 103%;
  }
}
@keyframes drop-62 {
  100% {
    top: 110%;
    left: 103%;
  }
}
.confetti-wrapper .confetti-63 {
  width: 6px;
  height: 2.4px;
  background-color: #4CAF50;
  top: -10%;
  left: 53%;
  opacity: 1.264715173;
  -webkit-transform: rotate(272.5905249192deg);
      -ms-transform: rotate(272.5905249192deg);
       -o-transform: rotate(272.5905249192deg);
          transform: rotate(272.5905249192deg);
  -webkit-animation: drop-63 4.4166908752s 0.2467686629s infinite;
       -o-animation: drop-63 4.4166908752s 0.2467686629s infinite;
          animation: drop-63 4.4166908752s 0.2467686629s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-63 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@-o-keyframes drop-63 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@keyframes drop-63 {
  100% {
    top: 110%;
    left: 64%;
  }
}
.confetti-wrapper .confetti-64 {
  width: 2px;
  height: 0.8px;
  background-color: #4CAF50;
  top: -10%;
  left: 0%;
  opacity: 0.7984579596;
  -webkit-transform: rotate(94.5810247976deg);
      -ms-transform: rotate(94.5810247976deg);
       -o-transform: rotate(94.5810247976deg);
          transform: rotate(94.5810247976deg);
  -webkit-animation: drop-64 4.9967417643s 0.9844706359s infinite;
       -o-animation: drop-64 4.9967417643s 0.9844706359s infinite;
          animation: drop-64 4.9967417643s 0.9844706359s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-64 {
  100% {
    top: 110%;
    left: 4%;
  }
}
@-o-keyframes drop-64 {
  100% {
    top: 110%;
    left: 4%;
  }
}
@keyframes drop-64 {
  100% {
    top: 110%;
    left: 4%;
  }
}
.confetti-wrapper .confetti-65 {
  width: 2px;
  height: 0.8px;
  background-color: #00bcd4;
  top: -10%;
  left: 99%;
  opacity: 0.9115171972;
  -webkit-transform: rotate(225.6780046526deg);
      -ms-transform: rotate(225.6780046526deg);
       -o-transform: rotate(225.6780046526deg);
          transform: rotate(225.6780046526deg);
  -webkit-animation: drop-65 4.1502956074s 0.5732139446s infinite;
       -o-animation: drop-65 4.1502956074s 0.5732139446s infinite;
          animation: drop-65 4.1502956074s 0.5732139446s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-65 {
  100% {
    top: 110%;
    left: 109%;
  }
}
@-o-keyframes drop-65 {
  100% {
    top: 110%;
    left: 109%;
  }
}
@keyframes drop-65 {
  100% {
    top: 110%;
    left: 109%;
  }
}
.confetti-wrapper .confetti-66 {
  width: 1px;
  height: 0.4px;
  background-color: #673ab7;
  top: -10%;
  left: 50%;
  opacity: 1.0016883708;
  -webkit-transform: rotate(259.2954989558deg);
      -ms-transform: rotate(259.2954989558deg);
       -o-transform: rotate(259.2954989558deg);
          transform: rotate(259.2954989558deg);
  -webkit-animation: drop-66 4.3517958382s 0.4278099015s infinite;
       -o-animation: drop-66 4.3517958382s 0.4278099015s infinite;
          animation: drop-66 4.3517958382s 0.4278099015s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-66 {
  100% {
    top: 110%;
    left: 61%;
  }
}
@-o-keyframes drop-66 {
  100% {
    top: 110%;
    left: 61%;
  }
}
@keyframes drop-66 {
  100% {
    top: 110%;
    left: 61%;
  }
}
.confetti-wrapper .confetti-67 {
  width: 3px;
  height: 1.2px;
  background-color: #673ab7;
  top: -10%;
  left: 0%;
  opacity: 1.4981026995;
  -webkit-transform: rotate(221.0395727331deg);
      -ms-transform: rotate(221.0395727331deg);
       -o-transform: rotate(221.0395727331deg);
          transform: rotate(221.0395727331deg);
  -webkit-animation: drop-67 4.2881087267s 0.9324890647s infinite;
       -o-animation: drop-67 4.2881087267s 0.9324890647s infinite;
          animation: drop-67 4.2881087267s 0.9324890647s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-67 {
  100% {
    top: 110%;
    left: 8%;
  }
}
@-o-keyframes drop-67 {
  100% {
    top: 110%;
    left: 8%;
  }
}
@keyframes drop-67 {
  100% {
    top: 110%;
    left: 8%;
  }
}
.confetti-wrapper .confetti-68 {
  width: 4px;
  height: 1.6px;
  background-color: #673ab7;
  top: -10%;
  left: 92%;
  opacity: 0.7006995535;
  -webkit-transform: rotate(176.1276402556deg);
      -ms-transform: rotate(176.1276402556deg);
       -o-transform: rotate(176.1276402556deg);
          transform: rotate(176.1276402556deg);
  -webkit-animation: drop-68 4.7766312997s 0.6711247042s infinite;
       -o-animation: drop-68 4.7766312997s 0.6711247042s infinite;
          animation: drop-68 4.7766312997s 0.6711247042s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-68 {
  100% {
    top: 110%;
    left: 106%;
  }
}
@-o-keyframes drop-68 {
  100% {
    top: 110%;
    left: 106%;
  }
}
@keyframes drop-68 {
  100% {
    top: 110%;
    left: 106%;
  }
}
.confetti-wrapper .confetti-69 {
  width: 7px;
  height: 2.8px;
  background-color: #00bcd4;
  top: -10%;
  left: 24%;
  opacity: 0.9803230446;
  -webkit-transform: rotate(59.9827009175deg);
      -ms-transform: rotate(59.9827009175deg);
       -o-transform: rotate(59.9827009175deg);
          transform: rotate(59.9827009175deg);
  -webkit-animation: drop-69 4.4317827517s 0.3097758346s infinite;
       -o-animation: drop-69 4.4317827517s 0.3097758346s infinite;
          animation: drop-69 4.4317827517s 0.3097758346s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-69 {
  100% {
    top: 110%;
    left: 26%;
  }
}
@-o-keyframes drop-69 {
  100% {
    top: 110%;
    left: 26%;
  }
}
@keyframes drop-69 {
  100% {
    top: 110%;
    left: 26%;
  }
}
.confetti-wrapper .confetti-70 {
  width: 6px;
  height: 2.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 93%;
  opacity: 1.3995108448;
  -webkit-transform: rotate(28.8466965478deg);
      -ms-transform: rotate(28.8466965478deg);
       -o-transform: rotate(28.8466965478deg);
          transform: rotate(28.8466965478deg);
  -webkit-animation: drop-70 4.0464291541s 0.939759247s infinite;
       -o-animation: drop-70 4.0464291541s 0.939759247s infinite;
          animation: drop-70 4.0464291541s 0.939759247s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-70 {
  100% {
    top: 110%;
    left: 106%;
  }
}
@-o-keyframes drop-70 {
  100% {
    top: 110%;
    left: 106%;
  }
}
@keyframes drop-70 {
  100% {
    top: 110%;
    left: 106%;
  }
}
.confetti-wrapper .confetti-71 {
  width: 3px;
  height: 1.2px;
  background-color: #009688;
  top: -10%;
  left: 96%;
  opacity: 1.1363102469;
  -webkit-transform: rotate(231.0750750966deg);
      -ms-transform: rotate(231.0750750966deg);
       -o-transform: rotate(231.0750750966deg);
          transform: rotate(231.0750750966deg);
  -webkit-animation: drop-71 4.8082915285s 0.3058293038s infinite;
       -o-animation: drop-71 4.8082915285s 0.3058293038s infinite;
          animation: drop-71 4.8082915285s 0.3058293038s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-71 {
  100% {
    top: 110%;
    left: 99%;
  }
}
@-o-keyframes drop-71 {
  100% {
    top: 110%;
    left: 99%;
  }
}
@keyframes drop-71 {
  100% {
    top: 110%;
    left: 99%;
  }
}
.confetti-wrapper .confetti-72 {
  width: 5px;
  height: 2px;
  background-color: #e91e63;
  top: -10%;
  left: 8%;
  opacity: 1.1491122376;
  -webkit-transform: rotate(237.0976256157deg);
      -ms-transform: rotate(237.0976256157deg);
       -o-transform: rotate(237.0976256157deg);
          transform: rotate(237.0976256157deg);
  -webkit-animation: drop-72 4.464373883s 0.122352094s infinite;
       -o-animation: drop-72 4.464373883s 0.122352094s infinite;
          animation: drop-72 4.464373883s 0.122352094s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-72 {
  100% {
    top: 110%;
    left: 18%;
  }
}
@-o-keyframes drop-72 {
  100% {
    top: 110%;
    left: 18%;
  }
}
@keyframes drop-72 {
  100% {
    top: 110%;
    left: 18%;
  }
}
.confetti-wrapper .confetti-73 {
  width: 9px;
  height: 3.6px;
  background-color: #009688;
  top: -10%;
  left: 39%;
  opacity: 1.3362661181;
  -webkit-transform: rotate(230.2259746345deg);
      -ms-transform: rotate(230.2259746345deg);
       -o-transform: rotate(230.2259746345deg);
          transform: rotate(230.2259746345deg);
  -webkit-animation: drop-73 4.0321500886s 0.8555273342s infinite;
       -o-animation: drop-73 4.0321500886s 0.8555273342s infinite;
          animation: drop-73 4.0321500886s 0.8555273342s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-73 {
  100% {
    top: 110%;
    left: 46%;
  }
}
@-o-keyframes drop-73 {
  100% {
    top: 110%;
    left: 46%;
  }
}
@keyframes drop-73 {
  100% {
    top: 110%;
    left: 46%;
  }
}
.confetti-wrapper .confetti-74 {
  width: 2px;
  height: 0.8px;
  background-color: #ffc107;
  top: -10%;
  left: 47%;
  opacity: 0.9584117512;
  -webkit-transform: rotate(226.0281229354deg);
      -ms-transform: rotate(226.0281229354deg);
       -o-transform: rotate(226.0281229354deg);
          transform: rotate(226.0281229354deg);
  -webkit-animation: drop-74 4.2093589159s 0.8662764173s infinite;
       -o-animation: drop-74 4.2093589159s 0.8662764173s infinite;
          animation: drop-74 4.2093589159s 0.8662764173s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-74 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@-o-keyframes drop-74 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@keyframes drop-74 {
  100% {
    top: 110%;
    left: 62%;
  }
}
.confetti-wrapper .confetti-75 {
  width: 4px;
  height: 1.6px;
  background-color: #ffc107;
  top: -10%;
  left: 81%;
  opacity: 0.7628770286;
  -webkit-transform: rotate(325.8111680935deg);
      -ms-transform: rotate(325.8111680935deg);
       -o-transform: rotate(325.8111680935deg);
          transform: rotate(325.8111680935deg);
  -webkit-animation: drop-75 4.602440148s 0.1338547914s infinite;
       -o-animation: drop-75 4.602440148s 0.1338547914s infinite;
          animation: drop-75 4.602440148s 0.1338547914s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-75 {
  100% {
    top: 110%;
    left: 96%;
  }
}
@-o-keyframes drop-75 {
  100% {
    top: 110%;
    left: 96%;
  }
}
@keyframes drop-75 {
  100% {
    top: 110%;
    left: 96%;
  }
}
.confetti-wrapper .confetti-76 {
  width: 2px;
  height: 0.8px;
  background-color: #ffc107;
  top: -10%;
  left: 11%;
  opacity: 1.0840112671;
  -webkit-transform: rotate(99.2898421987deg);
      -ms-transform: rotate(99.2898421987deg);
       -o-transform: rotate(99.2898421987deg);
          transform: rotate(99.2898421987deg);
  -webkit-animation: drop-76 4.6770471923s 0.3251131442s infinite;
       -o-animation: drop-76 4.6770471923s 0.3251131442s infinite;
          animation: drop-76 4.6770471923s 0.3251131442s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-76 {
  100% {
    top: 110%;
    left: 24%;
  }
}
@-o-keyframes drop-76 {
  100% {
    top: 110%;
    left: 24%;
  }
}
@keyframes drop-76 {
  100% {
    top: 110%;
    left: 24%;
  }
}
.confetti-wrapper .confetti-77 {
  width: 6px;
  height: 2.4px;
  background-color: #9c27b0;
  top: -10%;
  left: 26%;
  opacity: 1.2869534389;
  -webkit-transform: rotate(127.8052731147deg);
      -ms-transform: rotate(127.8052731147deg);
       -o-transform: rotate(127.8052731147deg);
          transform: rotate(127.8052731147deg);
  -webkit-animation: drop-77 4.1161341137s 0.5583784327s infinite;
       -o-animation: drop-77 4.1161341137s 0.5583784327s infinite;
          animation: drop-77 4.1161341137s 0.5583784327s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-77 {
  100% {
    top: 110%;
    left: 28%;
  }
}
@-o-keyframes drop-77 {
  100% {
    top: 110%;
    left: 28%;
  }
}
@keyframes drop-77 {
  100% {
    top: 110%;
    left: 28%;
  }
}
.confetti-wrapper .confetti-78 {
  width: 1px;
  height: 0.4px;
  background-color: #673ab7;
  top: -10%;
  left: 67%;
  opacity: 0.939660292;
  -webkit-transform: rotate(188.9319151683deg);
      -ms-transform: rotate(188.9319151683deg);
       -o-transform: rotate(188.9319151683deg);
          transform: rotate(188.9319151683deg);
  -webkit-animation: drop-78 4.3505614621s 0.3802613908s infinite;
       -o-animation: drop-78 4.3505614621s 0.3802613908s infinite;
          animation: drop-78 4.3505614621s 0.3802613908s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-78 {
  100% {
    top: 110%;
    left: 79%;
  }
}
@-o-keyframes drop-78 {
  100% {
    top: 110%;
    left: 79%;
  }
}
@keyframes drop-78 {
  100% {
    top: 110%;
    left: 79%;
  }
}
.confetti-wrapper .confetti-79 {
  width: 10px;
  height: 4px;
  background-color: #ff5722;
  top: -10%;
  left: 99%;
  opacity: 1.2953930818;
  -webkit-transform: rotate(20.750351091deg);
      -ms-transform: rotate(20.750351091deg);
       -o-transform: rotate(20.750351091deg);
          transform: rotate(20.750351091deg);
  -webkit-animation: drop-79 4.5413352623s 0.338283637s infinite;
       -o-animation: drop-79 4.5413352623s 0.338283637s infinite;
          animation: drop-79 4.5413352623s 0.338283637s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-79 {
  100% {
    top: 110%;
    left: 103%;
  }
}
@-o-keyframes drop-79 {
  100% {
    top: 110%;
    left: 103%;
  }
}
@keyframes drop-79 {
  100% {
    top: 110%;
    left: 103%;
  }
}
.confetti-wrapper .confetti-80 {
  width: 7px;
  height: 2.8px;
  background-color: #3f51b5;
  top: -10%;
  left: 51%;
  opacity: 0.690023278;
  -webkit-transform: rotate(181.4967139225deg);
      -ms-transform: rotate(181.4967139225deg);
       -o-transform: rotate(181.4967139225deg);
          transform: rotate(181.4967139225deg);
  -webkit-animation: drop-80 4.711079005s 0.8346850736s infinite;
       -o-animation: drop-80 4.711079005s 0.8346850736s infinite;
          animation: drop-80 4.711079005s 0.8346850736s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-80 {
  100% {
    top: 110%;
    left: 65%;
  }
}
@-o-keyframes drop-80 {
  100% {
    top: 110%;
    left: 65%;
  }
}
@keyframes drop-80 {
  100% {
    top: 110%;
    left: 65%;
  }
}
.confetti-wrapper .confetti-81 {
  width: 1px;
  height: 0.4px;
  background-color: #00bcd4;
  top: -10%;
  left: -6%;
  opacity: 1.4251548022;
  -webkit-transform: rotate(40.7566424334deg);
      -ms-transform: rotate(40.7566424334deg);
       -o-transform: rotate(40.7566424334deg);
          transform: rotate(40.7566424334deg);
  -webkit-animation: drop-81 4.3009366678s 0.0223838483s infinite;
       -o-animation: drop-81 4.3009366678s 0.0223838483s infinite;
          animation: drop-81 4.3009366678s 0.0223838483s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-81 {
  100% {
    top: 110%;
    left: -5%;
  }
}
@-o-keyframes drop-81 {
  100% {
    top: 110%;
    left: -5%;
  }
}
@keyframes drop-81 {
  100% {
    top: 110%;
    left: -5%;
  }
}
.confetti-wrapper .confetti-82 {
  width: 10px;
  height: 4px;
  background-color: #F44336;
  top: -10%;
  left: 74%;
  opacity: 1.1848465797;
  -webkit-transform: rotate(36.029049472deg);
      -ms-transform: rotate(36.029049472deg);
       -o-transform: rotate(36.029049472deg);
          transform: rotate(36.029049472deg);
  -webkit-animation: drop-82 4.4034927867s 0.1330382064s infinite;
       -o-animation: drop-82 4.4034927867s 0.1330382064s infinite;
          animation: drop-82 4.4034927867s 0.1330382064s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-82 {
  100% {
    top: 110%;
    left: 75%;
  }
}
@-o-keyframes drop-82 {
  100% {
    top: 110%;
    left: 75%;
  }
}
@keyframes drop-82 {
  100% {
    top: 110%;
    left: 75%;
  }
}
.confetti-wrapper .confetti-83 {
  width: 1px;
  height: 0.4px;
  background-color: #03a9f4;
  top: -10%;
  left: 62%;
  opacity: 1.0085583583;
  -webkit-transform: rotate(131.2381923236deg);
      -ms-transform: rotate(131.2381923236deg);
       -o-transform: rotate(131.2381923236deg);
          transform: rotate(131.2381923236deg);
  -webkit-animation: drop-83 4.8608833565s 0.1455977729s infinite;
       -o-animation: drop-83 4.8608833565s 0.1455977729s infinite;
          animation: drop-83 4.8608833565s 0.1455977729s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-83 {
  100% {
    top: 110%;
    left: 67%;
  }
}
@-o-keyframes drop-83 {
  100% {
    top: 110%;
    left: 67%;
  }
}
@keyframes drop-83 {
  100% {
    top: 110%;
    left: 67%;
  }
}
.confetti-wrapper .confetti-84 {
  width: 8px;
  height: 3.2px;
  background-color: #ff9800;
  top: -10%;
  left: 55%;
  opacity: 0.5700414061;
  -webkit-transform: rotate(264.546458789deg);
      -ms-transform: rotate(264.546458789deg);
       -o-transform: rotate(264.546458789deg);
          transform: rotate(264.546458789deg);
  -webkit-animation: drop-84 4.8221873639s 0.4565642004s infinite;
       -o-animation: drop-84 4.8221873639s 0.4565642004s infinite;
          animation: drop-84 4.8221873639s 0.4565642004s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-84 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@-o-keyframes drop-84 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@keyframes drop-84 {
  100% {
    top: 110%;
    left: 62%;
  }
}
.confetti-wrapper .confetti-85 {
  width: 1px;
  height: 0.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 18%;
  opacity: 0.9621453144;
  -webkit-transform: rotate(76.7019522596deg);
      -ms-transform: rotate(76.7019522596deg);
       -o-transform: rotate(76.7019522596deg);
          transform: rotate(76.7019522596deg);
  -webkit-animation: drop-85 4.2848706283s 0.3903880257s infinite;
       -o-animation: drop-85 4.2848706283s 0.3903880257s infinite;
          animation: drop-85 4.2848706283s 0.3903880257s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-85 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@-o-keyframes drop-85 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@keyframes drop-85 {
  100% {
    top: 110%;
    left: 27%;
  }
}
.confetti-wrapper .confetti-86 {
  width: 9px;
  height: 3.6px;
  background-color: #3f51b5;
  top: -10%;
  left: 24%;
  opacity: 0.5051618019;
  -webkit-transform: rotate(141.1409784732deg);
      -ms-transform: rotate(141.1409784732deg);
       -o-transform: rotate(141.1409784732deg);
          transform: rotate(141.1409784732deg);
  -webkit-animation: drop-86 4.1684042139s 0.4486694245s infinite;
       -o-animation: drop-86 4.1684042139s 0.4486694245s infinite;
          animation: drop-86 4.1684042139s 0.4486694245s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-86 {
  100% {
    top: 110%;
    left: 25%;
  }
}
@-o-keyframes drop-86 {
  100% {
    top: 110%;
    left: 25%;
  }
}
@keyframes drop-86 {
  100% {
    top: 110%;
    left: 25%;
  }
}
.confetti-wrapper .confetti-87 {
  width: 1px;
  height: 0.4px;
  background-color: #ffeb3b;
  top: -10%;
  left: 10%;
  opacity: 0.5259555541;
  -webkit-transform: rotate(163.8574669658deg);
      -ms-transform: rotate(163.8574669658deg);
       -o-transform: rotate(163.8574669658deg);
          transform: rotate(163.8574669658deg);
  -webkit-animation: drop-87 4.113308243s 0.5007231132s infinite;
       -o-animation: drop-87 4.113308243s 0.5007231132s infinite;
          animation: drop-87 4.113308243s 0.5007231132s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-87 {
  100% {
    top: 110%;
    left: 22%;
  }
}
@-o-keyframes drop-87 {
  100% {
    top: 110%;
    left: 22%;
  }
}
@keyframes drop-87 {
  100% {
    top: 110%;
    left: 22%;
  }
}
.confetti-wrapper .confetti-88 {
  width: 2px;
  height: 0.8px;
  background-color: #9c27b0;
  top: -10%;
  left: 81%;
  opacity: 1.3006170795;
  -webkit-transform: rotate(135.4201203188deg);
      -ms-transform: rotate(135.4201203188deg);
       -o-transform: rotate(135.4201203188deg);
          transform: rotate(135.4201203188deg);
  -webkit-animation: drop-88 4.9953108218s 0.6621430176s infinite;
       -o-animation: drop-88 4.9953108218s 0.6621430176s infinite;
          animation: drop-88 4.9953108218s 0.6621430176s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-88 {
  100% {
    top: 110%;
    left: 85%;
  }
}
@-o-keyframes drop-88 {
  100% {
    top: 110%;
    left: 85%;
  }
}
@keyframes drop-88 {
  100% {
    top: 110%;
    left: 85%;
  }
}
.confetti-wrapper .confetti-89 {
  width: 5px;
  height: 2px;
  background-color: #cddc39;
  top: -10%;
  left: 48%;
  opacity: 1.2793960617;
  -webkit-transform: rotate(51.7370789246deg);
      -ms-transform: rotate(51.7370789246deg);
       -o-transform: rotate(51.7370789246deg);
          transform: rotate(51.7370789246deg);
  -webkit-animation: drop-89 4.8558165097s 0.8167157391s infinite;
       -o-animation: drop-89 4.8558165097s 0.8167157391s infinite;
          animation: drop-89 4.8558165097s 0.8167157391s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-89 {
  100% {
    top: 110%;
    left: 50%;
  }
}
@-o-keyframes drop-89 {
  100% {
    top: 110%;
    left: 50%;
  }
}
@keyframes drop-89 {
  100% {
    top: 110%;
    left: 50%;
  }
}
.confetti-wrapper .confetti-90 {
  width: 10px;
  height: 4px;
  background-color: #03a9f4;
  top: -10%;
  left: 75%;
  opacity: 0.5267881827;
  -webkit-transform: rotate(26.2899595526deg);
      -ms-transform: rotate(26.2899595526deg);
       -o-transform: rotate(26.2899595526deg);
          transform: rotate(26.2899595526deg);
  -webkit-animation: drop-90 4.3261138532s 0.0701178686s infinite;
       -o-animation: drop-90 4.3261138532s 0.0701178686s infinite;
          animation: drop-90 4.3261138532s 0.0701178686s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-90 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@-o-keyframes drop-90 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@keyframes drop-90 {
  100% {
    top: 110%;
    left: 76%;
  }
}
.confetti-wrapper .confetti-91 {
  width: 2px;
  height: 0.8px;
  background-color: #ffeb3b;
  top: -10%;
  left: 59%;
  opacity: 0.8687973852;
  -webkit-transform: rotate(138.6468592643deg);
      -ms-transform: rotate(138.6468592643deg);
       -o-transform: rotate(138.6468592643deg);
          transform: rotate(138.6468592643deg);
  -webkit-animation: drop-91 4.2475209253s 0.4889886532s infinite;
       -o-animation: drop-91 4.2475209253s 0.4889886532s infinite;
          animation: drop-91 4.2475209253s 0.4889886532s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-91 {
  100% {
    top: 110%;
    left: 73%;
  }
}
@-o-keyframes drop-91 {
  100% {
    top: 110%;
    left: 73%;
  }
}
@keyframes drop-91 {
  100% {
    top: 110%;
    left: 73%;
  }
}
.confetti-wrapper .confetti-92 {
  width: 4px;
  height: 1.6px;
  background-color: #673ab7;
  top: -10%;
  left: 11%;
  opacity: 1.1933254868;
  -webkit-transform: rotate(297.3152455095deg);
      -ms-transform: rotate(297.3152455095deg);
       -o-transform: rotate(297.3152455095deg);
          transform: rotate(297.3152455095deg);
  -webkit-animation: drop-92 4.0694749895s 0.7248781301s infinite;
       -o-animation: drop-92 4.0694749895s 0.7248781301s infinite;
          animation: drop-92 4.0694749895s 0.7248781301s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-92 {
  100% {
    top: 110%;
    left: 25%;
  }
}
@-o-keyframes drop-92 {
  100% {
    top: 110%;
    left: 25%;
  }
}
@keyframes drop-92 {
  100% {
    top: 110%;
    left: 25%;
  }
}
.confetti-wrapper .confetti-93 {
  width: 7px;
  height: 2.8px;
  background-color: #ff9800;
  top: -10%;
  left: 66%;
  opacity: 1.0343227398;
  -webkit-transform: rotate(349.2001645755deg);
      -ms-transform: rotate(349.2001645755deg);
       -o-transform: rotate(349.2001645755deg);
          transform: rotate(349.2001645755deg);
  -webkit-animation: drop-93 4.1011981263s 0.5787299227s infinite;
       -o-animation: drop-93 4.1011981263s 0.5787299227s infinite;
          animation: drop-93 4.1011981263s 0.5787299227s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-93 {
  100% {
    top: 110%;
    left: 81%;
  }
}
@-o-keyframes drop-93 {
  100% {
    top: 110%;
    left: 81%;
  }
}
@keyframes drop-93 {
  100% {
    top: 110%;
    left: 81%;
  }
}
.confetti-wrapper .confetti-94 {
  width: 7px;
  height: 2.8px;
  background-color: #cddc39;
  top: -10%;
  left: -9%;
  opacity: 0.8556071044;
  -webkit-transform: rotate(27.8546717786deg);
      -ms-transform: rotate(27.8546717786deg);
       -o-transform: rotate(27.8546717786deg);
          transform: rotate(27.8546717786deg);
  -webkit-animation: drop-94 4.0457614974s 0.2960364016s infinite;
       -o-animation: drop-94 4.0457614974s 0.2960364016s infinite;
          animation: drop-94 4.0457614974s 0.2960364016s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-94 {
  100% {
    top: 110%;
    left: -1%;
  }
}
@-o-keyframes drop-94 {
  100% {
    top: 110%;
    left: -1%;
  }
}
@keyframes drop-94 {
  100% {
    top: 110%;
    left: -1%;
  }
}
.confetti-wrapper .confetti-95 {
  width: 6px;
  height: 2.4px;
  background-color: #03a9f4;
  top: -10%;
  left: 43%;
  opacity: 0.5087001349;
  -webkit-transform: rotate(49.3131850815deg);
      -ms-transform: rotate(49.3131850815deg);
       -o-transform: rotate(49.3131850815deg);
          transform: rotate(49.3131850815deg);
  -webkit-animation: drop-95 4.1384848367s 0.144253271s infinite;
       -o-animation: drop-95 4.1384848367s 0.144253271s infinite;
          animation: drop-95 4.1384848367s 0.144253271s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-95 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@-o-keyframes drop-95 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@keyframes drop-95 {
  100% {
    top: 110%;
    left: 47%;
  }
}
.confetti-wrapper .confetti-96 {
  width: 8px;
  height: 3.2px;
  background-color: #9c27b0;
  top: -10%;
  left: 81%;
  opacity: 1.2638305939;
  -webkit-transform: rotate(308.5208629934deg);
      -ms-transform: rotate(308.5208629934deg);
       -o-transform: rotate(308.5208629934deg);
          transform: rotate(308.5208629934deg);
  -webkit-animation: drop-96 4.7579383677s 0.2841909312s infinite;
       -o-animation: drop-96 4.7579383677s 0.2841909312s infinite;
          animation: drop-96 4.7579383677s 0.2841909312s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-96 {
  100% {
    top: 110%;
    left: 85%;
  }
}
@-o-keyframes drop-96 {
  100% {
    top: 110%;
    left: 85%;
  }
}
@keyframes drop-96 {
  100% {
    top: 110%;
    left: 85%;
  }
}
.confetti-wrapper .confetti-97 {
  width: 5px;
  height: 2px;
  background-color: #ffeb3b;
  top: -10%;
  left: 8%;
  opacity: 0.7763877116;
  -webkit-transform: rotate(102.3084116985deg);
      -ms-transform: rotate(102.3084116985deg);
       -o-transform: rotate(102.3084116985deg);
          transform: rotate(102.3084116985deg);
  -webkit-animation: drop-97 4.3659108075s 0.6893381649s infinite;
       -o-animation: drop-97 4.3659108075s 0.6893381649s infinite;
          animation: drop-97 4.3659108075s 0.6893381649s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-97 {
  100% {
    top: 110%;
    left: 12%;
  }
}
@-o-keyframes drop-97 {
  100% {
    top: 110%;
    left: 12%;
  }
}
@keyframes drop-97 {
  100% {
    top: 110%;
    left: 12%;
  }
}
.confetti-wrapper .confetti-98 {
  width: 9px;
  height: 3.6px;
  background-color: #9c27b0;
  top: -10%;
  left: 27%;
  opacity: 0.8566663117;
  -webkit-transform: rotate(315.0501086463deg);
      -ms-transform: rotate(315.0501086463deg);
       -o-transform: rotate(315.0501086463deg);
          transform: rotate(315.0501086463deg);
  -webkit-animation: drop-98 4.3598923569s 0.7938299734s infinite;
       -o-animation: drop-98 4.3598923569s 0.7938299734s infinite;
          animation: drop-98 4.3598923569s 0.7938299734s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-98 {
  100% {
    top: 110%;
    left: 32%;
  }
}
@-o-keyframes drop-98 {
  100% {
    top: 110%;
    left: 32%;
  }
}
@keyframes drop-98 {
  100% {
    top: 110%;
    left: 32%;
  }
}
.confetti-wrapper .confetti-99 {
  width: 10px;
  height: 4px;
  background-color: #cddc39;
  top: -10%;
  left: 42%;
  opacity: 1.1632142685;
  -webkit-transform: rotate(8.3322170489deg);
      -ms-transform: rotate(8.3322170489deg);
       -o-transform: rotate(8.3322170489deg);
          transform: rotate(8.3322170489deg);
  -webkit-animation: drop-99 4.614364051s 0.8587487058s infinite;
       -o-animation: drop-99 4.614364051s 0.8587487058s infinite;
          animation: drop-99 4.614364051s 0.8587487058s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-99 {
  100% {
    top: 110%;
    left: 49%;
  }
}
@-o-keyframes drop-99 {
  100% {
    top: 110%;
    left: 49%;
  }
}
@keyframes drop-99 {
  100% {
    top: 110%;
    left: 49%;
  }
}
.confetti-wrapper .confetti-100 {
  width: 2px;
  height: 0.8px;
  background-color: #3f51b5;
  top: -10%;
  left: 70%;
  opacity: 0.5061280718;
  -webkit-transform: rotate(97.0711219033deg);
      -ms-transform: rotate(97.0711219033deg);
       -o-transform: rotate(97.0711219033deg);
          transform: rotate(97.0711219033deg);
  -webkit-animation: drop-100 4.5383335116s 0.848205219s infinite;
       -o-animation: drop-100 4.5383335116s 0.848205219s infinite;
          animation: drop-100 4.5383335116s 0.848205219s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-100 {
  100% {
    top: 110%;
    left: 74%;
  }
}
@-o-keyframes drop-100 {
  100% {
    top: 110%;
    left: 74%;
  }
}
@keyframes drop-100 {
  100% {
    top: 110%;
    left: 74%;
  }
}
.confetti-wrapper .confetti-101 {
  width: 5px;
  height: 2px;
  background-color: #00bcd4;
  top: -10%;
  left: 60%;
  opacity: 0.7439196261;
  -webkit-transform: rotate(306.3894030539deg);
      -ms-transform: rotate(306.3894030539deg);
       -o-transform: rotate(306.3894030539deg);
          transform: rotate(306.3894030539deg);
  -webkit-animation: drop-101 4.859355001s 0.484937369s infinite;
       -o-animation: drop-101 4.859355001s 0.484937369s infinite;
          animation: drop-101 4.859355001s 0.484937369s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-101 {
  100% {
    top: 110%;
    left: 66%;
  }
}
@-o-keyframes drop-101 {
  100% {
    top: 110%;
    left: 66%;
  }
}
@keyframes drop-101 {
  100% {
    top: 110%;
    left: 66%;
  }
}
.confetti-wrapper .confetti-102 {
  width: 2px;
  height: 0.8px;
  background-color: #F44336;
  top: -10%;
  left: 74%;
  opacity: 0.6485247032;
  -webkit-transform: rotate(79.6616129749deg);
      -ms-transform: rotate(79.6616129749deg);
       -o-transform: rotate(79.6616129749deg);
          transform: rotate(79.6616129749deg);
  -webkit-animation: drop-102 4.179347539s 0.8129285301s infinite;
       -o-animation: drop-102 4.179347539s 0.8129285301s infinite;
          animation: drop-102 4.179347539s 0.8129285301s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-102 {
  100% {
    top: 110%;
    left: 87%;
  }
}
@-o-keyframes drop-102 {
  100% {
    top: 110%;
    left: 87%;
  }
}
@keyframes drop-102 {
  100% {
    top: 110%;
    left: 87%;
  }
}
.confetti-wrapper .confetti-103 {
  width: 9px;
  height: 3.6px;
  background-color: #9c27b0;
  top: -10%;
  left: 70%;
  opacity: 0.71628945;
  -webkit-transform: rotate(292.9321901992deg);
      -ms-transform: rotate(292.9321901992deg);
       -o-transform: rotate(292.9321901992deg);
          transform: rotate(292.9321901992deg);
  -webkit-animation: drop-103 4.7308426332s 0.9861365068s infinite;
       -o-animation: drop-103 4.7308426332s 0.9861365068s infinite;
          animation: drop-103 4.7308426332s 0.9861365068s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-103 {
  100% {
    top: 110%;
    left: 73%;
  }
}
@-o-keyframes drop-103 {
  100% {
    top: 110%;
    left: 73%;
  }
}
@keyframes drop-103 {
  100% {
    top: 110%;
    left: 73%;
  }
}
.confetti-wrapper .confetti-104 {
  width: 4px;
  height: 1.6px;
  background-color: #3f51b5;
  top: -10%;
  left: 43%;
  opacity: 0.6434435119;
  -webkit-transform: rotate(210.3586957509deg);
      -ms-transform: rotate(210.3586957509deg);
       -o-transform: rotate(210.3586957509deg);
          transform: rotate(210.3586957509deg);
  -webkit-animation: drop-104 4.245539017s 0.6250212245s infinite;
       -o-animation: drop-104 4.245539017s 0.6250212245s infinite;
          animation: drop-104 4.245539017s 0.6250212245s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-104 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@-o-keyframes drop-104 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@keyframes drop-104 {
  100% {
    top: 110%;
    left: 53%;
  }
}
.confetti-wrapper .confetti-105 {
  width: 6px;
  height: 2.4px;
  background-color: #00bcd4;
  top: -10%;
  left: 12%;
  opacity: 0.7978782803;
  -webkit-transform: rotate(233.4022027641deg);
      -ms-transform: rotate(233.4022027641deg);
       -o-transform: rotate(233.4022027641deg);
          transform: rotate(233.4022027641deg);
  -webkit-animation: drop-105 4.8277747903s 0.3247299368s infinite;
       -o-animation: drop-105 4.8277747903s 0.3247299368s infinite;
          animation: drop-105 4.8277747903s 0.3247299368s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-105 {
  100% {
    top: 110%;
    left: 24%;
  }
}
@-o-keyframes drop-105 {
  100% {
    top: 110%;
    left: 24%;
  }
}
@keyframes drop-105 {
  100% {
    top: 110%;
    left: 24%;
  }
}
.confetti-wrapper .confetti-106 {
  width: 8px;
  height: 3.2px;
  background-color: #ffc107;
  top: -10%;
  left: 18%;
  opacity: 0.8576221105;
  -webkit-transform: rotate(280.8527022258deg);
      -ms-transform: rotate(280.8527022258deg);
       -o-transform: rotate(280.8527022258deg);
          transform: rotate(280.8527022258deg);
  -webkit-animation: drop-106 4.8951987592s 0.4303137421s infinite;
       -o-animation: drop-106 4.8951987592s 0.4303137421s infinite;
          animation: drop-106 4.8951987592s 0.4303137421s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-106 {
  100% {
    top: 110%;
    left: 30%;
  }
}
@-o-keyframes drop-106 {
  100% {
    top: 110%;
    left: 30%;
  }
}
@keyframes drop-106 {
  100% {
    top: 110%;
    left: 30%;
  }
}
.confetti-wrapper .confetti-107 {
  width: 3px;
  height: 1.2px;
  background-color: #673ab7;
  top: -10%;
  left: 77%;
  opacity: 0.5278849261;
  -webkit-transform: rotate(254.3060718153deg);
      -ms-transform: rotate(254.3060718153deg);
       -o-transform: rotate(254.3060718153deg);
          transform: rotate(254.3060718153deg);
  -webkit-animation: drop-107 4.7444760862s 0.2154840517s infinite;
       -o-animation: drop-107 4.7444760862s 0.2154840517s infinite;
          animation: drop-107 4.7444760862s 0.2154840517s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-107 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@-o-keyframes drop-107 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@keyframes drop-107 {
  100% {
    top: 110%;
    left: 80%;
  }
}
.confetti-wrapper .confetti-108 {
  width: 9px;
  height: 3.6px;
  background-color: #8bc34a;
  top: -10%;
  left: 69%;
  opacity: 1.0661617651;
  -webkit-transform: rotate(143.8386905145deg);
      -ms-transform: rotate(143.8386905145deg);
       -o-transform: rotate(143.8386905145deg);
          transform: rotate(143.8386905145deg);
  -webkit-animation: drop-108 4.9296689161s 0.5060488033s infinite;
       -o-animation: drop-108 4.9296689161s 0.5060488033s infinite;
          animation: drop-108 4.9296689161s 0.5060488033s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-108 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@-o-keyframes drop-108 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@keyframes drop-108 {
  100% {
    top: 110%;
    left: 83%;
  }
}
.confetti-wrapper .confetti-109 {
  width: 10px;
  height: 4px;
  background-color: #673ab7;
  top: -10%;
  left: 74%;
  opacity: 0.5939424206;
  -webkit-transform: rotate(221.3951836531deg);
      -ms-transform: rotate(221.3951836531deg);
       -o-transform: rotate(221.3951836531deg);
          transform: rotate(221.3951836531deg);
  -webkit-animation: drop-109 4.857642218s 0.2775548705s infinite;
       -o-animation: drop-109 4.857642218s 0.2775548705s infinite;
          animation: drop-109 4.857642218s 0.2775548705s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-109 {
  100% {
    top: 110%;
    left: 82%;
  }
}
@-o-keyframes drop-109 {
  100% {
    top: 110%;
    left: 82%;
  }
}
@keyframes drop-109 {
  100% {
    top: 110%;
    left: 82%;
  }
}
.confetti-wrapper .confetti-110 {
  width: 9px;
  height: 3.6px;
  background-color: #ff5722;
  top: -10%;
  left: 27%;
  opacity: 0.8071976466;
  -webkit-transform: rotate(165.343627375deg);
      -ms-transform: rotate(165.343627375deg);
       -o-transform: rotate(165.343627375deg);
          transform: rotate(165.343627375deg);
  -webkit-animation: drop-110 4.4144446036s 0.1550647282s infinite;
       -o-animation: drop-110 4.4144446036s 0.1550647282s infinite;
          animation: drop-110 4.4144446036s 0.1550647282s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-110 {
  100% {
    top: 110%;
    left: 29%;
  }
}
@-o-keyframes drop-110 {
  100% {
    top: 110%;
    left: 29%;
  }
}
@keyframes drop-110 {
  100% {
    top: 110%;
    left: 29%;
  }
}
.confetti-wrapper .confetti-111 {
  width: 8px;
  height: 3.2px;
  background-color: #3f51b5;
  top: -10%;
  left: 70%;
  opacity: 0.6282380386;
  -webkit-transform: rotate(345.0313684262deg);
      -ms-transform: rotate(345.0313684262deg);
       -o-transform: rotate(345.0313684262deg);
          transform: rotate(345.0313684262deg);
  -webkit-animation: drop-111 4.9836425997s 0.497192555s infinite;
       -o-animation: drop-111 4.9836425997s 0.497192555s infinite;
          animation: drop-111 4.9836425997s 0.497192555s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-111 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@-o-keyframes drop-111 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@keyframes drop-111 {
  100% {
    top: 110%;
    left: 76%;
  }
}
.confetti-wrapper .confetti-112 {
  width: 10px;
  height: 4px;
  background-color: #F44336;
  top: -10%;
  left: 16%;
  opacity: 0.9942466395;
  -webkit-transform: rotate(118.1388251754deg);
      -ms-transform: rotate(118.1388251754deg);
       -o-transform: rotate(118.1388251754deg);
          transform: rotate(118.1388251754deg);
  -webkit-animation: drop-112 4.9982516566s 0.0958845257s infinite;
       -o-animation: drop-112 4.9982516566s 0.0958845257s infinite;
          animation: drop-112 4.9982516566s 0.0958845257s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-112 {
  100% {
    top: 110%;
    left: 30%;
  }
}
@-o-keyframes drop-112 {
  100% {
    top: 110%;
    left: 30%;
  }
}
@keyframes drop-112 {
  100% {
    top: 110%;
    left: 30%;
  }
}
.confetti-wrapper .confetti-113 {
  width: 6px;
  height: 2.4px;
  background-color: #9c27b0;
  top: -10%;
  left: 49%;
  opacity: 0.867679121;
  -webkit-transform: rotate(196.5475983731deg);
      -ms-transform: rotate(196.5475983731deg);
       -o-transform: rotate(196.5475983731deg);
          transform: rotate(196.5475983731deg);
  -webkit-animation: drop-113 4.3286239513s 0.5669891009s infinite;
       -o-animation: drop-113 4.3286239513s 0.5669891009s infinite;
          animation: drop-113 4.3286239513s 0.5669891009s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-113 {
  100% {
    top: 110%;
    left: 57%;
  }
}
@-o-keyframes drop-113 {
  100% {
    top: 110%;
    left: 57%;
  }
}
@keyframes drop-113 {
  100% {
    top: 110%;
    left: 57%;
  }
}
.confetti-wrapper .confetti-114 {
  width: 9px;
  height: 3.6px;
  background-color: #ff9800;
  top: -10%;
  left: 68%;
  opacity: 0.9054280754;
  -webkit-transform: rotate(157.4627884714deg);
      -ms-transform: rotate(157.4627884714deg);
       -o-transform: rotate(157.4627884714deg);
          transform: rotate(157.4627884714deg);
  -webkit-animation: drop-114 4.851918676s 0.4587059917s infinite;
       -o-animation: drop-114 4.851918676s 0.4587059917s infinite;
          animation: drop-114 4.851918676s 0.4587059917s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-114 {
  100% {
    top: 110%;
    left: 73%;
  }
}
@-o-keyframes drop-114 {
  100% {
    top: 110%;
    left: 73%;
  }
}
@keyframes drop-114 {
  100% {
    top: 110%;
    left: 73%;
  }
}
.confetti-wrapper .confetti-115 {
  width: 5px;
  height: 2px;
  background-color: #673ab7;
  top: -10%;
  left: 50%;
  opacity: 0.6818597352;
  -webkit-transform: rotate(115.3472302205deg);
      -ms-transform: rotate(115.3472302205deg);
       -o-transform: rotate(115.3472302205deg);
          transform: rotate(115.3472302205deg);
  -webkit-animation: drop-115 4.8147327509s 0.0601427024s infinite;
       -o-animation: drop-115 4.8147327509s 0.0601427024s infinite;
          animation: drop-115 4.8147327509s 0.0601427024s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-115 {
  100% {
    top: 110%;
    left: 55%;
  }
}
@-o-keyframes drop-115 {
  100% {
    top: 110%;
    left: 55%;
  }
}
@keyframes drop-115 {
  100% {
    top: 110%;
    left: 55%;
  }
}
.confetti-wrapper .confetti-116 {
  width: 2px;
  height: 0.8px;
  background-color: #e91e63;
  top: -10%;
  left: 64%;
  opacity: 0.5669130134;
  -webkit-transform: rotate(178.239719448deg);
      -ms-transform: rotate(178.239719448deg);
       -o-transform: rotate(178.239719448deg);
          transform: rotate(178.239719448deg);
  -webkit-animation: drop-116 4.9737926232s 0.0780224297s infinite;
       -o-animation: drop-116 4.9737926232s 0.0780224297s infinite;
          animation: drop-116 4.9737926232s 0.0780224297s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-116 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@-o-keyframes drop-116 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@keyframes drop-116 {
  100% {
    top: 110%;
    left: 76%;
  }
}
.confetti-wrapper .confetti-117 {
  width: 3px;
  height: 1.2px;
  background-color: #ff9800;
  top: -10%;
  left: 4%;
  opacity: 1.3333728599;
  -webkit-transform: rotate(20.5610079316deg);
      -ms-transform: rotate(20.5610079316deg);
       -o-transform: rotate(20.5610079316deg);
          transform: rotate(20.5610079316deg);
  -webkit-animation: drop-117 4.5164436549s 0.4922004885s infinite;
       -o-animation: drop-117 4.5164436549s 0.4922004885s infinite;
          animation: drop-117 4.5164436549s 0.4922004885s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-117 {
  100% {
    top: 110%;
    left: 15%;
  }
}
@-o-keyframes drop-117 {
  100% {
    top: 110%;
    left: 15%;
  }
}
@keyframes drop-117 {
  100% {
    top: 110%;
    left: 15%;
  }
}
.confetti-wrapper .confetti-118 {
  width: 8px;
  height: 3.2px;
  background-color: #673ab7;
  top: -10%;
  left: 42%;
  opacity: 1.4743851116;
  -webkit-transform: rotate(20.5314218505deg);
      -ms-transform: rotate(20.5314218505deg);
       -o-transform: rotate(20.5314218505deg);
          transform: rotate(20.5314218505deg);
  -webkit-animation: drop-118 4.2060970775s 0.6464674316s infinite;
       -o-animation: drop-118 4.2060970775s 0.6464674316s infinite;
          animation: drop-118 4.2060970775s 0.6464674316s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-118 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@-o-keyframes drop-118 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@keyframes drop-118 {
  100% {
    top: 110%;
    left: 43%;
  }
}
.confetti-wrapper .confetti-119 {
  width: 1px;
  height: 0.4px;
  background-color: #8bc34a;
  top: -10%;
  left: 40%;
  opacity: 1.4355294476;
  -webkit-transform: rotate(243.9214986461deg);
      -ms-transform: rotate(243.9214986461deg);
       -o-transform: rotate(243.9214986461deg);
          transform: rotate(243.9214986461deg);
  -webkit-animation: drop-119 4.3368032073s 0.6633850721s infinite;
       -o-animation: drop-119 4.3368032073s 0.6633850721s infinite;
          animation: drop-119 4.3368032073s 0.6633850721s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-119 {
  100% {
    top: 110%;
    left: 48%;
  }
}
@-o-keyframes drop-119 {
  100% {
    top: 110%;
    left: 48%;
  }
}
@keyframes drop-119 {
  100% {
    top: 110%;
    left: 48%;
  }
}
.confetti-wrapper .confetti-120 {
  width: 10px;
  height: 4px;
  background-color: #4CAF50;
  top: -10%;
  left: 43%;
  opacity: 0.5444524982;
  -webkit-transform: rotate(55.8164571661deg);
      -ms-transform: rotate(55.8164571661deg);
       -o-transform: rotate(55.8164571661deg);
          transform: rotate(55.8164571661deg);
  -webkit-animation: drop-120 4.488746562s 0.1691985661s infinite;
       -o-animation: drop-120 4.488746562s 0.1691985661s infinite;
          animation: drop-120 4.488746562s 0.1691985661s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-120 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@-o-keyframes drop-120 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@keyframes drop-120 {
  100% {
    top: 110%;
    left: 53%;
  }
}
.confetti-wrapper .confetti-121 {
  width: 2px;
  height: 0.8px;
  background-color: #00bcd4;
  top: -10%;
  left: 9%;
  opacity: 1.4629925908;
  -webkit-transform: rotate(291.4357548708deg);
      -ms-transform: rotate(291.4357548708deg);
       -o-transform: rotate(291.4357548708deg);
          transform: rotate(291.4357548708deg);
  -webkit-animation: drop-121 4.2192307718s 0.5670766114s infinite;
       -o-animation: drop-121 4.2192307718s 0.5670766114s infinite;
          animation: drop-121 4.2192307718s 0.5670766114s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-121 {
  100% {
    top: 110%;
    left: 22%;
  }
}
@-o-keyframes drop-121 {
  100% {
    top: 110%;
    left: 22%;
  }
}
@keyframes drop-121 {
  100% {
    top: 110%;
    left: 22%;
  }
}
.confetti-wrapper .confetti-122 {
  width: 2px;
  height: 0.8px;
  background-color: #ffc107;
  top: -10%;
  left: 45%;
  opacity: 0.7896730763;
  -webkit-transform: rotate(175.7008213482deg);
      -ms-transform: rotate(175.7008213482deg);
       -o-transform: rotate(175.7008213482deg);
          transform: rotate(175.7008213482deg);
  -webkit-animation: drop-122 4.9280489512s 0.781178634s infinite;
       -o-animation: drop-122 4.9280489512s 0.781178634s infinite;
          animation: drop-122 4.9280489512s 0.781178634s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-122 {
  100% {
    top: 110%;
    left: 60%;
  }
}
@-o-keyframes drop-122 {
  100% {
    top: 110%;
    left: 60%;
  }
}
@keyframes drop-122 {
  100% {
    top: 110%;
    left: 60%;
  }
}
.confetti-wrapper .confetti-123 {
  width: 1px;
  height: 0.4px;
  background-color: #673ab7;
  top: -10%;
  left: 89%;
  opacity: 0.6545491028;
  -webkit-transform: rotate(60.7460795715deg);
      -ms-transform: rotate(60.7460795715deg);
       -o-transform: rotate(60.7460795715deg);
          transform: rotate(60.7460795715deg);
  -webkit-animation: drop-123 4.946322679s 0.1207226252s infinite;
       -o-animation: drop-123 4.946322679s 0.1207226252s infinite;
          animation: drop-123 4.946322679s 0.1207226252s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-123 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@-o-keyframes drop-123 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@keyframes drop-123 {
  100% {
    top: 110%;
    left: 100%;
  }
}
.confetti-wrapper .confetti-124 {
  width: 8px;
  height: 3.2px;
  background-color: #ffeb3b;
  top: -10%;
  left: -4%;
  opacity: 0.8676519239;
  -webkit-transform: rotate(85.6866484995deg);
      -ms-transform: rotate(85.6866484995deg);
       -o-transform: rotate(85.6866484995deg);
          transform: rotate(85.6866484995deg);
  -webkit-animation: drop-124 4.1274143767s 0.7905247017s infinite;
       -o-animation: drop-124 4.1274143767s 0.7905247017s infinite;
          animation: drop-124 4.1274143767s 0.7905247017s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-124 {
  100% {
    top: 110%;
    left: -2%;
  }
}
@-o-keyframes drop-124 {
  100% {
    top: 110%;
    left: -2%;
  }
}
@keyframes drop-124 {
  100% {
    top: 110%;
    left: -2%;
  }
}
.confetti-wrapper .confetti-125 {
  width: 2px;
  height: 0.8px;
  background-color: #cddc39;
  top: -10%;
  left: 33%;
  opacity: 1.1256567987;
  -webkit-transform: rotate(185.5126302455deg);
      -ms-transform: rotate(185.5126302455deg);
       -o-transform: rotate(185.5126302455deg);
          transform: rotate(185.5126302455deg);
  -webkit-animation: drop-125 4.0258680756s 0.1709522822s infinite;
       -o-animation: drop-125 4.0258680756s 0.1709522822s infinite;
          animation: drop-125 4.0258680756s 0.1709522822s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-125 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@-o-keyframes drop-125 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@keyframes drop-125 {
  100% {
    top: 110%;
    left: 39%;
  }
}
.confetti-wrapper .confetti-126 {
  width: 3px;
  height: 1.2px;
  background-color: #9c27b0;
  top: -10%;
  left: 1%;
  opacity: 1.099028086;
  -webkit-transform: rotate(183.4185610053deg);
      -ms-transform: rotate(183.4185610053deg);
       -o-transform: rotate(183.4185610053deg);
          transform: rotate(183.4185610053deg);
  -webkit-animation: drop-126 4.1562469289s 0.5823682773s infinite;
       -o-animation: drop-126 4.1562469289s 0.5823682773s infinite;
          animation: drop-126 4.1562469289s 0.5823682773s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-126 {
  100% {
    top: 110%;
    left: 3%;
  }
}
@-o-keyframes drop-126 {
  100% {
    top: 110%;
    left: 3%;
  }
}
@keyframes drop-126 {
  100% {
    top: 110%;
    left: 3%;
  }
}
.confetti-wrapper .confetti-127 {
  width: 8px;
  height: 3.2px;
  background-color: #4CAF50;
  top: -10%;
  left: 48%;
  opacity: 0.7029940465;
  -webkit-transform: rotate(228.6243348311deg);
      -ms-transform: rotate(228.6243348311deg);
       -o-transform: rotate(228.6243348311deg);
          transform: rotate(228.6243348311deg);
  -webkit-animation: drop-127 4.0993395485s 0.6975458904s infinite;
       -o-animation: drop-127 4.0993395485s 0.6975458904s infinite;
          animation: drop-127 4.0993395485s 0.6975458904s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-127 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@-o-keyframes drop-127 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@keyframes drop-127 {
  100% {
    top: 110%;
    left: 62%;
  }
}
.confetti-wrapper .confetti-128 {
  width: 10px;
  height: 4px;
  background-color: #ffc107;
  top: -10%;
  left: 12%;
  opacity: 1.2486713148;
  -webkit-transform: rotate(119.9031207453deg);
      -ms-transform: rotate(119.9031207453deg);
       -o-transform: rotate(119.9031207453deg);
          transform: rotate(119.9031207453deg);
  -webkit-animation: drop-128 4.0256951956s 0.0020220668s infinite;
       -o-animation: drop-128 4.0256951956s 0.0020220668s infinite;
          animation: drop-128 4.0256951956s 0.0020220668s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-128 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@-o-keyframes drop-128 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@keyframes drop-128 {
  100% {
    top: 110%;
    left: 27%;
  }
}
.confetti-wrapper .confetti-129 {
  width: 8px;
  height: 3.2px;
  background-color: #ff9800;
  top: -10%;
  left: 58%;
  opacity: 1.2949838456;
  -webkit-transform: rotate(9.8594639469deg);
      -ms-transform: rotate(9.8594639469deg);
       -o-transform: rotate(9.8594639469deg);
          transform: rotate(9.8594639469deg);
  -webkit-animation: drop-129 4.7323818766s 0.9581232412s infinite;
       -o-animation: drop-129 4.7323818766s 0.9581232412s infinite;
          animation: drop-129 4.7323818766s 0.9581232412s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-129 {
  100% {
    top: 110%;
    left: 72%;
  }
}
@-o-keyframes drop-129 {
  100% {
    top: 110%;
    left: 72%;
  }
}
@keyframes drop-129 {
  100% {
    top: 110%;
    left: 72%;
  }
}
.confetti-wrapper .confetti-130 {
  width: 5px;
  height: 2px;
  background-color: #F44336;
  top: -10%;
  left: -8%;
  opacity: 1.449889173;
  -webkit-transform: rotate(331.3087801609deg);
      -ms-transform: rotate(331.3087801609deg);
       -o-transform: rotate(331.3087801609deg);
          transform: rotate(331.3087801609deg);
  -webkit-animation: drop-130 4.4969565158s 0.9599862552s infinite;
       -o-animation: drop-130 4.4969565158s 0.9599862552s infinite;
          animation: drop-130 4.4969565158s 0.9599862552s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-130 {
  100% {
    top: 110%;
    left: 4%;
  }
}
@-o-keyframes drop-130 {
  100% {
    top: 110%;
    left: 4%;
  }
}
@keyframes drop-130 {
  100% {
    top: 110%;
    left: 4%;
  }
}
.confetti-wrapper .confetti-131 {
  width: 9px;
  height: 3.6px;
  background-color: #3f51b5;
  top: -10%;
  left: 62%;
  opacity: 1.3409555916;
  -webkit-transform: rotate(239.6134204116deg);
      -ms-transform: rotate(239.6134204116deg);
       -o-transform: rotate(239.6134204116deg);
          transform: rotate(239.6134204116deg);
  -webkit-animation: drop-131 4.2563418433s 0.9541658311s infinite;
       -o-animation: drop-131 4.2563418433s 0.9541658311s infinite;
          animation: drop-131 4.2563418433s 0.9541658311s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-131 {
  100% {
    top: 110%;
    left: 66%;
  }
}
@-o-keyframes drop-131 {
  100% {
    top: 110%;
    left: 66%;
  }
}
@keyframes drop-131 {
  100% {
    top: 110%;
    left: 66%;
  }
}
.confetti-wrapper .confetti-132 {
  width: 7px;
  height: 2.8px;
  background-color: #F44336;
  top: -10%;
  left: 40%;
  opacity: 0.7933008242;
  -webkit-transform: rotate(191.3238481753deg);
      -ms-transform: rotate(191.3238481753deg);
       -o-transform: rotate(191.3238481753deg);
          transform: rotate(191.3238481753deg);
  -webkit-animation: drop-132 4.450127326s 0.7622440122s infinite;
       -o-animation: drop-132 4.450127326s 0.7622440122s infinite;
          animation: drop-132 4.450127326s 0.7622440122s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-132 {
  100% {
    top: 110%;
    left: 54%;
  }
}
@-o-keyframes drop-132 {
  100% {
    top: 110%;
    left: 54%;
  }
}
@keyframes drop-132 {
  100% {
    top: 110%;
    left: 54%;
  }
}
.confetti-wrapper .confetti-133 {
  width: 10px;
  height: 4px;
  background-color: #00bcd4;
  top: -10%;
  left: 66%;
  opacity: 0.6696695283;
  -webkit-transform: rotate(11.4392687912deg);
      -ms-transform: rotate(11.4392687912deg);
       -o-transform: rotate(11.4392687912deg);
          transform: rotate(11.4392687912deg);
  -webkit-animation: drop-133 4.0932282454s 0.9828654399s infinite;
       -o-animation: drop-133 4.0932282454s 0.9828654399s infinite;
          animation: drop-133 4.0932282454s 0.9828654399s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-133 {
  100% {
    top: 110%;
    left: 69%;
  }
}
@-o-keyframes drop-133 {
  100% {
    top: 110%;
    left: 69%;
  }
}
@keyframes drop-133 {
  100% {
    top: 110%;
    left: 69%;
  }
}
.confetti-wrapper .confetti-134 {
  width: 5px;
  height: 2px;
  background-color: #3f51b5;
  top: -10%;
  left: 29%;
  opacity: 1.0418004247;
  -webkit-transform: rotate(25.5993802223deg);
      -ms-transform: rotate(25.5993802223deg);
       -o-transform: rotate(25.5993802223deg);
          transform: rotate(25.5993802223deg);
  -webkit-animation: drop-134 4.9075565864s 0.0800939719s infinite;
       -o-animation: drop-134 4.9075565864s 0.0800939719s infinite;
          animation: drop-134 4.9075565864s 0.0800939719s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-134 {
  100% {
    top: 110%;
    left: 37%;
  }
}
@-o-keyframes drop-134 {
  100% {
    top: 110%;
    left: 37%;
  }
}
@keyframes drop-134 {
  100% {
    top: 110%;
    left: 37%;
  }
}
.confetti-wrapper .confetti-135 {
  width: 4px;
  height: 1.6px;
  background-color: #4CAF50;
  top: -10%;
  left: 31%;
  opacity: 0.6735822973;
  -webkit-transform: rotate(153.9128706008deg);
      -ms-transform: rotate(153.9128706008deg);
       -o-transform: rotate(153.9128706008deg);
          transform: rotate(153.9128706008deg);
  -webkit-animation: drop-135 4.4684623736s 0.8134643783s infinite;
       -o-animation: drop-135 4.4684623736s 0.8134643783s infinite;
          animation: drop-135 4.4684623736s 0.8134643783s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-135 {
  100% {
    top: 110%;
    left: 44%;
  }
}
@-o-keyframes drop-135 {
  100% {
    top: 110%;
    left: 44%;
  }
}
@keyframes drop-135 {
  100% {
    top: 110%;
    left: 44%;
  }
}
.confetti-wrapper .confetti-136 {
  width: 3px;
  height: 1.2px;
  background-color: #9c27b0;
  top: -10%;
  left: 25%;
  opacity: 1.4452324481;
  -webkit-transform: rotate(179.2988777479deg);
      -ms-transform: rotate(179.2988777479deg);
       -o-transform: rotate(179.2988777479deg);
          transform: rotate(179.2988777479deg);
  -webkit-animation: drop-136 4.3935242792s 0.0285604133s infinite;
       -o-animation: drop-136 4.3935242792s 0.0285604133s infinite;
          animation: drop-136 4.3935242792s 0.0285604133s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-136 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@-o-keyframes drop-136 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@keyframes drop-136 {
  100% {
    top: 110%;
    left: 27%;
  }
}
.confetti-wrapper .confetti-137 {
  width: 3px;
  height: 1.2px;
  background-color: #673ab7;
  top: -10%;
  left: 16%;
  opacity: 1.0640509355;
  -webkit-transform: rotate(322.9790046466deg);
      -ms-transform: rotate(322.9790046466deg);
       -o-transform: rotate(322.9790046466deg);
          transform: rotate(322.9790046466deg);
  -webkit-animation: drop-137 4.3061062375s 0.0130192827s infinite;
       -o-animation: drop-137 4.3061062375s 0.0130192827s infinite;
          animation: drop-137 4.3061062375s 0.0130192827s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-137 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@-o-keyframes drop-137 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@keyframes drop-137 {
  100% {
    top: 110%;
    left: 19%;
  }
}
.confetti-wrapper .confetti-138 {
  width: 1px;
  height: 0.4px;
  background-color: #009688;
  top: -10%;
  left: 48%;
  opacity: 0.630404112;
  -webkit-transform: rotate(317.5273588831deg);
      -ms-transform: rotate(317.5273588831deg);
       -o-transform: rotate(317.5273588831deg);
          transform: rotate(317.5273588831deg);
  -webkit-animation: drop-138 4.0135068613s 0.4735854396s infinite;
       -o-animation: drop-138 4.0135068613s 0.4735854396s infinite;
          animation: drop-138 4.0135068613s 0.4735854396s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-138 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@-o-keyframes drop-138 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@keyframes drop-138 {
  100% {
    top: 110%;
    left: 53%;
  }
}
.confetti-wrapper .confetti-139 {
  width: 1px;
  height: 0.4px;
  background-color: #4CAF50;
  top: -10%;
  left: 36%;
  opacity: 1.041932043;
  -webkit-transform: rotate(137.4856737536deg);
      -ms-transform: rotate(137.4856737536deg);
       -o-transform: rotate(137.4856737536deg);
          transform: rotate(137.4856737536deg);
  -webkit-animation: drop-139 4.1786914797s 0.7436952526s infinite;
       -o-animation: drop-139 4.1786914797s 0.7436952526s infinite;
          animation: drop-139 4.1786914797s 0.7436952526s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-139 {
  100% {
    top: 110%;
    left: 51%;
  }
}
@-o-keyframes drop-139 {
  100% {
    top: 110%;
    left: 51%;
  }
}
@keyframes drop-139 {
  100% {
    top: 110%;
    left: 51%;
  }
}
.confetti-wrapper .confetti-140 {
  width: 2px;
  height: 0.8px;
  background-color: #ff5722;
  top: -10%;
  left: 2%;
  opacity: 1.0427806899;
  -webkit-transform: rotate(142.5463656277deg);
      -ms-transform: rotate(142.5463656277deg);
       -o-transform: rotate(142.5463656277deg);
          transform: rotate(142.5463656277deg);
  -webkit-animation: drop-140 4.9307864913s 0.936935591s infinite;
       -o-animation: drop-140 4.9307864913s 0.936935591s infinite;
          animation: drop-140 4.9307864913s 0.936935591s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-140 {
  100% {
    top: 110%;
    left: 4%;
  }
}
@-o-keyframes drop-140 {
  100% {
    top: 110%;
    left: 4%;
  }
}
@keyframes drop-140 {
  100% {
    top: 110%;
    left: 4%;
  }
}
.confetti-wrapper .confetti-141 {
  width: 5px;
  height: 2px;
  background-color: #03a9f4;
  top: -10%;
  left: 38%;
  opacity: 1.1830441279;
  -webkit-transform: rotate(227.5915432062deg);
      -ms-transform: rotate(227.5915432062deg);
       -o-transform: rotate(227.5915432062deg);
          transform: rotate(227.5915432062deg);
  -webkit-animation: drop-141 4.7000562829s 0.6553187022s infinite;
       -o-animation: drop-141 4.7000562829s 0.6553187022s infinite;
          animation: drop-141 4.7000562829s 0.6553187022s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-141 {
  100% {
    top: 110%;
    left: 51%;
  }
}
@-o-keyframes drop-141 {
  100% {
    top: 110%;
    left: 51%;
  }
}
@keyframes drop-141 {
  100% {
    top: 110%;
    left: 51%;
  }
}
.confetti-wrapper .confetti-142 {
  width: 5px;
  height: 2px;
  background-color: #9c27b0;
  top: -10%;
  left: 100%;
  opacity: 0.9454361132;
  -webkit-transform: rotate(167.2917861434deg);
      -ms-transform: rotate(167.2917861434deg);
       -o-transform: rotate(167.2917861434deg);
          transform: rotate(167.2917861434deg);
  -webkit-animation: drop-142 4.5545679969s 0.6444687098s infinite;
       -o-animation: drop-142 4.5545679969s 0.6444687098s infinite;
          animation: drop-142 4.5545679969s 0.6444687098s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-142 {
  100% {
    top: 110%;
    left: 103%;
  }
}
@-o-keyframes drop-142 {
  100% {
    top: 110%;
    left: 103%;
  }
}
@keyframes drop-142 {
  100% {
    top: 110%;
    left: 103%;
  }
}
.confetti-wrapper .confetti-143 {
  width: 2px;
  height: 0.8px;
  background-color: #009688;
  top: -10%;
  left: 1%;
  opacity: 1.4374743066;
  -webkit-transform: rotate(165.1747177509deg);
      -ms-transform: rotate(165.1747177509deg);
       -o-transform: rotate(165.1747177509deg);
          transform: rotate(165.1747177509deg);
  -webkit-animation: drop-143 4.5926219287s 0.5194883823s infinite;
       -o-animation: drop-143 4.5926219287s 0.5194883823s infinite;
          animation: drop-143 4.5926219287s 0.5194883823s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-143 {
  100% {
    top: 110%;
    left: 16%;
  }
}
@-o-keyframes drop-143 {
  100% {
    top: 110%;
    left: 16%;
  }
}
@keyframes drop-143 {
  100% {
    top: 110%;
    left: 16%;
  }
}
.confetti-wrapper .confetti-144 {
  width: 3px;
  height: 1.2px;
  background-color: #8bc34a;
  top: -10%;
  left: -6%;
  opacity: 1.052057465;
  -webkit-transform: rotate(116.8189346845deg);
      -ms-transform: rotate(116.8189346845deg);
       -o-transform: rotate(116.8189346845deg);
          transform: rotate(116.8189346845deg);
  -webkit-animation: drop-144 4.1908587136s 0.8691693234s infinite;
       -o-animation: drop-144 4.1908587136s 0.8691693234s infinite;
          animation: drop-144 4.1908587136s 0.8691693234s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-144 {
  100% {
    top: 110%;
    left: 2%;
  }
}
@-o-keyframes drop-144 {
  100% {
    top: 110%;
    left: 2%;
  }
}
@keyframes drop-144 {
  100% {
    top: 110%;
    left: 2%;
  }
}
.confetti-wrapper .confetti-145 {
  width: 3px;
  height: 1.2px;
  background-color: #009688;
  top: -10%;
  left: 1%;
  opacity: 1.2453425101;
  -webkit-transform: rotate(38.1039392522deg);
      -ms-transform: rotate(38.1039392522deg);
       -o-transform: rotate(38.1039392522deg);
          transform: rotate(38.1039392522deg);
  -webkit-animation: drop-145 4.4374359595s 0.8510342451s infinite;
       -o-animation: drop-145 4.4374359595s 0.8510342451s infinite;
          animation: drop-145 4.4374359595s 0.8510342451s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-145 {
  100% {
    top: 110%;
    left: 16%;
  }
}
@-o-keyframes drop-145 {
  100% {
    top: 110%;
    left: 16%;
  }
}
@keyframes drop-145 {
  100% {
    top: 110%;
    left: 16%;
  }
}
.confetti-wrapper .confetti-146 {
  width: 7px;
  height: 2.8px;
  background-color: #cddc39;
  top: -10%;
  left: 9%;
  opacity: 1.0758626153;
  -webkit-transform: rotate(166.5253255878deg);
      -ms-transform: rotate(166.5253255878deg);
       -o-transform: rotate(166.5253255878deg);
          transform: rotate(166.5253255878deg);
  -webkit-animation: drop-146 4.9556837164s 0.0812538369s infinite;
       -o-animation: drop-146 4.9556837164s 0.0812538369s infinite;
          animation: drop-146 4.9556837164s 0.0812538369s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-146 {
  100% {
    top: 110%;
    left: 17%;
  }
}
@-o-keyframes drop-146 {
  100% {
    top: 110%;
    left: 17%;
  }
}
@keyframes drop-146 {
  100% {
    top: 110%;
    left: 17%;
  }
}
.confetti-wrapper .confetti-147 {
  width: 4px;
  height: 1.6px;
  background-color: #9c27b0;
  top: -10%;
  left: 82%;
  opacity: 0.9565455627;
  -webkit-transform: rotate(103.7914932872deg);
      -ms-transform: rotate(103.7914932872deg);
       -o-transform: rotate(103.7914932872deg);
          transform: rotate(103.7914932872deg);
  -webkit-animation: drop-147 4.9376966716s 0.8672844655s infinite;
       -o-animation: drop-147 4.9376966716s 0.8672844655s infinite;
          animation: drop-147 4.9376966716s 0.8672844655s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-147 {
  100% {
    top: 110%;
    left: 95%;
  }
}
@-o-keyframes drop-147 {
  100% {
    top: 110%;
    left: 95%;
  }
}
@keyframes drop-147 {
  100% {
    top: 110%;
    left: 95%;
  }
}
.confetti-wrapper .confetti-148 {
  width: 7px;
  height: 2.8px;
  background-color: #8bc34a;
  top: -10%;
  left: 28%;
  opacity: 1.2489559205;
  -webkit-transform: rotate(327.318160297deg);
      -ms-transform: rotate(327.318160297deg);
       -o-transform: rotate(327.318160297deg);
          transform: rotate(327.318160297deg);
  -webkit-animation: drop-148 4.8565868792s 0.8851969024s infinite;
       -o-animation: drop-148 4.8565868792s 0.8851969024s infinite;
          animation: drop-148 4.8565868792s 0.8851969024s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-148 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@-o-keyframes drop-148 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@keyframes drop-148 {
  100% {
    top: 110%;
    left: 35%;
  }
}
.confetti-wrapper .confetti-149 {
  width: 8px;
  height: 3.2px;
  background-color: #00bcd4;
  top: -10%;
  left: 89%;
  opacity: 0.6624059601;
  -webkit-transform: rotate(239.4407114991deg);
      -ms-transform: rotate(239.4407114991deg);
       -o-transform: rotate(239.4407114991deg);
          transform: rotate(239.4407114991deg);
  -webkit-animation: drop-149 4.2143653283s 0.7294130503s infinite;
       -o-animation: drop-149 4.2143653283s 0.7294130503s infinite;
          animation: drop-149 4.2143653283s 0.7294130503s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-149 {
  100% {
    top: 110%;
    left: 99%;
  }
}
@-o-keyframes drop-149 {
  100% {
    top: 110%;
    left: 99%;
  }
}
@keyframes drop-149 {
  100% {
    top: 110%;
    left: 99%;
  }
}
.confetti-wrapper .confetti-150 {
  width: 9px;
  height: 3.6px;
  background-color: #8bc34a;
  top: -10%;
  left: 55%;
  opacity: 1.0811390129;
  -webkit-transform: rotate(296.632527307deg);
      -ms-transform: rotate(296.632527307deg);
       -o-transform: rotate(296.632527307deg);
          transform: rotate(296.632527307deg);
  -webkit-animation: drop-150 4.9831886541s 0.8125391441s infinite;
       -o-animation: drop-150 4.9831886541s 0.8125391441s infinite;
          animation: drop-150 4.9831886541s 0.8125391441s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-150 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@-o-keyframes drop-150 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@keyframes drop-150 {
  100% {
    top: 110%;
    left: 64%;
  }
}
.confetti-wrapper .confetti-151 {
  width: 4px;
  height: 1.6px;
  background-color: #ffc107;
  top: -10%;
  left: 90%;
  opacity: 0.7889886584;
  -webkit-transform: rotate(343.8105915157deg);
      -ms-transform: rotate(343.8105915157deg);
       -o-transform: rotate(343.8105915157deg);
          transform: rotate(343.8105915157deg);
  -webkit-animation: drop-151 4.3958661374s 0.554212822s infinite;
       -o-animation: drop-151 4.3958661374s 0.554212822s infinite;
          animation: drop-151 4.3958661374s 0.554212822s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-151 {
  100% {
    top: 110%;
    left: 105%;
  }
}
@-o-keyframes drop-151 {
  100% {
    top: 110%;
    left: 105%;
  }
}
@keyframes drop-151 {
  100% {
    top: 110%;
    left: 105%;
  }
}
.confetti-wrapper .confetti-152 {
  width: 9px;
  height: 3.6px;
  background-color: #cddc39;
  top: -10%;
  left: 83%;
  opacity: 0.6231429362;
  -webkit-transform: rotate(154.2702235475deg);
      -ms-transform: rotate(154.2702235475deg);
       -o-transform: rotate(154.2702235475deg);
          transform: rotate(154.2702235475deg);
  -webkit-animation: drop-152 4.0512116548s 0.0337508209s infinite;
       -o-animation: drop-152 4.0512116548s 0.0337508209s infinite;
          animation: drop-152 4.0512116548s 0.0337508209s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-152 {
  100% {
    top: 110%;
    left: 88%;
  }
}
@-o-keyframes drop-152 {
  100% {
    top: 110%;
    left: 88%;
  }
}
@keyframes drop-152 {
  100% {
    top: 110%;
    left: 88%;
  }
}
.confetti-wrapper .confetti-153 {
  width: 4px;
  height: 1.6px;
  background-color: #673ab7;
  top: -10%;
  left: 14%;
  opacity: 0.6917518264;
  -webkit-transform: rotate(170.6948161037deg);
      -ms-transform: rotate(170.6948161037deg);
       -o-transform: rotate(170.6948161037deg);
          transform: rotate(170.6948161037deg);
  -webkit-animation: drop-153 4.4520424883s 0.2766123172s infinite;
       -o-animation: drop-153 4.4520424883s 0.2766123172s infinite;
          animation: drop-153 4.4520424883s 0.2766123172s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-153 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@-o-keyframes drop-153 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@keyframes drop-153 {
  100% {
    top: 110%;
    left: 19%;
  }
}
.confetti-wrapper .confetti-154 {
  width: 7px;
  height: 2.8px;
  background-color: #F44336;
  top: -10%;
  left: 66%;
  opacity: 0.993436117;
  -webkit-transform: rotate(290.1961167194deg);
      -ms-transform: rotate(290.1961167194deg);
       -o-transform: rotate(290.1961167194deg);
          transform: rotate(290.1961167194deg);
  -webkit-animation: drop-154 4.617871306s 0.4896170111s infinite;
       -o-animation: drop-154 4.617871306s 0.4896170111s infinite;
          animation: drop-154 4.617871306s 0.4896170111s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-154 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@-o-keyframes drop-154 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@keyframes drop-154 {
  100% {
    top: 110%;
    left: 68%;
  }
}
.confetti-wrapper .confetti-155 {
  width: 6px;
  height: 2.4px;
  background-color: #ff9800;
  top: -10%;
  left: 34%;
  opacity: 0.5082015462;
  -webkit-transform: rotate(246.5130522464deg);
      -ms-transform: rotate(246.5130522464deg);
       -o-transform: rotate(246.5130522464deg);
          transform: rotate(246.5130522464deg);
  -webkit-animation: drop-155 4.3904849061s 0.5955651201s infinite;
       -o-animation: drop-155 4.3904849061s 0.5955651201s infinite;
          animation: drop-155 4.3904849061s 0.5955651201s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-155 {
  100% {
    top: 110%;
    left: 38%;
  }
}
@-o-keyframes drop-155 {
  100% {
    top: 110%;
    left: 38%;
  }
}
@keyframes drop-155 {
  100% {
    top: 110%;
    left: 38%;
  }
}
.confetti-wrapper .confetti-156 {
  width: 5px;
  height: 2px;
  background-color: #ffeb3b;
  top: -10%;
  left: 34%;
  opacity: 0.9812040733;
  -webkit-transform: rotate(18.3119642905deg);
      -ms-transform: rotate(18.3119642905deg);
       -o-transform: rotate(18.3119642905deg);
          transform: rotate(18.3119642905deg);
  -webkit-animation: drop-156 4.2997730051s 0.3123257227s infinite;
       -o-animation: drop-156 4.2997730051s 0.3123257227s infinite;
          animation: drop-156 4.2997730051s 0.3123257227s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-156 {
  100% {
    top: 110%;
    left: 46%;
  }
}
@-o-keyframes drop-156 {
  100% {
    top: 110%;
    left: 46%;
  }
}
@keyframes drop-156 {
  100% {
    top: 110%;
    left: 46%;
  }
}
.confetti-wrapper .confetti-157 {
  width: 3px;
  height: 1.2px;
  background-color: #ffeb3b;
  top: -10%;
  left: 62%;
  opacity: 1.3135582456;
  -webkit-transform: rotate(256.8040829145deg);
      -ms-transform: rotate(256.8040829145deg);
       -o-transform: rotate(256.8040829145deg);
          transform: rotate(256.8040829145deg);
  -webkit-animation: drop-157 4.0274463156s 0.0686797649s infinite;
       -o-animation: drop-157 4.0274463156s 0.0686797649s infinite;
          animation: drop-157 4.0274463156s 0.0686797649s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-157 {
  100% {
    top: 110%;
    left: 63%;
  }
}
@-o-keyframes drop-157 {
  100% {
    top: 110%;
    left: 63%;
  }
}
@keyframes drop-157 {
  100% {
    top: 110%;
    left: 63%;
  }
}
.confetti-wrapper .confetti-158 {
  width: 10px;
  height: 4px;
  background-color: #F44336;
  top: -10%;
  left: 6%;
  opacity: 1.1879955452;
  -webkit-transform: rotate(247.7091284728deg);
      -ms-transform: rotate(247.7091284728deg);
       -o-transform: rotate(247.7091284728deg);
          transform: rotate(247.7091284728deg);
  -webkit-animation: drop-158 4.295768177s 0.5626831556s infinite;
       -o-animation: drop-158 4.295768177s 0.5626831556s infinite;
          animation: drop-158 4.295768177s 0.5626831556s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-158 {
  100% {
    top: 110%;
    left: 11%;
  }
}
@-o-keyframes drop-158 {
  100% {
    top: 110%;
    left: 11%;
  }
}
@keyframes drop-158 {
  100% {
    top: 110%;
    left: 11%;
  }
}
.confetti-wrapper .confetti-159 {
  width: 1px;
  height: 0.4px;
  background-color: #4CAF50;
  top: -10%;
  left: 57%;
  opacity: 1.4989335271;
  -webkit-transform: rotate(46.2841276009deg);
      -ms-transform: rotate(46.2841276009deg);
       -o-transform: rotate(46.2841276009deg);
          transform: rotate(46.2841276009deg);
  -webkit-animation: drop-159 4.9762903632s 0.0879664195s infinite;
       -o-animation: drop-159 4.9762903632s 0.0879664195s infinite;
          animation: drop-159 4.9762903632s 0.0879664195s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-159 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@-o-keyframes drop-159 {
  100% {
    top: 110%;
    left: 64%;
  }
}
@keyframes drop-159 {
  100% {
    top: 110%;
    left: 64%;
  }
}
.confetti-wrapper .confetti-160 {
  width: 3px;
  height: 1.2px;
  background-color: #e91e63;
  top: -10%;
  left: 6%;
  opacity: 1.0930014786;
  -webkit-transform: rotate(51.6364573689deg);
      -ms-transform: rotate(51.6364573689deg);
       -o-transform: rotate(51.6364573689deg);
          transform: rotate(51.6364573689deg);
  -webkit-animation: drop-160 4.9235778298s 0.1499543109s infinite;
       -o-animation: drop-160 4.9235778298s 0.1499543109s infinite;
          animation: drop-160 4.9235778298s 0.1499543109s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-160 {
  100% {
    top: 110%;
    left: 21%;
  }
}
@-o-keyframes drop-160 {
  100% {
    top: 110%;
    left: 21%;
  }
}
@keyframes drop-160 {
  100% {
    top: 110%;
    left: 21%;
  }
}
.confetti-wrapper .confetti-161 {
  width: 1px;
  height: 0.4px;
  background-color: #8bc34a;
  top: -10%;
  left: 77%;
  opacity: 1.2424190527;
  -webkit-transform: rotate(28.7913988069deg);
      -ms-transform: rotate(28.7913988069deg);
       -o-transform: rotate(28.7913988069deg);
          transform: rotate(28.7913988069deg);
  -webkit-animation: drop-161 4.9527821958s 0.4325297518s infinite;
       -o-animation: drop-161 4.9527821958s 0.4325297518s infinite;
          animation: drop-161 4.9527821958s 0.4325297518s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-161 {
  100% {
    top: 110%;
    left: 88%;
  }
}
@-o-keyframes drop-161 {
  100% {
    top: 110%;
    left: 88%;
  }
}
@keyframes drop-161 {
  100% {
    top: 110%;
    left: 88%;
  }
}
.confetti-wrapper .confetti-162 {
  width: 3px;
  height: 1.2px;
  background-color: #ffc107;
  top: -10%;
  left: 59%;
  opacity: 0.7481082937;
  -webkit-transform: rotate(294.2848122774deg);
      -ms-transform: rotate(294.2848122774deg);
       -o-transform: rotate(294.2848122774deg);
          transform: rotate(294.2848122774deg);
  -webkit-animation: drop-162 4.5381310466s 0.2181604427s infinite;
       -o-animation: drop-162 4.5381310466s 0.2181604427s infinite;
          animation: drop-162 4.5381310466s 0.2181604427s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-162 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@-o-keyframes drop-162 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@keyframes drop-162 {
  100% {
    top: 110%;
    left: 62%;
  }
}
.confetti-wrapper .confetti-163 {
  width: 5px;
  height: 2px;
  background-color: #00bcd4;
  top: -10%;
  left: 48%;
  opacity: 0.7141106266;
  -webkit-transform: rotate(194.097597539deg);
      -ms-transform: rotate(194.097597539deg);
       -o-transform: rotate(194.097597539deg);
          transform: rotate(194.097597539deg);
  -webkit-animation: drop-163 4.2655088898s 0.6878479095s infinite;
       -o-animation: drop-163 4.2655088898s 0.6878479095s infinite;
          animation: drop-163 4.2655088898s 0.6878479095s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-163 {
  100% {
    top: 110%;
    left: 60%;
  }
}
@-o-keyframes drop-163 {
  100% {
    top: 110%;
    left: 60%;
  }
}
@keyframes drop-163 {
  100% {
    top: 110%;
    left: 60%;
  }
}
.confetti-wrapper .confetti-164 {
  width: 1px;
  height: 0.4px;
  background-color: #F44336;
  top: -10%;
  left: 46%;
  opacity: 1.4730052478;
  -webkit-transform: rotate(108.9407189568deg);
      -ms-transform: rotate(108.9407189568deg);
       -o-transform: rotate(108.9407189568deg);
          transform: rotate(108.9407189568deg);
  -webkit-animation: drop-164 4.6173645455s 0.4830318283s infinite;
       -o-animation: drop-164 4.6173645455s 0.4830318283s infinite;
          animation: drop-164 4.6173645455s 0.4830318283s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-164 {
  100% {
    top: 110%;
    left: 58%;
  }
}
@-o-keyframes drop-164 {
  100% {
    top: 110%;
    left: 58%;
  }
}
@keyframes drop-164 {
  100% {
    top: 110%;
    left: 58%;
  }
}
.confetti-wrapper .confetti-165 {
  width: 1px;
  height: 0.4px;
  background-color: #e91e63;
  top: -10%;
  left: 25%;
  opacity: 0.5759518504;
  -webkit-transform: rotate(150.6187134378deg);
      -ms-transform: rotate(150.6187134378deg);
       -o-transform: rotate(150.6187134378deg);
          transform: rotate(150.6187134378deg);
  -webkit-animation: drop-165 4.6378804274s 0.1699082717s infinite;
       -o-animation: drop-165 4.6378804274s 0.1699082717s infinite;
          animation: drop-165 4.6378804274s 0.1699082717s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-165 {
  100% {
    top: 110%;
    left: 38%;
  }
}
@-o-keyframes drop-165 {
  100% {
    top: 110%;
    left: 38%;
  }
}
@keyframes drop-165 {
  100% {
    top: 110%;
    left: 38%;
  }
}
.confetti-wrapper .confetti-166 {
  width: 2px;
  height: 0.8px;
  background-color: #ff5722;
  top: -10%;
  left: 91%;
  opacity: 0.7224763863;
  -webkit-transform: rotate(2.5939650839deg);
      -ms-transform: rotate(2.5939650839deg);
       -o-transform: rotate(2.5939650839deg);
          transform: rotate(2.5939650839deg);
  -webkit-animation: drop-166 4.4726987184s 0.5695778025s infinite;
       -o-animation: drop-166 4.4726987184s 0.5695778025s infinite;
          animation: drop-166 4.4726987184s 0.5695778025s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-166 {
  100% {
    top: 110%;
    left: 95%;
  }
}
@-o-keyframes drop-166 {
  100% {
    top: 110%;
    left: 95%;
  }
}
@keyframes drop-166 {
  100% {
    top: 110%;
    left: 95%;
  }
}
.confetti-wrapper .confetti-167 {
  width: 7px;
  height: 2.8px;
  background-color: #ffeb3b;
  top: -10%;
  left: 0%;
  opacity: 1.2277370133;
  -webkit-transform: rotate(162.6976493057deg);
      -ms-transform: rotate(162.6976493057deg);
       -o-transform: rotate(162.6976493057deg);
          transform: rotate(162.6976493057deg);
  -webkit-animation: drop-167 4.9694577687s 0.9780262046s infinite;
       -o-animation: drop-167 4.9694577687s 0.9780262046s infinite;
          animation: drop-167 4.9694577687s 0.9780262046s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-167 {
  100% {
    top: 110%;
    left: 11%;
  }
}
@-o-keyframes drop-167 {
  100% {
    top: 110%;
    left: 11%;
  }
}
@keyframes drop-167 {
  100% {
    top: 110%;
    left: 11%;
  }
}
.confetti-wrapper .confetti-168 {
  width: 8px;
  height: 3.2px;
  background-color: #8bc34a;
  top: -10%;
  left: 100%;
  opacity: 0.7924606782;
  -webkit-transform: rotate(212.5672219352deg);
      -ms-transform: rotate(212.5672219352deg);
       -o-transform: rotate(212.5672219352deg);
          transform: rotate(212.5672219352deg);
  -webkit-animation: drop-168 4.7538055317s 0.8254416606s infinite;
       -o-animation: drop-168 4.7538055317s 0.8254416606s infinite;
          animation: drop-168 4.7538055317s 0.8254416606s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-168 {
  100% {
    top: 110%;
    left: 112%;
  }
}
@-o-keyframes drop-168 {
  100% {
    top: 110%;
    left: 112%;
  }
}
@keyframes drop-168 {
  100% {
    top: 110%;
    left: 112%;
  }
}
.confetti-wrapper .confetti-169 {
  width: 7px;
  height: 2.8px;
  background-color: #cddc39;
  top: -10%;
  left: 21%;
  opacity: 1.4939348962;
  -webkit-transform: rotate(135.7905096758deg);
      -ms-transform: rotate(135.7905096758deg);
       -o-transform: rotate(135.7905096758deg);
          transform: rotate(135.7905096758deg);
  -webkit-animation: drop-169 4.6591743483s 0.4304844914s infinite;
       -o-animation: drop-169 4.6591743483s 0.4304844914s infinite;
          animation: drop-169 4.6591743483s 0.4304844914s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-169 {
  100% {
    top: 110%;
    left: 24%;
  }
}
@-o-keyframes drop-169 {
  100% {
    top: 110%;
    left: 24%;
  }
}
@keyframes drop-169 {
  100% {
    top: 110%;
    left: 24%;
  }
}
.confetti-wrapper .confetti-170 {
  width: 9px;
  height: 3.6px;
  background-color: #e91e63;
  top: -10%;
  left: 24%;
  opacity: 1.1555944848;
  -webkit-transform: rotate(71.6523829413deg);
      -ms-transform: rotate(71.6523829413deg);
       -o-transform: rotate(71.6523829413deg);
          transform: rotate(71.6523829413deg);
  -webkit-animation: drop-170 4.87162212s 0.9213364152s infinite;
       -o-animation: drop-170 4.87162212s 0.9213364152s infinite;
          animation: drop-170 4.87162212s 0.9213364152s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-170 {
  100% {
    top: 110%;
    left: 34%;
  }
}
@-o-keyframes drop-170 {
  100% {
    top: 110%;
    left: 34%;
  }
}
@keyframes drop-170 {
  100% {
    top: 110%;
    left: 34%;
  }
}
.confetti-wrapper .confetti-171 {
  width: 3px;
  height: 1.2px;
  background-color: #3f51b5;
  top: -10%;
  left: 52%;
  opacity: 0.7701590667;
  -webkit-transform: rotate(77.3850447465deg);
      -ms-transform: rotate(77.3850447465deg);
       -o-transform: rotate(77.3850447465deg);
          transform: rotate(77.3850447465deg);
  -webkit-animation: drop-171 4.9086274629s 0.3023129194s infinite;
       -o-animation: drop-171 4.9086274629s 0.3023129194s infinite;
          animation: drop-171 4.9086274629s 0.3023129194s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-171 {
  100% {
    top: 110%;
    left: 55%;
  }
}
@-o-keyframes drop-171 {
  100% {
    top: 110%;
    left: 55%;
  }
}
@keyframes drop-171 {
  100% {
    top: 110%;
    left: 55%;
  }
}
.confetti-wrapper .confetti-172 {
  width: 2px;
  height: 0.8px;
  background-color: #ffc107;
  top: -10%;
  left: 73%;
  opacity: 0.9003160532;
  -webkit-transform: rotate(252.8199621015deg);
      -ms-transform: rotate(252.8199621015deg);
       -o-transform: rotate(252.8199621015deg);
          transform: rotate(252.8199621015deg);
  -webkit-animation: drop-172 4.9990804393s 0.8133862746s infinite;
       -o-animation: drop-172 4.9990804393s 0.8133862746s infinite;
          animation: drop-172 4.9990804393s 0.8133862746s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-172 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@-o-keyframes drop-172 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@keyframes drop-172 {
  100% {
    top: 110%;
    left: 76%;
  }
}
.confetti-wrapper .confetti-173 {
  width: 9px;
  height: 3.6px;
  background-color: #009688;
  top: -10%;
  left: -5%;
  opacity: 1.2792114587;
  -webkit-transform: rotate(190.0746869972deg);
      -ms-transform: rotate(190.0746869972deg);
       -o-transform: rotate(190.0746869972deg);
          transform: rotate(190.0746869972deg);
  -webkit-animation: drop-173 4.1128170922s 0.4406926535s infinite;
       -o-animation: drop-173 4.1128170922s 0.4406926535s infinite;
          animation: drop-173 4.1128170922s 0.4406926535s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-173 {
  100% {
    top: 110%;
    left: 2%;
  }
}
@-o-keyframes drop-173 {
  100% {
    top: 110%;
    left: 2%;
  }
}
@keyframes drop-173 {
  100% {
    top: 110%;
    left: 2%;
  }
}
.confetti-wrapper .confetti-174 {
  width: 4px;
  height: 1.6px;
  background-color: #03a9f4;
  top: -10%;
  left: 79%;
  opacity: 1.4410090163;
  -webkit-transform: rotate(263.477645009deg);
      -ms-transform: rotate(263.477645009deg);
       -o-transform: rotate(263.477645009deg);
          transform: rotate(263.477645009deg);
  -webkit-animation: drop-174 4.5620419701s 0.6934985543s infinite;
       -o-animation: drop-174 4.5620419701s 0.6934985543s infinite;
          animation: drop-174 4.5620419701s 0.6934985543s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-174 {
  100% {
    top: 110%;
    left: 92%;
  }
}
@-o-keyframes drop-174 {
  100% {
    top: 110%;
    left: 92%;
  }
}
@keyframes drop-174 {
  100% {
    top: 110%;
    left: 92%;
  }
}
.confetti-wrapper .confetti-175 {
  width: 2px;
  height: 0.8px;
  background-color: #F44336;
  top: -10%;
  left: 60%;
  opacity: 1.1367770462;
  -webkit-transform: rotate(129.886379045deg);
      -ms-transform: rotate(129.886379045deg);
       -o-transform: rotate(129.886379045deg);
          transform: rotate(129.886379045deg);
  -webkit-animation: drop-175 4.8418235969s 0.9033309727s infinite;
       -o-animation: drop-175 4.8418235969s 0.9033309727s infinite;
          animation: drop-175 4.8418235969s 0.9033309727s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-175 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@-o-keyframes drop-175 {
  100% {
    top: 110%;
    left: 62%;
  }
}
@keyframes drop-175 {
  100% {
    top: 110%;
    left: 62%;
  }
}
.confetti-wrapper .confetti-176 {
  width: 4px;
  height: 1.6px;
  background-color: #ffc107;
  top: -10%;
  left: 20%;
  opacity: 1.259022832;
  -webkit-transform: rotate(30.5595453717deg);
      -ms-transform: rotate(30.5595453717deg);
       -o-transform: rotate(30.5595453717deg);
          transform: rotate(30.5595453717deg);
  -webkit-animation: drop-176 4.0084060234s 0.3692722661s infinite;
       -o-animation: drop-176 4.0084060234s 0.3692722661s infinite;
          animation: drop-176 4.0084060234s 0.3692722661s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-176 {
  100% {
    top: 110%;
    left: 30%;
  }
}
@-o-keyframes drop-176 {
  100% {
    top: 110%;
    left: 30%;
  }
}
@keyframes drop-176 {
  100% {
    top: 110%;
    left: 30%;
  }
}
.confetti-wrapper .confetti-177 {
  width: 4px;
  height: 1.6px;
  background-color: #4CAF50;
  top: -10%;
  left: 40%;
  opacity: 1.4351654371;
  -webkit-transform: rotate(355.7142390047deg);
      -ms-transform: rotate(355.7142390047deg);
       -o-transform: rotate(355.7142390047deg);
          transform: rotate(355.7142390047deg);
  -webkit-animation: drop-177 4.9741687677s 0.5309267652s infinite;
       -o-animation: drop-177 4.9741687677s 0.5309267652s infinite;
          animation: drop-177 4.9741687677s 0.5309267652s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-177 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@-o-keyframes drop-177 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@keyframes drop-177 {
  100% {
    top: 110%;
    left: 47%;
  }
}
.confetti-wrapper .confetti-178 {
  width: 2px;
  height: 0.8px;
  background-color: #00bcd4;
  top: -10%;
  left: 31%;
  opacity: 1.1018415365;
  -webkit-transform: rotate(90.955195907deg);
      -ms-transform: rotate(90.955195907deg);
       -o-transform: rotate(90.955195907deg);
          transform: rotate(90.955195907deg);
  -webkit-animation: drop-178 4.675151284s 0.7869553739s infinite;
       -o-animation: drop-178 4.675151284s 0.7869553739s infinite;
          animation: drop-178 4.675151284s 0.7869553739s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-178 {
  100% {
    top: 110%;
    left: 36%;
  }
}
@-o-keyframes drop-178 {
  100% {
    top: 110%;
    left: 36%;
  }
}
@keyframes drop-178 {
  100% {
    top: 110%;
    left: 36%;
  }
}
.confetti-wrapper .confetti-179 {
  width: 2px;
  height: 0.8px;
  background-color: #e91e63;
  top: -10%;
  left: 29%;
  opacity: 1.2119293823;
  -webkit-transform: rotate(270.3777593926deg);
      -ms-transform: rotate(270.3777593926deg);
       -o-transform: rotate(270.3777593926deg);
          transform: rotate(270.3777593926deg);
  -webkit-animation: drop-179 4.697403284s 0.0484438532s infinite;
       -o-animation: drop-179 4.697403284s 0.0484438532s infinite;
          animation: drop-179 4.697403284s 0.0484438532s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-179 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@-o-keyframes drop-179 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@keyframes drop-179 {
  100% {
    top: 110%;
    left: 35%;
  }
}
.confetti-wrapper .confetti-180 {
  width: 3px;
  height: 1.2px;
  background-color: #9c27b0;
  top: -10%;
  left: -7%;
  opacity: 0.9118047147;
  -webkit-transform: rotate(176.1355264298deg);
      -ms-transform: rotate(176.1355264298deg);
       -o-transform: rotate(176.1355264298deg);
          transform: rotate(176.1355264298deg);
  -webkit-animation: drop-180 4.6662635499s 0.3131962456s infinite;
       -o-animation: drop-180 4.6662635499s 0.3131962456s infinite;
          animation: drop-180 4.6662635499s 0.3131962456s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-180 {
  100% {
    top: 110%;
    left: -2%;
  }
}
@-o-keyframes drop-180 {
  100% {
    top: 110%;
    left: -2%;
  }
}
@keyframes drop-180 {
  100% {
    top: 110%;
    left: -2%;
  }
}
.confetti-wrapper .confetti-181 {
  width: 5px;
  height: 2px;
  background-color: #ffc107;
  top: -10%;
  left: 76%;
  opacity: 0.7455560937;
  -webkit-transform: rotate(280.8027437345deg);
      -ms-transform: rotate(280.8027437345deg);
       -o-transform: rotate(280.8027437345deg);
          transform: rotate(280.8027437345deg);
  -webkit-animation: drop-181 4.4840672624s 0.3710769279s infinite;
       -o-animation: drop-181 4.4840672624s 0.3710769279s infinite;
          animation: drop-181 4.4840672624s 0.3710769279s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-181 {
  100% {
    top: 110%;
    left: 85%;
  }
}
@-o-keyframes drop-181 {
  100% {
    top: 110%;
    left: 85%;
  }
}
@keyframes drop-181 {
  100% {
    top: 110%;
    left: 85%;
  }
}
.confetti-wrapper .confetti-182 {
  width: 9px;
  height: 3.6px;
  background-color: #cddc39;
  top: -10%;
  left: 22%;
  opacity: 1.1933332561;
  -webkit-transform: rotate(112.275137477deg);
      -ms-transform: rotate(112.275137477deg);
       -o-transform: rotate(112.275137477deg);
          transform: rotate(112.275137477deg);
  -webkit-animation: drop-182 4.7091767296s 0.3677144468s infinite;
       -o-animation: drop-182 4.7091767296s 0.3677144468s infinite;
          animation: drop-182 4.7091767296s 0.3677144468s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-182 {
  100% {
    top: 110%;
    left: 36%;
  }
}
@-o-keyframes drop-182 {
  100% {
    top: 110%;
    left: 36%;
  }
}
@keyframes drop-182 {
  100% {
    top: 110%;
    left: 36%;
  }
}
.confetti-wrapper .confetti-183 {
  width: 10px;
  height: 4px;
  background-color: #3f51b5;
  top: -10%;
  left: 3%;
  opacity: 1.2173453181;
  -webkit-transform: rotate(274.1722378252deg);
      -ms-transform: rotate(274.1722378252deg);
       -o-transform: rotate(274.1722378252deg);
          transform: rotate(274.1722378252deg);
  -webkit-animation: drop-183 4.8154962834s 0.857083772s infinite;
       -o-animation: drop-183 4.8154962834s 0.857083772s infinite;
          animation: drop-183 4.8154962834s 0.857083772s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-183 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@-o-keyframes drop-183 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@keyframes drop-183 {
  100% {
    top: 110%;
    left: 9%;
  }
}
.confetti-wrapper .confetti-184 {
  width: 8px;
  height: 3.2px;
  background-color: #cddc39;
  top: -10%;
  left: 59%;
  opacity: 1.1312647704;
  -webkit-transform: rotate(170.0396669155deg);
      -ms-transform: rotate(170.0396669155deg);
       -o-transform: rotate(170.0396669155deg);
          transform: rotate(170.0396669155deg);
  -webkit-animation: drop-184 4.5069463666s 0.9037116908s infinite;
       -o-animation: drop-184 4.5069463666s 0.9037116908s infinite;
          animation: drop-184 4.5069463666s 0.9037116908s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-184 {
  100% {
    top: 110%;
    left: 63%;
  }
}
@-o-keyframes drop-184 {
  100% {
    top: 110%;
    left: 63%;
  }
}
@keyframes drop-184 {
  100% {
    top: 110%;
    left: 63%;
  }
}
.confetti-wrapper .confetti-185 {
  width: 9px;
  height: 3.6px;
  background-color: #03a9f4;
  top: -10%;
  left: 100%;
  opacity: 1.2076064663;
  -webkit-transform: rotate(113.9185109628deg);
      -ms-transform: rotate(113.9185109628deg);
       -o-transform: rotate(113.9185109628deg);
          transform: rotate(113.9185109628deg);
  -webkit-animation: drop-185 4.8952119355s 0.7347753086s infinite;
       -o-animation: drop-185 4.8952119355s 0.7347753086s infinite;
          animation: drop-185 4.8952119355s 0.7347753086s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-185 {
  100% {
    top: 110%;
    left: 105%;
  }
}
@-o-keyframes drop-185 {
  100% {
    top: 110%;
    left: 105%;
  }
}
@keyframes drop-185 {
  100% {
    top: 110%;
    left: 105%;
  }
}
.confetti-wrapper .confetti-186 {
  width: 7px;
  height: 2.8px;
  background-color: #9c27b0;
  top: -10%;
  left: 29%;
  opacity: 1.4591181633;
  -webkit-transform: rotate(320.2165832476deg);
      -ms-transform: rotate(320.2165832476deg);
       -o-transform: rotate(320.2165832476deg);
          transform: rotate(320.2165832476deg);
  -webkit-animation: drop-186 4.7952152331s 0.9153781843s infinite;
       -o-animation: drop-186 4.7952152331s 0.9153781843s infinite;
          animation: drop-186 4.7952152331s 0.9153781843s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-186 {
  100% {
    top: 110%;
    left: 33%;
  }
}
@-o-keyframes drop-186 {
  100% {
    top: 110%;
    left: 33%;
  }
}
@keyframes drop-186 {
  100% {
    top: 110%;
    left: 33%;
  }
}
.confetti-wrapper .confetti-187 {
  width: 4px;
  height: 1.6px;
  background-color: #cddc39;
  top: -10%;
  left: 48%;
  opacity: 0.8451784369;
  -webkit-transform: rotate(9.8462356408deg);
      -ms-transform: rotate(9.8462356408deg);
       -o-transform: rotate(9.8462356408deg);
          transform: rotate(9.8462356408deg);
  -webkit-animation: drop-187 4.9917321963s 0.5646957655s infinite;
       -o-animation: drop-187 4.9917321963s 0.5646957655s infinite;
          animation: drop-187 4.9917321963s 0.5646957655s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-187 {
  100% {
    top: 110%;
    left: 55%;
  }
}
@-o-keyframes drop-187 {
  100% {
    top: 110%;
    left: 55%;
  }
}
@keyframes drop-187 {
  100% {
    top: 110%;
    left: 55%;
  }
}
.confetti-wrapper .confetti-188 {
  width: 8px;
  height: 3.2px;
  background-color: #673ab7;
  top: -10%;
  left: 94%;
  opacity: 1.2748164972;
  -webkit-transform: rotate(215.0877746359deg);
      -ms-transform: rotate(215.0877746359deg);
       -o-transform: rotate(215.0877746359deg);
          transform: rotate(215.0877746359deg);
  -webkit-animation: drop-188 4.0940444799s 0.3912587039s infinite;
       -o-animation: drop-188 4.0940444799s 0.3912587039s infinite;
          animation: drop-188 4.0940444799s 0.3912587039s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-188 {
  100% {
    top: 110%;
    left: 95%;
  }
}
@-o-keyframes drop-188 {
  100% {
    top: 110%;
    left: 95%;
  }
}
@keyframes drop-188 {
  100% {
    top: 110%;
    left: 95%;
  }
}
.confetti-wrapper .confetti-189 {
  width: 1px;
  height: 0.4px;
  background-color: #009688;
  top: -10%;
  left: -4%;
  opacity: 0.994971822;
  -webkit-transform: rotate(114.2014076058deg);
      -ms-transform: rotate(114.2014076058deg);
       -o-transform: rotate(114.2014076058deg);
          transform: rotate(114.2014076058deg);
  -webkit-animation: drop-189 4.2790196431s 0.129613783s infinite;
       -o-animation: drop-189 4.2790196431s 0.129613783s infinite;
          animation: drop-189 4.2790196431s 0.129613783s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-189 {
  100% {
    top: 110%;
    left: 1%;
  }
}
@-o-keyframes drop-189 {
  100% {
    top: 110%;
    left: 1%;
  }
}
@keyframes drop-189 {
  100% {
    top: 110%;
    left: 1%;
  }
}
.confetti-wrapper .confetti-190 {
  width: 3px;
  height: 1.2px;
  background-color: #4CAF50;
  top: -10%;
  left: 5%;
  opacity: 1.2928643471;
  -webkit-transform: rotate(215.562948348deg);
      -ms-transform: rotate(215.562948348deg);
       -o-transform: rotate(215.562948348deg);
          transform: rotate(215.562948348deg);
  -webkit-animation: drop-190 4.7792498566s 0.0093055201s infinite;
       -o-animation: drop-190 4.7792498566s 0.0093055201s infinite;
          animation: drop-190 4.7792498566s 0.0093055201s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-190 {
  100% {
    top: 110%;
    left: 8%;
  }
}
@-o-keyframes drop-190 {
  100% {
    top: 110%;
    left: 8%;
  }
}
@keyframes drop-190 {
  100% {
    top: 110%;
    left: 8%;
  }
}
.confetti-wrapper .confetti-191 {
  width: 6px;
  height: 2.4px;
  background-color: #cddc39;
  top: -10%;
  left: 32%;
  opacity: 0.6252900737;
  -webkit-transform: rotate(109.6614017586deg);
      -ms-transform: rotate(109.6614017586deg);
       -o-transform: rotate(109.6614017586deg);
          transform: rotate(109.6614017586deg);
  -webkit-animation: drop-191 4.9724263647s 0.9262988978s infinite;
       -o-animation: drop-191 4.9724263647s 0.9262988978s infinite;
          animation: drop-191 4.9724263647s 0.9262988978s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-191 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@-o-keyframes drop-191 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@keyframes drop-191 {
  100% {
    top: 110%;
    left: 39%;
  }
}
.confetti-wrapper .confetti-192 {
  width: 10px;
  height: 4px;
  background-color: #8bc34a;
  top: -10%;
  left: 100%;
  opacity: 1.1069841078;
  -webkit-transform: rotate(350.0949564696deg);
      -ms-transform: rotate(350.0949564696deg);
       -o-transform: rotate(350.0949564696deg);
          transform: rotate(350.0949564696deg);
  -webkit-animation: drop-192 4.4781172396s 0.8759402496s infinite;
       -o-animation: drop-192 4.4781172396s 0.8759402496s infinite;
          animation: drop-192 4.4781172396s 0.8759402496s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-192 {
  100% {
    top: 110%;
    left: 106%;
  }
}
@-o-keyframes drop-192 {
  100% {
    top: 110%;
    left: 106%;
  }
}
@keyframes drop-192 {
  100% {
    top: 110%;
    left: 106%;
  }
}
.confetti-wrapper .confetti-193 {
  width: 8px;
  height: 3.2px;
  background-color: #3f51b5;
  top: -10%;
  left: 21%;
  opacity: 0.745605704;
  -webkit-transform: rotate(349.111758468deg);
      -ms-transform: rotate(349.111758468deg);
       -o-transform: rotate(349.111758468deg);
          transform: rotate(349.111758468deg);
  -webkit-animation: drop-193 4.9351295427s 0.4497633446s infinite;
       -o-animation: drop-193 4.9351295427s 0.4497633446s infinite;
          animation: drop-193 4.9351295427s 0.4497633446s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-193 {
  100% {
    top: 110%;
    left: 31%;
  }
}
@-o-keyframes drop-193 {
  100% {
    top: 110%;
    left: 31%;
  }
}
@keyframes drop-193 {
  100% {
    top: 110%;
    left: 31%;
  }
}
.confetti-wrapper .confetti-194 {
  width: 9px;
  height: 3.6px;
  background-color: #3f51b5;
  top: -10%;
  left: -2%;
  opacity: 0.7391641326;
  -webkit-transform: rotate(42.3968611432deg);
      -ms-transform: rotate(42.3968611432deg);
       -o-transform: rotate(42.3968611432deg);
          transform: rotate(42.3968611432deg);
  -webkit-animation: drop-194 4.5588080492s 0.7104230099s infinite;
       -o-animation: drop-194 4.5588080492s 0.7104230099s infinite;
          animation: drop-194 4.5588080492s 0.7104230099s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-194 {
  100% {
    top: 110%;
    left: 7%;
  }
}
@-o-keyframes drop-194 {
  100% {
    top: 110%;
    left: 7%;
  }
}
@keyframes drop-194 {
  100% {
    top: 110%;
    left: 7%;
  }
}
.confetti-wrapper .confetti-195 {
  width: 3px;
  height: 1.2px;
  background-color: #cddc39;
  top: -10%;
  left: 40%;
  opacity: 0.7420027726;
  -webkit-transform: rotate(237.2377024199deg);
      -ms-transform: rotate(237.2377024199deg);
       -o-transform: rotate(237.2377024199deg);
          transform: rotate(237.2377024199deg);
  -webkit-animation: drop-195 4.9997032185s 0.0778745733s infinite;
       -o-animation: drop-195 4.9997032185s 0.0778745733s infinite;
          animation: drop-195 4.9997032185s 0.0778745733s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-195 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@-o-keyframes drop-195 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@keyframes drop-195 {
  100% {
    top: 110%;
    left: 43%;
  }
}
.confetti-wrapper .confetti-196 {
  width: 10px;
  height: 4px;
  background-color: #3f51b5;
  top: -10%;
  left: 36%;
  opacity: 0.6398841581;
  -webkit-transform: rotate(66.887613477deg);
      -ms-transform: rotate(66.887613477deg);
       -o-transform: rotate(66.887613477deg);
          transform: rotate(66.887613477deg);
  -webkit-animation: drop-196 4.6228247374s 0.2260955018s infinite;
       -o-animation: drop-196 4.6228247374s 0.2260955018s infinite;
          animation: drop-196 4.6228247374s 0.2260955018s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-196 {
  100% {
    top: 110%;
    left: 50%;
  }
}
@-o-keyframes drop-196 {
  100% {
    top: 110%;
    left: 50%;
  }
}
@keyframes drop-196 {
  100% {
    top: 110%;
    left: 50%;
  }
}
.confetti-wrapper .confetti-197 {
  width: 8px;
  height: 3.2px;
  background-color: #4CAF50;
  top: -10%;
  left: 19%;
  opacity: 0.5590344852;
  -webkit-transform: rotate(189.4002669022deg);
      -ms-transform: rotate(189.4002669022deg);
       -o-transform: rotate(189.4002669022deg);
          transform: rotate(189.4002669022deg);
  -webkit-animation: drop-197 4.623656164s 0.2422070685s infinite;
       -o-animation: drop-197 4.623656164s 0.2422070685s infinite;
          animation: drop-197 4.623656164s 0.2422070685s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-197 {
  100% {
    top: 110%;
    left: 28%;
  }
}
@-o-keyframes drop-197 {
  100% {
    top: 110%;
    left: 28%;
  }
}
@keyframes drop-197 {
  100% {
    top: 110%;
    left: 28%;
  }
}
.confetti-wrapper .confetti-198 {
  width: 3px;
  height: 1.2px;
  background-color: #3f51b5;
  top: -10%;
  left: 80%;
  opacity: 1.0468880099;
  -webkit-transform: rotate(235.2935229334deg);
      -ms-transform: rotate(235.2935229334deg);
       -o-transform: rotate(235.2935229334deg);
          transform: rotate(235.2935229334deg);
  -webkit-animation: drop-198 4.144648797s 0.4561505274s infinite;
       -o-animation: drop-198 4.144648797s 0.4561505274s infinite;
          animation: drop-198 4.144648797s 0.4561505274s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-198 {
  100% {
    top: 110%;
    left: 90%;
  }
}
@-o-keyframes drop-198 {
  100% {
    top: 110%;
    left: 90%;
  }
}
@keyframes drop-198 {
  100% {
    top: 110%;
    left: 90%;
  }
}
.confetti-wrapper .confetti-199 {
  width: 2px;
  height: 0.8px;
  background-color: #3f51b5;
  top: -10%;
  left: 87%;
  opacity: 1.1167228024;
  -webkit-transform: rotate(211.4933526025deg);
      -ms-transform: rotate(211.4933526025deg);
       -o-transform: rotate(211.4933526025deg);
          transform: rotate(211.4933526025deg);
  -webkit-animation: drop-199 4.4517620169s 0.2248419357s infinite;
       -o-animation: drop-199 4.4517620169s 0.2248419357s infinite;
          animation: drop-199 4.4517620169s 0.2248419357s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-199 {
  100% {
    top: 110%;
    left: 94%;
  }
}
@-o-keyframes drop-199 {
  100% {
    top: 110%;
    left: 94%;
  }
}
@keyframes drop-199 {
  100% {
    top: 110%;
    left: 94%;
  }
}
.confetti-wrapper .confetti-200 {
  width: 6px;
  height: 2.4px;
  background-color: #F44336;
  top: -10%;
  left: -7%;
  opacity: 1.0529909954;
  -webkit-transform: rotate(11.3464529309deg);
      -ms-transform: rotate(11.3464529309deg);
       -o-transform: rotate(11.3464529309deg);
          transform: rotate(11.3464529309deg);
  -webkit-animation: drop-200 4.1924903042s 0.3048024733s infinite;
       -o-animation: drop-200 4.1924903042s 0.3048024733s infinite;
          animation: drop-200 4.1924903042s 0.3048024733s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-200 {
  100% {
    top: 110%;
    left: 1%;
  }
}
@-o-keyframes drop-200 {
  100% {
    top: 110%;
    left: 1%;
  }
}
@keyframes drop-200 {
  100% {
    top: 110%;
    left: 1%;
  }
}