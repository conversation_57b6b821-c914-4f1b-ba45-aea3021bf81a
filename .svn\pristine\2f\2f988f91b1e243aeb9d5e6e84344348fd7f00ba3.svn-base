.wizlet.wizletVanilla .colletion-menu .container .row > .col, .wizlet.wizletActionMenu .collection-menu .container .row > .col { padding: 0; }

.wizlet.wizletVanilla .colletion-menu .card-panel.margin-right, .wizlet.wizletActionMenu .collection-menu .card-panel.margin-right { margin-right: 10%; }

.wizlet.wizletVanilla .colletion-menu .card-panel .intro, .wizlet.wizletActionMenu .collection-menu .card-panel .intro { font-size: 1.25rem; }

.wizlet.wizletVanilla .colletion-menu .card-panel .card-image, .wizlet.wizletActionMenu .collection-menu .card-panel .card-image { padding-top: 12px; }

.wizlet.wizletVanilla .colletion-menu .card-panel .card-image img, .wizlet.wizletActionMenu .collection-menu .card-panel .card-image img { background-color: white; }

.wizlet.wizletVanilla .colletion-menu .collection, .wizlet.wizletActionMenu .collection-menu .collection { margin: 1.5rem 0 1rem 0; border: none; overflow-y: auto; }

.wizlet.wizletVanilla .colletion-menu .collection .collection-item, .wizlet.wizletActionMenu .collection-menu .collection .collection-item { padding: 15px 5px; text-align: left; border-color: #FE41A5; cursor: pointer; }

.wizlet.wizletVanilla .colletion-menu .collection .collection-item a, .wizlet.wizletActionMenu .collection-menu .collection .collection-item a { display: block; color: #28324B; }

.wizlet.wizletVanilla .colletion-menu .collection .collection-item.visited, .wizlet.wizletActionMenu .collection-menu .collection .collection-item.visited { background-color: white; }

.wizlet.wizletVanilla .colletion-menu .collection .collection-item.active, .wizlet.wizletActionMenu .collection-menu .collection .collection-item.active { background-color: #232323; }

.wizlet.wizletVanilla .colletion-menu .collection .collection-item.active a, .wizlet.wizletActionMenu .collection-menu .collection .collection-item.active a { font-weight: bold; color: #ffffff; }

.wizlet.wizletVanilla .colletion-menu .collection .collection-item:hover, .wizlet.wizletActionMenu .collection-menu .collection .collection-item:hover { background-color: #FE41A5; }

.wizlet.wizletVanilla .colletion-menu .collection .collection-item:hover a, .wizlet.wizletActionMenu .collection-menu .collection .collection-item:hover a { color: #ffffff; }
