<?xml version="1.0" encoding="utf-8" ?>
<Action layout="../../../layout/tabsLayoutModalAssess">

  <!-- Tabs component -->
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{Header_Modal_Assess_Title}",
      swipeable: false,
      tabPanelID: "tabPanelModalAssess",
      tabs: [
          "!{Header_Modal_Assess_Tab1}",
          "!{Header_Modal_Assess_Tab2}"
      ],
      _activeTab: 1,
      activeTab: "Q_FAC_Navigation_Tab",
      activeTabByVal: false,
      scope: ["Q_FAC_Navigation_Tab"]
  }]]></Component>

  <Component type="AssessForm" customJS="true">
    <![CDATA[{
      templateInEvent: "html/assessForm.dot",
      css: "styles/assessForm.css",
      header: "!{}",
      animate: "fadeIn",
      class: "responsive-table _bigHeaders_ _firstColumnBold_ centered",
      table: {
        title: "Score_RolePlay_R1",
        body: "!{Header_Modal_Assess_Text}",
        headers: { 
          user: "!{Header_Modal_Assess_Head1}",
          vote: "!{Header_Modal_Assess_Head2}", 
          value: "!{Header_Modal_Assess_Head3}" 
        },
        rows: [
          { user: "ge_sim1", vote:"Score_RolePlay_R1", value: "0" },
          { user: "ge_sim2", vote:"Score_RolePlay_R1", value: "0" },
          { user: "ge_sim3", vote:"Score_RolePlay_R1", value: "0" },
          { user: "ge_sim4", vote:"Score_RolePlay_R1", value: "0" },
          { user: "ge_sim5", vote:"Score_RolePlay_R1", value: "0" },
          { user: "ge_sim6", vote:"Score_RolePlay_R1", value: "0" },
          { user: "ge_sim7", vote:"Score_RolePlay_R1", value: "0" },
          { user: "ge_sim8", vote:"Score_RolePlay_R1", value: "0" },
          { user: "ge_sim9", vote:"Score_RolePlay_R1", value: "0" },
          { user: "ge_sim10", vote:"Score_RolePlay_R1", value: "0" }
        ]
      } ,
      submitBtn: {
        label: "!{Header_Modal_Assess_Submit}",
        toastDuration: "4e3"
      },
      onclose: {
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD"
      }   
    }]]>
  </Component>
  

  <Component type="AssessForm" customJS="true">
    <![CDATA[{
      templateInEvent: "html/assessForm.dot",
      css: "styles/assessForm.css",
      header: "!{}",
      animate: "fadeIn",
      class: "responsive-table _bigHeaders_ _firstColumnBold_ centered",
      table: {
        title: "Score_RolePlay_R2",
        body: "!{Header_Modal_Assess_Text}",
        headers: { 
          user: "!{Header_Modal_Assess_Head1}",
          vote: "!{Header_Modal_Assess_Head2}", 
          value: "!{Header_Modal_Assess_Head3}" 
        },
        rows: [
          { user: "ge_sim1", vote:"Score_RolePlay_R2", value: "0" },
          { user: "ge_sim2", vote:"Score_RolePlay_R2", value: "0" },
          { user: "ge_sim3", vote:"Score_RolePlay_R2", value: "0" },
          { user: "ge_sim4", vote:"Score_RolePlay_R2", value: "0" },
          { user: "ge_sim5", vote:"Score_RolePlay_R2", value: "0" },
          { user: "ge_sim6", vote:"Score_RolePlay_R2", value: "0" },
          { user: "ge_sim7", vote:"Score_RolePlay_R2", value: "0" },
          { user: "ge_sim8", vote:"Score_RolePlay_R2", value: "0" },
          { user: "ge_sim9", vote:"Score_RolePlay_R2", value: "0" },
          { user: "ge_sim10", vote:"Score_RolePlay_R2", value: "0" }
        ]
      } ,
      submitBtn: {
        label: "!{Header_Modal_Assess_Submit}",
        toastDuration: "4e3"
      }   
    }]]>
  </Component>
  



</Action>