@charset "UTF-8";
.wizlet.wizletCardReveal .container {
  padding: 0;
}
.wizlet.wizletCardReveal .row.no-padding {
  margin: 10px -10px;
}
.wizlet.wizletCardReveal .row.no-padding > .col {
  padding: 0 5px;
}
.wizlet.wizletCardReveal .row.no-padding > .col > .card {
  margin: 5px 0;
}
.wizlet.wizletCardReveal .row.is-flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.wizlet.wizletCardReveal .row.is-flex > .col {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: vertical;
  -moz-flex-direction: vertical;
  -ms-flex-direction: column;
  -webkit-flex-direction: column;
  flex-direction: column;
  margin: auto auto;
}
.wizlet.wizletCardReveal .container > .header-container {
  position: relative;
}
.wizlet.wizletCardReveal .container > .header-container .header.help {
  padding-right: 36px;
}
.wizlet.wizletCardReveal .container > .header-container i.help {
  position: absolute;
  right: 0;
  bottom: 0;
  -webkit-transform: translate(-50%, 0%);
      -ms-transform: translate(-50%, 0%);
       -o-transform: translate(-50%, 0%);
          transform: translate(-50%, 0%);
  cursor: pointer;
}
.wizlet.wizletCardReveal .container > .header-container i.help:hover {
  -webkit-transform: scale(1.1) translate(-50%, 0%);
      -ms-transform: scale(1.1) translate(-50%, 0%);
       -o-transform: scale(1.1) translate(-50%, 0%);
          transform: scale(1.1) translate(-50%, 0%);
}
.wizlet.wizletCardReveal .container > .intro {
  padding-bottom: 1rem;
  margin-top: 1rem;
  background-color: #FFFFFF;
}
.wizlet.wizletCardReveal .container > .intro p {
  font-size: inherit;
}
.wizlet.wizletCardReveal .collapsible {
  background-color: #FFFFFF;
}
.wizlet.wizletCardReveal .collapsible .collapsible-body {
  padding: 1rem;
  text-align: left;
}
.wizlet.wizletCardReveal .collapsible .collapsible-body p {
  font-size: inherit;
}
.wizlet.wizletCardReveal .card.maincard .badge.header-label {
  z-index: 2;
  position: absolute;
  left: 0;
  top: 0;
  background-color: #cc4a3d;
  min-height: 2rem;
  font-size: 1.5rem;
  line-height: 2rem;
  margin: 0;
}
.wizlet.wizletCardReveal .card.maincard .card-image {
  min-width: 30%;
  max-height: 75%;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}
.wizlet.wizletCardReveal .card.maincard .card-action a {
  font-family: clientBold, Arial;
  cursor: pointer;
  color: #cc4a3d !important;
  font-size: 20px;
}
.wizlet.wizletCardReveal .card.maincard .card-action a:hover {
  color: #cc4a3d !important;
  text-decoration: underline;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletCardReveal .card.maincard .card-action a {
    font-size: 16px;
  }
}
.wizlet.wizletCardReveal .card.maincard .card-content.card-content,
.wizlet.wizletCardReveal .card.maincard .card-reveal.card-content {
  background-color: #FFFFFF;
  border-radius: 10px;
}
.wizlet.wizletCardReveal .card.maincard .card-content .card-title,
.wizlet.wizletCardReveal .card.maincard .card-reveal .card-title {
  font-family: clientBolder, clientBold, Arial;
  font-size: 100%;
  line-height: 100%;
  text-align: left;
  padding-bottom: 5px;
  border-bottom: 3px solid #cc4a3d;
  margin-bottom: 15px;
}
.wizlet.wizletCardReveal .card.maincard .card-content .card-title.activator, .wizlet.wizletCardReveal .card.maincard .card-content .card-title.reveal,
.wizlet.wizletCardReveal .card.maincard .card-reveal .card-title.activator,
.wizlet.wizletCardReveal .card.maincard .card-reveal .card-title.reveal {
  position: relative;
}
.wizlet.wizletCardReveal .card.maincard .card-content .card-title.activator i.material-icons.right, .wizlet.wizletCardReveal .card.maincard .card-content .card-title.reveal i.material-icons.right,
.wizlet.wizletCardReveal .card.maincard .card-reveal .card-title.activator i.material-icons.right,
.wizlet.wizletCardReveal .card.maincard .card-reveal .card-title.reveal i.material-icons.right {
  position: absolute;
  top: 0;
  right: -5px;
}
.wizlet.wizletCardReveal .card.maincard .card-content .card-title.size.big,
.wizlet.wizletCardReveal .card.maincard .card-reveal .card-title.size.big {
  font-size: 120%;
}
.wizlet.wizletCardReveal .card.maincard .card-content .card-title.size.small,
.wizlet.wizletCardReveal .card.maincard .card-reveal .card-title.size.small {
  font-size: 90%;
}
.wizlet.wizletCardReveal .card.maincard .card-reveal {
  padding-top: 48px;
}
.wizlet.wizletCardReveal .card.maincard .card-content {
  padding: 5px 12px;
  overflow: auto;
}
.wizlet.wizletCardReveal .card.maincard .card-content .card-title {
  border-bottom: none;
  line-height: 2rem;
  word-break: break-word;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletCardReveal .card.maincard .card-content .card-title {
    font-size: 20px;
  }
}
.wizlet.wizletCardReveal .card.maincard .card-action {
  padding: 8px 12px;
  cursor: pointer;
}
.wizlet.wizletCardReveal .card.maincard.sticky-action .card-reveal {
  opacity: 0.95;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
  filter: alpha(opacity=95);
  zoom: 1;
}
.wizlet.wizletCardReveal .card.maincard.sticky-action .card-reveal p, .wizlet.wizletCardReveal .card.maincard.sticky-action .card-reveal ul {
  font-size: inherit;
}
.wizlet.wizletCardReveal .card.maincard.disabled {
  pointer-events: none;
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.wizlet.wizletCardReveal .card.maincard.no-reveal .card-content .card-title {
  text-align: center;
}
.wizlet.wizletCardReveal .card.maincard.no-reveal .card-image, .wizlet.wizletCardReveal .card.maincard.no-reveal .card-content {
  cursor: pointer;
}
.wizlet.wizletCardReveal .card.maincard.no-reveal .card-image.onHover:hover {
  -webkit-filter: saturate(200%);
          filter: saturate(200%);
}