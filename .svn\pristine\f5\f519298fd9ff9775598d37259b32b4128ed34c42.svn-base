.wizlet.wizletVanilla .card.video .card-content.card-content {
  background-color: #FFFFFF;
  border-radius: 10px;
}
.wizlet.wizletVanilla .card.video .card-content .card-title {
  font-family: clientBolder, clientBold, Arial;
  font-size: 150%;
  line-height: 100%;
  text-align: left;
  padding-bottom: 5px;
  border-bottom: 3px solid #cc4a3d;
  margin-bottom: 15px;
}
.wizlet.wizletVanilla .card.video .card-content .card-title.activator, .wizlet.wizletVanilla .card.video .card-content .card-title.reveal {
  position: relative;
}
.wizlet.wizletVanilla .card.video .card-content .card-title.activator i.material-icons.right, .wizlet.wizletVanilla .card.video .card-content .card-title.reveal i.material-icons.right {
  position: absolute;
  top: 0;
  right: -5px;
}
@media only screen and (max-width : 992px) {
  .wizlet.wizletVanilla .card.video .card-content {
    padding: 12px;
  }
}
.wizlet.wizletVanilla .card.video .card-content .video-box {
  padding-top: 12px;
  text-align: center;
}
.wizlet.wizletVanilla .card.video .card-content .video-box video.responsive-video {
  width: 100%;
  max-width: 80vw;
  max-height: 60vh;
}
.wizlet.wizletVanilla .card.video .card-image {
  max-width: 25%;
  padding: 10px;
  min-width: 40%;
  max-width: 60%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}
@media only screen and (min-width : 601px) {
  .wizlet.wizletVanilla .card.video .card-image {
    min-width: 20%;
    max-width: 30%;
  }
}
.wizlet.wizletVanilla .card.video .card-image img {
  margin: auto;
}