<?xml version="1.0" encoding="utf-8" ?>
<Action>


  <Aggregator doneText="">
    <ParticipantFilter>
        <Question>Filter_Agg</Question>
    </ParticipantFilter>


    <!-- KPI: TOTAL -->
    <Total result="Score_SIM_Total" method="avg">
      <Question validate="false">Score_SIM_Total_KPI1</Question>
      <Question validate="false">Score_SIM_Total_KPI2</Question>
      <Question validate="false">Score_SIM_Total_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_Rank" method="samerank">
        <Question validate="false">Score_SIM_Total</Question>
    </Total>
    
    <Total result="Score_SIM_Total_R1_KPI1" method="sum">
      <Question validate="false">Score_SIM_Total_KPI1</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI2" method="sum">
      <Question validate="false">Score_SIM_Total_KPI2</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_KPI3" method="sum">
      <Question validate="false">Score_SIM_Total_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_R1" method="sum">
      <Question validate="false">Score_SIM_Total</Question>
    </Total>
    <Total result="Score_SIM_Total_R1_Rank" method="samerank">
        <Question validate="false">Score_SIM_Total</Question>
    </Total>

  </Aggregator>



  <UpdateParticipant currentActionControl="ForemanOrDirector" initialCurrentAction="SIM_R1_Finish" />

</Action>