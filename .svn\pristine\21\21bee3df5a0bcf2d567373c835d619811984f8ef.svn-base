@charset "UTF-8";

@import "../materialize-src/sass/components/variables";

//Shared styles
@mixin card-title ($size: 150%, $line: 100%) {  

    &.card-content {
        background-color: color("client-colors", "white");
        border-radius: $card-panel-border;
    }

    .card-title { 
        font-family: clientBolder, clientBold, Arial;
        font-size: $size;
        line-height: $line;
        text-align: left;
        padding-bottom: 5px;
        border-bottom: 3px solid color("client-colors", "border");
        margin-bottom: 15px;
        
        &.activator,
        &.reveal {
            position: relative;
            i.material-icons.right {
                position: absolute;
                top: 0;
                right: -5px;
            }
        }

    }
    // p, ul {
    //     text-align: justify;
    // }
}


@mixin card-border ($width: 10px, $style: solid, $margin: "bottom") {  
    border-width: $width;
    border-color: color("client-colors", "border");
    border-radius: $card-panel-border;
    &.top { border-top-style: $style; }
    &.right { border-right-style: $style; }
    &.left { border-left-style: $style; }
    &.bottom { border-bottom-style: $style; @if $margin == "bottom" { margin-bottom: 50px; } }
    float: none !important;
}

@mixin header-help () {  
    .header-container {
        position: relative;

        .header.help {
            padding-right: 36px;
        }
        i.help {
            position: absolute;
            right: 0;
            bottom: 0;
            transform: translate(-50%, 0%);

            cursor: pointer;
            &:hover {
                transform: scale(1.1) translate(-50%, 0%);
            }
        }
    }
}



//Custom tooltip box to a speech bubble

@mixin tooltip-booble () { 
    .material-tooltip.timage {

        $bg-color: rgba(color("client-colors", "secondary"), 0.9);
        
        @media #{$medium-and-up} {
            max-width: 45% !important;
        }
        &.large {
            max-width: 96% !important;
        }
        padding: 5px 20px 20px;
        border-radius: 10px;
        background: $bg-color;
        overflow: visible;
        font-size: inherit;
        text-align: initial;
        @media #{$small-and-down} {
            font-size: 1rem;
        }

        .tooltip-content,
        .tooltip-content li {
            font-size: 95%;
            line-height: 100%;
        }
        .tooltip-content {
            @media #{$medium-and-up} {
                font-size: 100%;
            }
            @media #{$large-and-up} {
                font-size: 110%;
            }
            @media #{$extra-large-and-up} {
                font-size: 120%;
            }
        }
        
        &:after {
            position: absolute;
            content: " ";
            margin-left: -8px;
            width: 0;
            height: 0;
            border-style: solid;

        }
        
        &.bottom:after {
            top: 0%;
            left: 50%;
            border-width: 0 13px 15px 13px;
            border-color: transparent transparent $bg-color transparent;
            margin-top: -14px;
        }

        &.top:after {
            top: 100%;
            left: 50%;
            border-width: 15px 13px 0 13px;
            border-color: $bg-color transparent transparent transparent;
            margin-top: -1px;
        }

        &.right:after {
            top: 25%;
            left: 0%;
            border-width: 13px 15px 13px 0;
            border-color: transparent $bg-color transparent transparent;
            margin-left: -14px;
        }

        &.left:after {
            top: 25%;
            left: 100%;
            border-width: 13px 0 13px 15px;
            border-color: transparent transparent transparent $bg-color;
            margin-left: -1px;
        }        
    }
}

@mixin side-image () {
    .side-image {
        padding: 0;
        text-align: center;
        margin-top: 10px;
        span.title {
            min-height: 5%;
            color: color("client-colors", "font");
            font-family: clientBold, Arial;
            font-size: 100%;
        }
        img {
            height: 90%;
            width: 100%;
        }   
    }
}

@mixin header-badge () {  
    .header {
        $badge: 2rem;
        &.with-label {
            position: relative;
            padding-left: $badge * 1.25;
            margin-bottom: 1rem;

            .badge.header-label {
                position: absolute;
                left: 0;
                top: 0;
                background-color: color("client-colors", "secondary");
                min-width: $badge;
                min-height: $badge;
                font-size: 1.25rem;
                font-weight: bold;
                border-radius: 50%;
                line-height: $badge;
                margin: 0;
                margin-top: -5px;
            }
        }
    }
}


@mixin modal-open ($size: 150%) {  
    
    .modal {
        &.open {
            
            &::-webkit-scrollbar-track {
                @include box-shadow (inset 0 0 6px rgba(0,0,0,0.3)); 
                @include border-radius (10px);
            }
            
            &::-webkit-scrollbar-thumb {
                @include box-shadow (inset 0 0 6px rgba(0,0,0,0.5));
                @include border-radius (10px);

            }
    
            // text-align: justify;
            top: 50% !important;
            transform: translateY(-50%) !important;

            &.large {
                //top: 5% !important;
                width: 90%;
                @media #{$large-and-up} { width: 80%; }
                @media #{$extra-large-and-up} { width: 70%; }
                max-height: 90%;
            }
    
            &.glossary {
                .modal-content .collapsible-body {
                        font-size: 1rem;
                }
            }
            
            .modal-content .container {
                width: 98% !important;
            }

            +.modal-overlay {
                opacity: 0.8 !important;
            }
        }

        .modal-footer {
            width: auto;
            clear: both;
        }

        &, .modal-footer {
            background-color: color("client-colors", "background");
        }
    }

}

@mixin row-submit () {
    .row.submit {
        padding-top: 5px;
        margin-right: 0;
        text-align: right;

        #checkBtn[hidden] {
            display: inline-block !important;
            visibility: hidden;
        }

        @media #{$small-and-down} {
            .btn {

                font-size: 0;
                width: $button-floating-size;
                height: $button-floating-size;
                line-height: $button-floating-size;
                i {
                  width: inherit;
                  display: inline-block;
                  text-align: center;
                  color: $button-floating-color;
                  font-size: $button-large-icon-font-size;
                  line-height: $button-floating-size;
                }
            }
        }
    }
}

@mixin data-table-style () {

        
    .dataTables_wrapper {
        
        $color: color("client-colors", "border");
        $color2: rgba(color("client-colors", "secondary"), 0.15);
        $color3: color("client-colors", "primary");

        input {
            width: auto;
        }
        
        .select-wrapper {
            display: inline-block;
            margin-left: 10px;
        }

        .dataTables_info {
            font-size: 0.8rem;
            color: color("client-colors", "font3");
            padding: 10px 0;
        }

        .dataTables_paginate {
            padding: 10px 0;

            .paginate_button {
                width: 45px;
                height: 45px;
                text-align: center;
                line-height: 45px;
                padding: 0;
                display: inline-block;
                color: #fff;
                position: relative;
                overflow: hidden;
                border-radius: 50%;
                border: none !important;
                -webkit-transition: background-color .3s;
                transition: background-color .3s;
                cursor: pointer;
                vertical-align: middle;
                background: none;

                &:hover {
                    background-color: $color;
                }
                
                &.current {
                    pointer-events: none;
                    color: #fff !important;
                    background-color: $color3;
                }

                &.previous,
                &.next {
                    background-color: $color;
                    line-height: 55px;
                    &:hover {
                        background-color: $color3;
                    }

                    i {
                        color: #fff;
                    }
                }
                
                &.disabled  {
                    pointer-events: none;
                    background-color: color("client-colors", "grey");
                }
            }
        }
    }
        
    &.hideControls .dataTables_wrapper {

        .dataTables_length,
        .dataTables_filter,
        .dataTables_info,
        .dataTables_paginate  {
            display: none;
        }

    }
    

}

@mixin vendor ($prop, $args) {
  -webkit-#{$prop}: args;
  -moz-#{$prop}: args;
  -ms-#{$prop}: args;
  #{$prop}: args;
}

@mixin no-select-highlight () {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

//Mixin methods
@mixin flexbox () {
    display: -webkit-box;
    display: -moz-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

@mixin flex-wrap () {
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

@mixin flex-direction ($direction) {
    @if $direction == column {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;

        -webkit-flex-direction:vertical;
        -moz-flex-direction:vertical;
        -ms-flex-direction:column;
        -webkit-flex-direction:column;
        flex-direction:column;
    } @else {
        -webkit-flex-direction:horizontal;
        -moz-flex-direction:horizontal;
        -ms-flex-direction:row;
        -webkit-flex-direction:row;
        flex-direction:row;
    }
}

@mixin box-shadow ($value) {
    -webkit-box-shadow: $value;
    -moz-box-shadow: $value;
    box-shadow: $value;
}
@mixin box-shadow2 ($value1, $value2) {
    -webkit-box-shadow: $value1, $value2;
    -moz-box-shadow: $value1, $value2;
    box-shadow: $value1, $value2;
}

@mixin border-radius ($value: 3px) {
    -webkit-border-radius: $value;
    -moz-border-radius: $value;
    border-radius: $value;
    // keeps background from busting out of border 
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
}

@mixin opacity ($value: 0.5) {
    opacity: $value;
    -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)';
    filter: alpha(opacity= $value * 100 );
    zoom: 1;
}

@mixin truncate-line () {    
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

@mixin font-feature-settings ($font) {
    -webkit-font-feature-settings: $font;
    font-feature-settings: $font;
}

@mixin font-smoothing () {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}


@mixin vertical-align-middle () {
    display: inline-block;
    vertical-align: sub;
    // vertical-align: -webkit-baseline-middle;
    // vertical-align: -moz-middle-with-baseline;
}

@mixin transform($transforms) {
    -moz-transform: $transforms;
    -o-transform: $transforms;
    -ms-transform: $transforms;
    -webkit-transform: $transforms;
    transform: $transforms;
}
@mixin transform-origin($transforms) {
    -moz-transform-origin: $transforms;
    -o-transform-origin: $transforms;
    -ms-transform-origin: $transforms;
    -webkit-transform-origin: $transforms;
    transform-origin: $transforms;
}

@mixin scroll-y-hidden() {
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 0 !important
    }
    -ms-overflow-style: none;
}

@mixin scroll-x-hidden() {
    overflow-x: auto;
    &::-webkit-scrollbar {
      height: 0 !important
    }
    -ms-overflow-style: none;
}


@mixin border-chat-corner($side: 'left', $color:white) {

    &:before {
        position: absolute;
        top: 0;
        @if $side == 'left' {
            border-width: 0 10px 10px 0;
            border-color: transparent $color transparent transparent;
            left: -9px;        
        } @else {
            border-width: 10px 10px 0 0;
            border-color: $color transparent transparent transparent;
            right: -9px;
        }
    }
}

//Dynamic repeat colors in a list of elements
@mixin dynamic-colors($element, $colors...) {
    $colorsLength: length($colors);
    @for $i from 1 through $colorsLength {
        &:nth-of-type(#{$colorsLength}n+#{$i}) {
            #{$element} { background-color: nth($colors, $i); }
        }
    }  
}

@mixin dynamic-stroke-colors($colors...) {
    $colorsLength: length($colors);
    @for $i from 1 through $colorsLength {
        &:nth-of-type(#{$colorsLength}n+#{$i}) {
            stroke: nth($colors, $i);
        }
    }  
}

@mixin dynamic-border-chat-corner-colors($element, $colors...) {
    $colorsLength: length($colors);
    @for $i from 1 through $colorsLength {
        &:nth-of-type(#{$colorsLength}n+#{$i}) {
            #{$element}:before {
                    border-color: transparent nth($colors, $i) transparent transparent;
            }
            &.me {
                #{$element}:before {
                    border-color: nth($colors, $i) transparent transparent transparent;
                }
            }
        }
    }  

}



//Degraded effect in items from a list from darker to lighter
@mixin degrade-li-items($elements, $color) {
    @for $i from 0 to $elements {
        li:nth-child(#{$i + 1}) {
            background-color: lighten($color, percentage($i/($elements*2))) !important;
        }
    }
    li:nth-child(#{$elements + 1}) {
        background-color: lighten($color, percentage(($elements - 1)/($elements*2))) !important;
    }
}


@mixin linear-gradient($from, $to) {
    background: $to;
    background: -moz-linear-gradient(top, $from 0%, $to 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,$from), color-stop(100%,$to));
    background: -webkit-linear-gradient(top, $from 0%,$to 100%);
    background: -o-linear-gradient(top, $from 0%,$to 100%);
    background: ms-linear-gradient(to bottom, $from 0%,$to 100%);
    background: linear-gradient(to bottom, $from 0%,$to 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#{$from}, endColorstr=#{$to})
  }
  
  @mixin linear-gradient2($deg, $from, $to, $max) {
    background: $to;
    background: -moz-linear-gradient($deg, $from 0%, $to $max);
    background: -webkit-gradient(linear, left top, right bottom, color-stop(0%,$from), color-stop($max,$to));
    background: -webkit-linear-gradient($deg, $from 0%,$to $max);
    background: -o-linear-gradient($deg, $from 0%,$to $max);
    background: ms-linear-gradient($deg, $from 0%,$to $max);
    background: linear-gradient($deg, $from 0%,$to $max);
    filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#{$from}, endColorstr=#{$to})
  }
  
  @mixin linear-gradient-stops3($deg, $color1, $stop1, $color2, $stop2, $color3, $stop3) {
    background: $color1;
    background: -moz-linear-gradient($deg, $color1 $stop1, $color2 $stop2, $color3 $stop3);
    background: -webkit-gradient(linear, left bottom, right top, color-stop($stop1,$color1), color-stop($stop2,$color2), color-stop($stop3,$color3));
    background: -webkit-linear-gradient($deg, $color1 $stop1, $color2 $stop2, $color3 $stop3);
    background: -o-linear-gradient($deg, $color1 $stop1, $color2 $stop2, $color3 $stop3);
    background: ms-linear-gradient($deg, $color1 $stop1, $color2 $stop2, $color3 $stop3);
    background: linear-gradient($deg, $color1 $stop1, $color2 $stop2, $color3 $stop3);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=$color1, endColorstr=$color3,GradientType=1 );
  }

  @mixin linear-gradient-stops4($deg, $color1, $stop1, $color2, $stop2, $color3, $stop3, $color4, $stop4) {
    background: $color1;
    background: -moz-linear-gradient($deg, $color1 $stop1, $color2 $stop2, $color3 $stop3, $color4 $stop4);
    background: -webkit-gradient(linear, left bottom, right top, color-stop($stop1,$color1), color-stop($stop2,$color2), color-stop($stop3,$color3), color-stop($stop4,$color4));
    background: -webkit-linear-gradient($deg, $color1 $stop1, $color2 $stop2, $color3 $stop3, $color4 $stop4);
    background: -o-linear-gradient($deg, $color1 $stop1, $color2 $stop2, $color3 $stop3, $color4 $stop4);
    background: ms-linear-gradient($deg, $color1 $stop1, $color2 $stop2, $color3 $stop3, $color4 $stop4);
    background: linear-gradient($deg, $color1 $stop1, $color2 $stop2, $color3 $stop3, $color4 $stop4);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=$color1, endColorstr=$color4,GradientType=1 );
  }
  
  @mixin radial-gradient-stops3($mode, $color1, $stop1, $color2, $stop2, $color3, $stop3) {
    background: $color1;
    background: -moz-radial-gradient(center, $mode farthest-corner, $color1 $stop1, $color2 $stop2, $color3 $stop3);
    background: -webkit-gradient(radial, center center, 0px, center center, 100%, color-stop($stop1,$color1), color-stop($stop2,$color2), color-stop($stop3,$color3));
    background: -webkit-radial-gradient(center, $mode farthest-corner, $color1 $stop1, $color2 $stop2, $color3 $stop3);
    background: -o-radial-gradient(center, $mode farthest-corner, $color1 $stop1, $color2 $stop2, $color3 $stop3);
    background: -ms-radial-gradient(center, $mode farthest-corner, $color1 $stop1, $color2 $stop2, $color3 $stop3);    
    background: radial-gradient($mode at center, $color1 $stop1, $color2 $stop2, $color3 $stop3);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=$color1, endColorstr=$color3,GradientType=1 );
  }

@mixin keyframes ($animationName) {
    @-webkit-keyframes #{$animationName} {
        @content;    
    }
    @-moz-keyframes #{$animationName} {
        @content;
    }
    @-ms-keyframes #{$animationName} {
        @content;
    }
    @-o-keyframes #{$animationName} {
        @content;
    }
    @keyframes #{$animationName} {
        @content;
    }
}


@mixin animation-name($name...) {
    -o-animation-name: $name;
    -moz-animation-name: $name;
    -ms-animation-name: $name;
    -webkit-animation-name: $name;
    animation-name: $name;
}
@mixin animation-duration($duration...) {
    -o-animation-duration: $duration;
    -moz-animation-duration: $duration;
    -ms-animation-duration: $duration;
    -webkit-animation-duration: $duration;
    animation-duration: $duration;
}
@mixin animation-timing-function($timing...) {
    -o-animation-timing-function: $timing;
    -moz-animation-timing-function: $timing;
    -ms-animation-timing-function: $timing;
    -webkit-animation-timing-function: $timing;
    animation-timing-function: $timing;
}
@mixin animation-delay($delay...) {
    -o-animation-delay: $delay;
    -moz-animation-delay: $delay;
    -ms-animation-delay: $delay;
    -webkit-animation-delay: $delay;
    animation-delay: $delay;
}
@mixin animation-iteration-count($count...) {
    -o-animation-iteration-count: $count;
    -moz-animation-iteration-count: $count;
    -ms-animation-iteration-count: $count;
    -webkit-animation-iteration-count: $count;
    animation-iteration-count: $count;
}
@mixin animation-direction($direction...) {
    -o-animation-direction: $direction;
    -moz-animation-direction: $direction;
    -ms-animation-direction: $direction;
    -webkit-animation-direction: $direction;
    animation-direction: $direction;
}
@mixin animation-fill-mode($fill...) {
    -o-animation-fill-mode: $fill;
    -moz-animation-fill-mode: $fill;
    -ms-animation-fill-mode: $fill;
    -webkit-animation-fill-mode: $fill;
    animation-fill-mode: $fill;
}