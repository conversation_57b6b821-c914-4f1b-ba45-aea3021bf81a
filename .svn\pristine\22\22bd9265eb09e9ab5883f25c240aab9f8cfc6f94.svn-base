<?xml version="1.0" encoding="utf-8" ?>
<Action>

  <!-- ************************************************************ -->
  <!-- *********************** AGGREGATION  *********************** -->
  <!-- ************************************************************ -->

  <Aggregator doneText="">
    <ParticipantFilter>
        <Question>Filter_Agg</Question>
    </ParticipantFilter>

    <!-- KPI1 -->
    <Total result="Score_SIM_Total_KPI1" method="sum">
      <Question validate="false">Score_SIM_Init_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI1</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI1</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R3_Scenario2_KPI1</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R4_Scenario2_KPI1</Question>
      <Question validate="false">Score_SIM_R5_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R5_Scenario2_KPI1</Question>
    </Total>
    
    <!-- KPI2 -->
    <Total result="Score_SIM_Total_KPI2" method="sum">
      <Question validate="false">Score_SIM_Init_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI2</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI2</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R3_Scenario2_KPI2</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R4_Scenario2_KPI2</Question>
      <Question validate="false">Score_SIM_R5_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R5_Scenario2_KPI2</Question>
    </Total>
    
    <!-- KPI3 -->
    <Total result="Score_SIM_Total_KPI3" method="sum">
      <Question validate="false">Score_SIM_Init_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R1_Scenario2_KPI3</Question>
      <Question validate="false">Score_SIM_R2_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R2_Scenario2_KPI3</Question>
      <Question validate="false">Score_SIM_R3_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R3_Scenario2_KPI3</Question>
      <Question validate="false">Score_SIM_R4_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R4_Scenario2_KPI3</Question>
      <Question validate="false">Score_SIM_R5_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R5_Scenario2_KPI3</Question>
    </Total>


    <!-- KPI: TOTAL -->
    <Total result="Score_SIM_Total" method="avg">
      <Question validate="false">Score_SIM_Total_KPI1</Question>
      <Question validate="false">Score_SIM_Total_KPI2</Question>
      <Question validate="false">Score_SIM_Total_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_Rank" method="samerank">
        <Question validate="false">Score_SIM_Total</Question>
    </Total>
    
    <Total result="Score_SIM_Total_R1_Rank" method="samerank">
        <Question validate="false">Score_SIM_Total_R1</Question>
    </Total>
    <Total result="Score_SIM_Total_R2_Rank" method="samerank">
        <Question validate="false">Score_SIM_Total_R2</Question>
    </Total>
    <Total result="Score_SIM_Total_R3_Rank" method="samerank">
        <Question validate="false">Score_SIM_Total_R3</Question>
    </Total>
    <Total result="Score_SIM_Total_R4_Rank" method="samerank">
        <Question validate="false">Score_SIM_Total_R4</Question>
    </Total>
    <Total result="Score_SIM_Total_R5_Rank" method="samerank">
        <Question validate="false">Score_SIM_Total_R5</Question>
    </Total>

  </Aggregator>


</Action>