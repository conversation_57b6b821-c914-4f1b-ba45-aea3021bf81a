<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutLanding_FAC_R5">
  <Include name="Header_IntroFAC_R5"></Include>

  <Component type="Vanilla"><![CDATA[{
    templateInEvent: "html/landingPage.dot",
    css: "styles/landingPage.css",
    fullScreen: true,
    facilitator: true,
    content: {
      title: "!{}",
      subtitle: "!{}",
      image: "!{LandingPage_ImgFac}"
    }
  }]]></Component>


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - showDelay: show the hidden button after Xms (waiting the animation)
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "center",
      animate: "zoomIn animate__delay-4s",
      id: "btn_navigation_home",
      _isHidden: true, _showDelay: 3000,
      hasModals: true,
      buttons: [
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list",
          isFloat: false, 
          tooltip: "!{}"
        },
        {
          type: "target",
          pulse: false,
          isLarge: false,
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R5_Start",
          targetSection: "SIM_R5_FAC_dashboard",
          label: "!{GD_SIM_R5_Start}",
          icon: "looks_two"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>



  <!-- Reset all participant's votes when landing -->
  <Component type="myCode_reset" customJS="true"><![CDATA[{
      clearAll: true,
      resetVotes: false
  }]]></Component> 

  
  <!-- EXPORT CSV BUTTON -->
  <Component type="ActionButton" customJS="true">
    <![CDATA[{
      templateInEvent: "html/actionButton.dot",
      css: "styles/actionButton.css",
      animate: "zoomIn animate__delay-3s",
      id: "btn_export",
      _isHidden: true, _showDelay: 3000,
      title: "!{GroupDirector_ExportBtn_R1}",
      icon: "cloud_download",
      onclick: "",
      pulse: true,
      color: "aux1",
      modal: {
        modalID: "modal-export",
        header: "!{GroupDirector_Export_Modal_Title}",
        text: "!{GroupDirector_Export_Modal_Text}",
        close: "!{GroupDirector_Export_Modal_Close}",
        action: "!{GroupDirector_Export_Modal_Action}",
        onclick: "",
        onclickFunction: "dataExport",
        onclickQuestion: "GD",
        onclickQuestion2: "Data Report R1"
      },
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <Voting autoNext="false">
    <Score type="Vote" result="Q_FAC_Navigation_Tab" response="1"/> 
  </Voting>


</Action>