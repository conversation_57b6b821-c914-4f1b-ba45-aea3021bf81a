{
    "workbench.colorCustomizations": {
        "activityBar.activeBackground": "#d77065",
        "activityBar.activeBorder": "#b4fe61",
        "activityBar.background": "#d77065",
        "activityBar.foreground": "#15202b",
        "activityBar.inactiveForeground": "#15202b99",
        "activityBarBadge.background": "#1a6522",
        "activityBarBadge.foreground": "#e7e7e7",
        "statusBar.background": "#cc4a3d",
        "statusBar.foreground": "#e7e7e7",
        "statusBarItem.hoverBackground": "#d77065",
        "titleBar.activeBackground": "#cc4a3d",
        "titleBar.activeForeground": "#e7e7e7",
        "titleBar.inactiveBackground": "#cc4a3d99",
        "titleBar.inactiveForeground": "#e7e7e799",
        "sash.hoverBorder": "#d77065",
        "statusBarItem.remoteBackground": "#cc4a3d",
        "statusBarItem.remoteForeground": "#e7e7e7",
        "commandCenter.border": "#e7e7e799"
    },
    "peacock.color": "#cc4a3d",
    "liveSassCompile.settings.formats": [
        {
        "format": "expanded",
        // "format": "compact",
        "extensionName": ".css",
        // "savePath": null
        "savePath": "/styles/"
        }
    ],
    "liveSassCompile.settings.generateMap": false,
    "liveSassCompile.settings.autoprefix": [
        "> 1%",
        "last 3 version",
        "Chrome >= 35",
        "Firefox >= 38",
        "Edge >= 10",
        "Explorer >= 10",
        "ie >= 10",
        "iOS >= 8",
        "Safari >= 8",
        "Android 2.3",
        "Android >= 4",
        "Opera >= 12"
    ]
}