.wizlet.wizletFormInputs .card .card-image {
  min-width: 30%;
}
.wizlet.wizletFormInputs .card .card-image video {
  display: block;
  position: relative;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: black;
}
.wizlet.wizletFormInputs .card .card-content.card-content {
  background-color: #FFFFFF;
  border-radius: 10px;
}
.wizlet.wizletFormInputs .card .card-content .card-title {
  font-family: clientBolder, clientBold, Arial;
  font-size: 150%;
  line-height: 100%;
  text-align: left;
  padding-bottom: 5px;
  border-bottom: 3px solid #cc4a3d;
  margin-bottom: 15px;
}
.wizlet.wizletFormInputs .card .card-content .card-title.activator, .wizlet.wizletFormInputs .card .card-content .card-title.reveal {
  position: relative;
}
.wizlet.wizletFormInputs .card .card-content .card-title.activator i.material-icons.right, .wizlet.wizletFormInputs .card .card-content .card-title.reveal i.material-icons.right {
  position: absolute;
  top: 0;
  right: -5px;
}
.wizlet.wizletFormInputs .card .card-content .row {
  margin: 0;
}
.wizlet.wizletFormInputs .card .card-content form .input-field {
  min-height: 72px;
}
.wizlet.wizletFormInputs .card .card-content form input {
  cursor: pointer;
  font-size: inherit;
}
.wizlet.wizletFormInputs .card .card-content form input + label {
  pointer-events: none;
}
.wizlet.wizletFormInputs .card .card-content form input + label * {
  pointer-events: all;
}
.wizlet.wizletFormInputs .card .card-content form input:disabled {
  pointer-events: none;
}
.wizlet.wizletFormInputs .card .card-content form input[type=number] {
  -moz-appearance: textfield;
}
.wizlet.wizletFormInputs .card .card-content form input[type=number]::-webkit-outer-spin-button, .wizlet.wizletFormInputs .card .card-content form input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.wizlet.wizletFormInputs .card .card-content form .remaining {
  min-height: 18px;
  float: right;
  font-size: 12px;
}
.wizlet.wizletFormInputs .card .card-content form label:not(.active) ~ .remaining, .wizlet.wizletFormInputs .card .card-content form label:not(.active) ~ .character-counter {
  visibility: hidden;
}
.wizlet.wizletFormInputs .card .card-content form input.valid {
  -webkit-box-shadow: 0 1px 0 0;
          box-shadow: 0 1px 0 0;
}
.wizlet.wizletFormInputs .card .card-content form input.invalid {
  border-color: #fb054b;
  -webkit-box-shadow: 0 1px 0 0 #fb054b;
          box-shadow: 0 1px 0 0 #fb054b;
}
.wizlet.wizletFormInputs .card .card-content form input.invalid ~ .helper-text:after,
.wizlet.wizletFormInputs .card .card-content form input.invalid ~ label {
  color: #fb054b;
}
.wizlet.wizletFormInputs .card .card-content form .select-wrapper.valid > input {
  -webkit-box-shadow: 0 1px 0 0;
          box-shadow: 0 1px 0 0;
}
.wizlet.wizletFormInputs .card .card-content form .select-wrapper.invalid ~ .helper-text:after,
.wizlet.wizletFormInputs .card .card-content form .select-wrapper.invalid ~ label {
  color: #fb054b;
}
.wizlet.wizletFormInputs .card .card-content form .select-wrapper.invalid > input {
  border-color: #fb054b;
  -webkit-box-shadow: 0 1px 0 0 #fb054b;
          box-shadow: 0 1px 0 0 #fb054b;
}
.wizlet.wizletFormInputs .card .card-content form .select-wrapper input {
  padding-right: 15px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  text-overflow: ellipsis;
}
.wizlet.wizletFormInputs .card .card-content form .select-wrapper img.avatar {
  position: absolute;
  bottom: 5px;
  right: 25px;
  width: 36px;
}
.wizlet.wizletFormInputs .card .card-content form .select-wrapper ul.select-dropdown li span {
  color: #24466b;
}
.wizlet.wizletFormInputs .card .card-content .more_info {
  font-size: 1rem;
}
.wizlet.wizletFormInputs .card .card-image, .wizlet.wizletFormInputs .card .card-content {
  background-color: #FFFFFF;
}
.wizlet.wizletFormInputs .row.submit {
  padding-top: 5px;
  margin-right: 0;
  text-align: right;
}
.wizlet.wizletFormInputs .row.submit #checkBtn[hidden] {
  display: inline-block !important;
  visibility: hidden;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletFormInputs .row.submit .btn {
    font-size: 0;
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
  .wizlet.wizletFormInputs .row.submit .btn i {
    width: inherit;
    display: inline-block;
    text-align: center;
    color: #fff;
    font-size: 1.6rem;
    line-height: 40px;
  }
}