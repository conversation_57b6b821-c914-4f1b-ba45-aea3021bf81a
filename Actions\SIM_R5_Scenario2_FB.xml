<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayout_SIM_R5" layout="../../../layout/tabsLayout2">
  <Include name="Header_SIM_R5"></Include>
  <Include name="KPIgauges"></Include>
  
  
  <!-- Breadcrumbs progress -->
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/breadcrumbs.dot",
      css: "styles/breadcrumbs.css",
      animate: "fadeInLeft",
      progress: "2",
      steps: [ "!{SIM_BreadCrumbs_1}",
               "!{SIM_BreadCrumbs_2}",
               "!{SIM_BreadCrumbs_3}",
               "!{SIM_BreadCrumbs_4}",
               "!{SIM_BreadCrumbs_5}" ]
    }]]>
  </Component>

  
  
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{SIM_R5_Scenario2_FB_Header}",
      swipeable: true,
      tabs: [
          "!{Feedback_Opt} !{Choice_Opt1}",
          "!{Feedback_Opt} !{Choice_Opt2}",
          "!{Feedback_Opt} !{Choice_Opt3}"
      ],
      correctOption: 0,
      myText: "!{Feedback_MyOpt}",
      isCheck: false,
      bind: "Q_SIM_R5_Scenario2",
      
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower","Q_SIM_R5_Scenario2"]
  }]]></Component>


  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img: { 
          materialboxed: false, small: true,
          src: "!{SIM_R5_Scenario2_FB_Opt1_Img}",  alt: "!{}" 
        },
        title: "!{SIM_R5_Scenario2_Opt1}",
        body: "!{SIM_R5_Scenario2_FB_Opt1_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R5_Scenario2_Opt1_KPI1}", "!{SIM_R5_Scenario2_Opt1_KPI2}", "!{SIM_R5_Scenario2_Opt1_KPI3}"],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R5_Scenario2",
        value: 1
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R5_Scenario2"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img: { 
          materialboxed: false, small: true,
          src: "!{SIM_R5_Scenario2_FB_Opt2_Img}",  alt: "!{}" 
        },
        title: "!{SIM_R5_Scenario2_Opt2}",
        body: "!{SIM_R5_Scenario2_FB_Opt2_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R5_Scenario2_Opt2_KPI1}", "!{SIM_R5_Scenario2_Opt2_KPI2}", "!{SIM_R5_Scenario2_Opt2_KPI3}"],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R5_Scenario2",
        value: 2
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R5_Scenario2"]
    }]]>
  </Component>
  <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img: { 
          materialboxed: false, small: true,
          src: "!{SIM_R5_Scenario2_FB_Opt3_Img}",  alt: "!{}" 
        },
        title: "!{SIM_R5_Scenario2_Opt3}",
        body: "!{SIM_R5_Scenario2_FB_Opt3_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R5_Scenario2_Opt3_KPI1}", "!{SIM_R5_Scenario2_Opt3_KPI2}", "!{SIM_R5_Scenario2_Opt3_KPI3}"],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R5_Scenario2",
        value: 3
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R5_Scenario2"]
    }]]>
  </Component>
  <!-- <Component type="Card" customJS="true">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img: { 
          materialboxed: false, small: true,
          src: "!{SIM_R5_Scenario2_FB_Opt4_Img}",  alt: "!{}" 
        },
        title: "!{SIM_R5_Scenario2_Opt4}",
        body: "!{SIM_R5_Scenario2_FB_Opt4_Text}"
      },
      feedback_score: {
        title: "!{KPI_Title}",
        kpi_titles: ["!{KPI_Metric1}","!{KPI_Metric2}","!{KPI_Metric3}"],
        kpi_scores: ["!{SIM_R5_Scenario2_Opt4_KPI1}", "!{SIM_R5_Scenario2_Opt4_KPI2}", "!{SIM_R5_Scenario2_Opt4_KPI3}"],
        icon_right: "arrow_upward",
        icon_none: "",
        icon_wrong: "arrow_downward",
        bind: "Q_SIM_R5_Scenario2",
        value: 4
      },
      trackTeam: "Team",
      isFollower: "Follower",
      scope: ["Follower", "Q_SIM_R5_Scenario2"]
    }]]>
  </Component> -->


  


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "zoomIn animate__delay-5s",
      id: "navButton",
      _isHidden: true, _showDelay: "5000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          gdActionEmbed: "",
          gdActionTrack: "GD",
          gdActionSection: "",
          targetSection: "",
          label: "!{Navigation_next}",
          tooltip: "!{Navigation_next_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>





</Action>