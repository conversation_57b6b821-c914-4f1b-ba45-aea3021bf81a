.confetti-wrapper {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.confetti-wrapper [class|=confetti] {
  position: absolute;
}
.confetti-wrapper .confetti-0 {
  width: 4px;
  height: 1.6px;
  background-color: #8bc34a;
  top: -10%;
  left: 33%;
  opacity: 0.6116717593;
  -webkit-transform: rotate(221.2926360936deg);
      -ms-transform: rotate(221.2926360936deg);
       -o-transform: rotate(221.2926360936deg);
          transform: rotate(221.2926360936deg);
  -webkit-animation: drop-0 4.9055865286s 0.3222890474s infinite;
       -o-animation: drop-0 4.9055865286s 0.3222890474s infinite;
          animation: drop-0 4.9055865286s 0.3222890474s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-0 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@-o-keyframes drop-0 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@keyframes drop-0 {
  100% {
    top: 110%;
    left: 41%;
  }
}
.confetti-wrapper .confetti-1 {
  width: 1px;
  height: 0.4px;
  background-color: #ffeb3b;
  top: -10%;
  left: 22%;
  opacity: 0.6592150722;
  -webkit-transform: rotate(119.5978421933deg);
      -ms-transform: rotate(119.5978421933deg);
       -o-transform: rotate(119.5978421933deg);
          transform: rotate(119.5978421933deg);
  -webkit-animation: drop-1 4.2578667699s 0.4158819306s infinite;
       -o-animation: drop-1 4.2578667699s 0.4158819306s infinite;
          animation: drop-1 4.2578667699s 0.4158819306s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-1 {
  100% {
    top: 110%;
    left: 24%;
  }
}
@-o-keyframes drop-1 {
  100% {
    top: 110%;
    left: 24%;
  }
}
@keyframes drop-1 {
  100% {
    top: 110%;
    left: 24%;
  }
}
.confetti-wrapper .confetti-2 {
  width: 5px;
  height: 2px;
  background-color: #03a9f4;
  top: -10%;
  left: -9%;
  opacity: 0.9561123703;
  -webkit-transform: rotate(319.2462933498deg);
      -ms-transform: rotate(319.2462933498deg);
       -o-transform: rotate(319.2462933498deg);
          transform: rotate(319.2462933498deg);
  -webkit-animation: drop-2 4.4927803175s 0.1093282136s infinite;
       -o-animation: drop-2 4.4927803175s 0.1093282136s infinite;
          animation: drop-2 4.4927803175s 0.1093282136s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-2 {
  100% {
    top: 110%;
    left: 6%;
  }
}
@-o-keyframes drop-2 {
  100% {
    top: 110%;
    left: 6%;
  }
}
@keyframes drop-2 {
  100% {
    top: 110%;
    left: 6%;
  }
}
.confetti-wrapper .confetti-3 {
  width: 2px;
  height: 0.8px;
  background-color: #673ab7;
  top: -10%;
  left: 4%;
  opacity: 0.584419768;
  -webkit-transform: rotate(35.5354596121deg);
      -ms-transform: rotate(35.5354596121deg);
       -o-transform: rotate(35.5354596121deg);
          transform: rotate(35.5354596121deg);
  -webkit-animation: drop-3 4.0446702278s 0.3187747317s infinite;
       -o-animation: drop-3 4.0446702278s 0.3187747317s infinite;
          animation: drop-3 4.0446702278s 0.3187747317s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-3 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@-o-keyframes drop-3 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@keyframes drop-3 {
  100% {
    top: 110%;
    left: 9%;
  }
}
.confetti-wrapper .confetti-4 {
  width: 1px;
  height: 0.4px;
  background-color: #9c27b0;
  top: -10%;
  left: -4%;
  opacity: 0.925942132;
  -webkit-transform: rotate(196.066209326deg);
      -ms-transform: rotate(196.066209326deg);
       -o-transform: rotate(196.066209326deg);
          transform: rotate(196.066209326deg);
  -webkit-animation: drop-4 4.1032795888s 0.4693595065s infinite;
       -o-animation: drop-4 4.1032795888s 0.4693595065s infinite;
          animation: drop-4 4.1032795888s 0.4693595065s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-4 {
  100% {
    top: 110%;
    left: 7%;
  }
}
@-o-keyframes drop-4 {
  100% {
    top: 110%;
    left: 7%;
  }
}
@keyframes drop-4 {
  100% {
    top: 110%;
    left: 7%;
  }
}
.confetti-wrapper .confetti-5 {
  width: 8px;
  height: 3.2px;
  background-color: #3f51b5;
  top: -10%;
  left: 79%;
  opacity: 1.2480347862;
  -webkit-transform: rotate(259.0169891374deg);
      -ms-transform: rotate(259.0169891374deg);
       -o-transform: rotate(259.0169891374deg);
          transform: rotate(259.0169891374deg);
  -webkit-animation: drop-5 4.3349041133s 0.4927360507s infinite;
       -o-animation: drop-5 4.3349041133s 0.4927360507s infinite;
          animation: drop-5 4.3349041133s 0.4927360507s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-5 {
  100% {
    top: 110%;
    left: 91%;
  }
}
@-o-keyframes drop-5 {
  100% {
    top: 110%;
    left: 91%;
  }
}
@keyframes drop-5 {
  100% {
    top: 110%;
    left: 91%;
  }
}
.confetti-wrapper .confetti-6 {
  width: 4px;
  height: 1.6px;
  background-color: #009688;
  top: -10%;
  left: 78%;
  opacity: 1.0413352347;
  -webkit-transform: rotate(175.4388091367deg);
      -ms-transform: rotate(175.4388091367deg);
       -o-transform: rotate(175.4388091367deg);
          transform: rotate(175.4388091367deg);
  -webkit-animation: drop-6 4.5858265626s 0.0602767348s infinite;
       -o-animation: drop-6 4.5858265626s 0.0602767348s infinite;
          animation: drop-6 4.5858265626s 0.0602767348s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-6 {
  100% {
    top: 110%;
    left: 84%;
  }
}
@-o-keyframes drop-6 {
  100% {
    top: 110%;
    left: 84%;
  }
}
@keyframes drop-6 {
  100% {
    top: 110%;
    left: 84%;
  }
}
.confetti-wrapper .confetti-7 {
  width: 8px;
  height: 3.2px;
  background-color: #4CAF50;
  top: -10%;
  left: 22%;
  opacity: 1.2582392081;
  -webkit-transform: rotate(336.095365871deg);
      -ms-transform: rotate(336.095365871deg);
       -o-transform: rotate(336.095365871deg);
          transform: rotate(336.095365871deg);
  -webkit-animation: drop-7 4.9267754258s 0.6358102115s infinite;
       -o-animation: drop-7 4.9267754258s 0.6358102115s infinite;
          animation: drop-7 4.9267754258s 0.6358102115s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-7 {
  100% {
    top: 110%;
    left: 37%;
  }
}
@-o-keyframes drop-7 {
  100% {
    top: 110%;
    left: 37%;
  }
}
@keyframes drop-7 {
  100% {
    top: 110%;
    left: 37%;
  }
}
.confetti-wrapper .confetti-8 {
  width: 4px;
  height: 1.6px;
  background-color: #673ab7;
  top: -10%;
  left: 28%;
  opacity: 1.1869539845;
  -webkit-transform: rotate(8.0740333688deg);
      -ms-transform: rotate(8.0740333688deg);
       -o-transform: rotate(8.0740333688deg);
          transform: rotate(8.0740333688deg);
  -webkit-animation: drop-8 4.4082089378s 0.0313220495s infinite;
       -o-animation: drop-8 4.4082089378s 0.0313220495s infinite;
          animation: drop-8 4.4082089378s 0.0313220495s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-8 {
  100% {
    top: 110%;
    left: 38%;
  }
}
@-o-keyframes drop-8 {
  100% {
    top: 110%;
    left: 38%;
  }
}
@keyframes drop-8 {
  100% {
    top: 110%;
    left: 38%;
  }
}
.confetti-wrapper .confetti-9 {
  width: 1px;
  height: 0.4px;
  background-color: #ffc107;
  top: -10%;
  left: 46%;
  opacity: 0.5661024126;
  -webkit-transform: rotate(343.5790533249deg);
      -ms-transform: rotate(343.5790533249deg);
       -o-transform: rotate(343.5790533249deg);
          transform: rotate(343.5790533249deg);
  -webkit-animation: drop-9 4.6349553197s 0.6085442068s infinite;
       -o-animation: drop-9 4.6349553197s 0.6085442068s infinite;
          animation: drop-9 4.6349553197s 0.6085442068s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-9 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@-o-keyframes drop-9 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@keyframes drop-9 {
  100% {
    top: 110%;
    left: 47%;
  }
}
.confetti-wrapper .confetti-10 {
  width: 4px;
  height: 1.6px;
  background-color: #cddc39;
  top: -10%;
  left: 82%;
  opacity: 1.0678310641;
  -webkit-transform: rotate(130.5128778383deg);
      -ms-transform: rotate(130.5128778383deg);
       -o-transform: rotate(130.5128778383deg);
          transform: rotate(130.5128778383deg);
  -webkit-animation: drop-10 4.2740480358s 0.3570636816s infinite;
       -o-animation: drop-10 4.2740480358s 0.3570636816s infinite;
          animation: drop-10 4.2740480358s 0.3570636816s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-10 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@-o-keyframes drop-10 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@keyframes drop-10 {
  100% {
    top: 110%;
    left: 83%;
  }
}
.confetti-wrapper .confetti-11 {
  width: 3px;
  height: 1.2px;
  background-color: #ff5722;
  top: -10%;
  left: 36%;
  opacity: 1.1863563808;
  -webkit-transform: rotate(225.2828536313deg);
      -ms-transform: rotate(225.2828536313deg);
       -o-transform: rotate(225.2828536313deg);
          transform: rotate(225.2828536313deg);
  -webkit-animation: drop-11 4.4416456812s 0.5479331452s infinite;
       -o-animation: drop-11 4.4416456812s 0.5479331452s infinite;
          animation: drop-11 4.4416456812s 0.5479331452s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-11 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@-o-keyframes drop-11 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@keyframes drop-11 {
  100% {
    top: 110%;
    left: 43%;
  }
}
.confetti-wrapper .confetti-12 {
  width: 7px;
  height: 2.8px;
  background-color: #cddc39;
  top: -10%;
  left: -1%;
  opacity: 1.0368462793;
  -webkit-transform: rotate(53.9823583517deg);
      -ms-transform: rotate(53.9823583517deg);
       -o-transform: rotate(53.9823583517deg);
          transform: rotate(53.9823583517deg);
  -webkit-animation: drop-12 4.5387864294s 0.0225826103s infinite;
       -o-animation: drop-12 4.5387864294s 0.0225826103s infinite;
          animation: drop-12 4.5387864294s 0.0225826103s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-12 {
  100% {
    top: 110%;
    left: 5%;
  }
}
@-o-keyframes drop-12 {
  100% {
    top: 110%;
    left: 5%;
  }
}
@keyframes drop-12 {
  100% {
    top: 110%;
    left: 5%;
  }
}
.confetti-wrapper .confetti-13 {
  width: 7px;
  height: 2.8px;
  background-color: #673ab7;
  top: -10%;
  left: 6%;
  opacity: 0.6136254944;
  -webkit-transform: rotate(289.5164660669deg);
      -ms-transform: rotate(289.5164660669deg);
       -o-transform: rotate(289.5164660669deg);
          transform: rotate(289.5164660669deg);
  -webkit-animation: drop-13 4.0123473233s 0.2952451135s infinite;
       -o-animation: drop-13 4.0123473233s 0.2952451135s infinite;
          animation: drop-13 4.0123473233s 0.2952451135s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-13 {
  100% {
    top: 110%;
    left: 14%;
  }
}
@-o-keyframes drop-13 {
  100% {
    top: 110%;
    left: 14%;
  }
}
@keyframes drop-13 {
  100% {
    top: 110%;
    left: 14%;
  }
}
.confetti-wrapper .confetti-14 {
  width: 9px;
  height: 3.6px;
  background-color: #673ab7;
  top: -10%;
  left: 33%;
  opacity: 1.2984349238;
  -webkit-transform: rotate(92.058123046deg);
      -ms-transform: rotate(92.058123046deg);
       -o-transform: rotate(92.058123046deg);
          transform: rotate(92.058123046deg);
  -webkit-animation: drop-14 4.9821202115s 0.7724739908s infinite;
       -o-animation: drop-14 4.9821202115s 0.7724739908s infinite;
          animation: drop-14 4.9821202115s 0.7724739908s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-14 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@-o-keyframes drop-14 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@keyframes drop-14 {
  100% {
    top: 110%;
    left: 43%;
  }
}
.confetti-wrapper .confetti-15 {
  width: 5px;
  height: 2px;
  background-color: #00bcd4;
  top: -10%;
  left: 100%;
  opacity: 1.3089622547;
  -webkit-transform: rotate(38.3480207829deg);
      -ms-transform: rotate(38.3480207829deg);
       -o-transform: rotate(38.3480207829deg);
          transform: rotate(38.3480207829deg);
  -webkit-animation: drop-15 4.9043071512s 0.8369209964s infinite;
       -o-animation: drop-15 4.9043071512s 0.8369209964s infinite;
          animation: drop-15 4.9043071512s 0.8369209964s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-15 {
  100% {
    top: 110%;
    left: 112%;
  }
}
@-o-keyframes drop-15 {
  100% {
    top: 110%;
    left: 112%;
  }
}
@keyframes drop-15 {
  100% {
    top: 110%;
    left: 112%;
  }
}
.confetti-wrapper .confetti-16 {
  width: 8px;
  height: 3.2px;
  background-color: #009688;
  top: -10%;
  left: 100%;
  opacity: 1.0609902731;
  -webkit-transform: rotate(338.1850568554deg);
      -ms-transform: rotate(338.1850568554deg);
       -o-transform: rotate(338.1850568554deg);
          transform: rotate(338.1850568554deg);
  -webkit-animation: drop-16 4.5242516966s 0.6500140206s infinite;
       -o-animation: drop-16 4.5242516966s 0.6500140206s infinite;
          animation: drop-16 4.5242516966s 0.6500140206s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-16 {
  100% {
    top: 110%;
    left: 111%;
  }
}
@-o-keyframes drop-16 {
  100% {
    top: 110%;
    left: 111%;
  }
}
@keyframes drop-16 {
  100% {
    top: 110%;
    left: 111%;
  }
}
.confetti-wrapper .confetti-17 {
  width: 4px;
  height: 1.6px;
  background-color: #4CAF50;
  top: -10%;
  left: 53%;
  opacity: 1.3609515344;
  -webkit-transform: rotate(4.4423089981deg);
      -ms-transform: rotate(4.4423089981deg);
       -o-transform: rotate(4.4423089981deg);
          transform: rotate(4.4423089981deg);
  -webkit-animation: drop-17 4.8081190668s 0.0451565712s infinite;
       -o-animation: drop-17 4.8081190668s 0.0451565712s infinite;
          animation: drop-17 4.8081190668s 0.0451565712s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-17 {
  100% {
    top: 110%;
    left: 63%;
  }
}
@-o-keyframes drop-17 {
  100% {
    top: 110%;
    left: 63%;
  }
}
@keyframes drop-17 {
  100% {
    top: 110%;
    left: 63%;
  }
}
.confetti-wrapper .confetti-18 {
  width: 6px;
  height: 2.4px;
  background-color: #00bcd4;
  top: -10%;
  left: 11%;
  opacity: 0.791748567;
  -webkit-transform: rotate(40.2803049182deg);
      -ms-transform: rotate(40.2803049182deg);
       -o-transform: rotate(40.2803049182deg);
          transform: rotate(40.2803049182deg);
  -webkit-animation: drop-18 4.5159485126s 0.2428774417s infinite;
       -o-animation: drop-18 4.5159485126s 0.2428774417s infinite;
          animation: drop-18 4.5159485126s 0.2428774417s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-18 {
  100% {
    top: 110%;
    left: 13%;
  }
}
@-o-keyframes drop-18 {
  100% {
    top: 110%;
    left: 13%;
  }
}
@keyframes drop-18 {
  100% {
    top: 110%;
    left: 13%;
  }
}
.confetti-wrapper .confetti-19 {
  width: 4px;
  height: 1.6px;
  background-color: #F44336;
  top: -10%;
  left: 99%;
  opacity: 0.7240865484;
  -webkit-transform: rotate(69.7262355876deg);
      -ms-transform: rotate(69.7262355876deg);
       -o-transform: rotate(69.7262355876deg);
          transform: rotate(69.7262355876deg);
  -webkit-animation: drop-19 4.5491209646s 0.6193626092s infinite;
       -o-animation: drop-19 4.5491209646s 0.6193626092s infinite;
          animation: drop-19 4.5491209646s 0.6193626092s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-19 {
  100% {
    top: 110%;
    left: 104%;
  }
}
@-o-keyframes drop-19 {
  100% {
    top: 110%;
    left: 104%;
  }
}
@keyframes drop-19 {
  100% {
    top: 110%;
    left: 104%;
  }
}
.confetti-wrapper .confetti-20 {
  width: 3px;
  height: 1.2px;
  background-color: #ffeb3b;
  top: -10%;
  left: 88%;
  opacity: 0.7649408671;
  -webkit-transform: rotate(230.8494534589deg);
      -ms-transform: rotate(230.8494534589deg);
       -o-transform: rotate(230.8494534589deg);
          transform: rotate(230.8494534589deg);
  -webkit-animation: drop-20 4.8466946181s 0.9141348111s infinite;
       -o-animation: drop-20 4.8466946181s 0.9141348111s infinite;
          animation: drop-20 4.8466946181s 0.9141348111s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-20 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@-o-keyframes drop-20 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@keyframes drop-20 {
  100% {
    top: 110%;
    left: 100%;
  }
}
.confetti-wrapper .confetti-21 {
  width: 3px;
  height: 1.2px;
  background-color: #9c27b0;
  top: -10%;
  left: 36%;
  opacity: 1.1068738933;
  -webkit-transform: rotate(190.4157768794deg);
      -ms-transform: rotate(190.4157768794deg);
       -o-transform: rotate(190.4157768794deg);
          transform: rotate(190.4157768794deg);
  -webkit-animation: drop-21 4.9320517716s 0.8634092458s infinite;
       -o-animation: drop-21 4.9320517716s 0.8634092458s infinite;
          animation: drop-21 4.9320517716s 0.8634092458s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-21 {
  100% {
    top: 110%;
    left: 38%;
  }
}
@-o-keyframes drop-21 {
  100% {
    top: 110%;
    left: 38%;
  }
}
@keyframes drop-21 {
  100% {
    top: 110%;
    left: 38%;
  }
}
.confetti-wrapper .confetti-22 {
  width: 6px;
  height: 2.4px;
  background-color: #ffc107;
  top: -10%;
  left: 72%;
  opacity: 1.1138473891;
  -webkit-transform: rotate(357.7762864001deg);
      -ms-transform: rotate(357.7762864001deg);
       -o-transform: rotate(357.7762864001deg);
          transform: rotate(357.7762864001deg);
  -webkit-animation: drop-22 4.7720210137s 0.6360105989s infinite;
       -o-animation: drop-22 4.7720210137s 0.6360105989s infinite;
          animation: drop-22 4.7720210137s 0.6360105989s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-22 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@-o-keyframes drop-22 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@keyframes drop-22 {
  100% {
    top: 110%;
    left: 80%;
  }
}
.confetti-wrapper .confetti-23 {
  width: 5px;
  height: 2px;
  background-color: #F44336;
  top: -10%;
  left: 24%;
  opacity: 0.8365578334;
  -webkit-transform: rotate(102.3411257435deg);
      -ms-transform: rotate(102.3411257435deg);
       -o-transform: rotate(102.3411257435deg);
          transform: rotate(102.3411257435deg);
  -webkit-animation: drop-23 4.6819873678s 0.7862823229s infinite;
       -o-animation: drop-23 4.6819873678s 0.7862823229s infinite;
          animation: drop-23 4.6819873678s 0.7862823229s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-23 {
  100% {
    top: 110%;
    left: 26%;
  }
}
@-o-keyframes drop-23 {
  100% {
    top: 110%;
    left: 26%;
  }
}
@keyframes drop-23 {
  100% {
    top: 110%;
    left: 26%;
  }
}
.confetti-wrapper .confetti-24 {
  width: 7px;
  height: 2.8px;
  background-color: #673ab7;
  top: -10%;
  left: 88%;
  opacity: 0.7051961567;
  -webkit-transform: rotate(50.7320443675deg);
      -ms-transform: rotate(50.7320443675deg);
       -o-transform: rotate(50.7320443675deg);
          transform: rotate(50.7320443675deg);
  -webkit-animation: drop-24 4.6753395481s 0.8225081388s infinite;
       -o-animation: drop-24 4.6753395481s 0.8225081388s infinite;
          animation: drop-24 4.6753395481s 0.8225081388s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-24 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@-o-keyframes drop-24 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@keyframes drop-24 {
  100% {
    top: 110%;
    left: 100%;
  }
}
.confetti-wrapper .confetti-25 {
  width: 6px;
  height: 2.4px;
  background-color: #009688;
  top: -10%;
  left: 12%;
  opacity: 0.5759074781;
  -webkit-transform: rotate(120.1812786639deg);
      -ms-transform: rotate(120.1812786639deg);
       -o-transform: rotate(120.1812786639deg);
          transform: rotate(120.1812786639deg);
  -webkit-animation: drop-25 4.2725690621s 0.3092288224s infinite;
       -o-animation: drop-25 4.2725690621s 0.3092288224s infinite;
          animation: drop-25 4.2725690621s 0.3092288224s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-25 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@-o-keyframes drop-25 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@keyframes drop-25 {
  100% {
    top: 110%;
    left: 19%;
  }
}
.confetti-wrapper .confetti-26 {
  width: 1px;
  height: 0.4px;
  background-color: #8bc34a;
  top: -10%;
  left: 75%;
  opacity: 0.6269163935;
  -webkit-transform: rotate(234.2079641704deg);
      -ms-transform: rotate(234.2079641704deg);
       -o-transform: rotate(234.2079641704deg);
          transform: rotate(234.2079641704deg);
  -webkit-animation: drop-26 4.2995260704s 0.3605948344s infinite;
       -o-animation: drop-26 4.2995260704s 0.3605948344s infinite;
          animation: drop-26 4.2995260704s 0.3605948344s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-26 {
  100% {
    top: 110%;
    left: 87%;
  }
}
@-o-keyframes drop-26 {
  100% {
    top: 110%;
    left: 87%;
  }
}
@keyframes drop-26 {
  100% {
    top: 110%;
    left: 87%;
  }
}
.confetti-wrapper .confetti-27 {
  width: 10px;
  height: 4px;
  background-color: #673ab7;
  top: -10%;
  left: -2%;
  opacity: 0.6472360123;
  -webkit-transform: rotate(260.1523728635deg);
      -ms-transform: rotate(260.1523728635deg);
       -o-transform: rotate(260.1523728635deg);
          transform: rotate(260.1523728635deg);
  -webkit-animation: drop-27 4.3515037022s 0.6463538727s infinite;
       -o-animation: drop-27 4.3515037022s 0.6463538727s infinite;
          animation: drop-27 4.3515037022s 0.6463538727s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-27 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@-o-keyframes drop-27 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@keyframes drop-27 {
  100% {
    top: 110%;
    left: 9%;
  }
}
.confetti-wrapper .confetti-28 {
  width: 8px;
  height: 3.2px;
  background-color: #9c27b0;
  top: -10%;
  left: 32%;
  opacity: 1.4618009533;
  -webkit-transform: rotate(73.3945040155deg);
      -ms-transform: rotate(73.3945040155deg);
       -o-transform: rotate(73.3945040155deg);
          transform: rotate(73.3945040155deg);
  -webkit-animation: drop-28 4.6004646251s 0.2190328341s infinite;
       -o-animation: drop-28 4.6004646251s 0.2190328341s infinite;
          animation: drop-28 4.6004646251s 0.2190328341s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-28 {
  100% {
    top: 110%;
    left: 36%;
  }
}
@-o-keyframes drop-28 {
  100% {
    top: 110%;
    left: 36%;
  }
}
@keyframes drop-28 {
  100% {
    top: 110%;
    left: 36%;
  }
}
.confetti-wrapper .confetti-29 {
  width: 2px;
  height: 0.8px;
  background-color: #F44336;
  top: -10%;
  left: 46%;
  opacity: 1.258390477;
  -webkit-transform: rotate(179.0473526108deg);
      -ms-transform: rotate(179.0473526108deg);
       -o-transform: rotate(179.0473526108deg);
          transform: rotate(179.0473526108deg);
  -webkit-animation: drop-29 4.162467549s 0.6711745249s infinite;
       -o-animation: drop-29 4.162467549s 0.6711745249s infinite;
          animation: drop-29 4.162467549s 0.6711745249s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-29 {
  100% {
    top: 110%;
    left: 48%;
  }
}
@-o-keyframes drop-29 {
  100% {
    top: 110%;
    left: 48%;
  }
}
@keyframes drop-29 {
  100% {
    top: 110%;
    left: 48%;
  }
}
.confetti-wrapper .confetti-30 {
  width: 9px;
  height: 3.6px;
  background-color: #4CAF50;
  top: -10%;
  left: 53%;
  opacity: 0.538093386;
  -webkit-transform: rotate(177.3416921242deg);
      -ms-transform: rotate(177.3416921242deg);
       -o-transform: rotate(177.3416921242deg);
          transform: rotate(177.3416921242deg);
  -webkit-animation: drop-30 4.230263746s 0.6105902012s infinite;
       -o-animation: drop-30 4.230263746s 0.6105902012s infinite;
          animation: drop-30 4.230263746s 0.6105902012s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-30 {
  100% {
    top: 110%;
    left: 61%;
  }
}
@-o-keyframes drop-30 {
  100% {
    top: 110%;
    left: 61%;
  }
}
@keyframes drop-30 {
  100% {
    top: 110%;
    left: 61%;
  }
}
.confetti-wrapper .confetti-31 {
  width: 5px;
  height: 2px;
  background-color: #8bc34a;
  top: -10%;
  left: 87%;
  opacity: 1.0821670646;
  -webkit-transform: rotate(214.1267397177deg);
      -ms-transform: rotate(214.1267397177deg);
       -o-transform: rotate(214.1267397177deg);
          transform: rotate(214.1267397177deg);
  -webkit-animation: drop-31 4.3141104417s 0.7418380332s infinite;
       -o-animation: drop-31 4.3141104417s 0.7418380332s infinite;
          animation: drop-31 4.3141104417s 0.7418380332s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-31 {
  100% {
    top: 110%;
    left: 97%;
  }
}
@-o-keyframes drop-31 {
  100% {
    top: 110%;
    left: 97%;
  }
}
@keyframes drop-31 {
  100% {
    top: 110%;
    left: 97%;
  }
}
.confetti-wrapper .confetti-32 {
  width: 1px;
  height: 0.4px;
  background-color: #03a9f4;
  top: -10%;
  left: 48%;
  opacity: 0.5811805817;
  -webkit-transform: rotate(91.5088269679deg);
      -ms-transform: rotate(91.5088269679deg);
       -o-transform: rotate(91.5088269679deg);
          transform: rotate(91.5088269679deg);
  -webkit-animation: drop-32 4.0230416433s 0.7488313433s infinite;
       -o-animation: drop-32 4.0230416433s 0.7488313433s infinite;
          animation: drop-32 4.0230416433s 0.7488313433s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-32 {
  100% {
    top: 110%;
    left: 59%;
  }
}
@-o-keyframes drop-32 {
  100% {
    top: 110%;
    left: 59%;
  }
}
@keyframes drop-32 {
  100% {
    top: 110%;
    left: 59%;
  }
}
.confetti-wrapper .confetti-33 {
  width: 4px;
  height: 1.6px;
  background-color: #ffc107;
  top: -10%;
  left: -6%;
  opacity: 1.4660259718;
  -webkit-transform: rotate(352.1487558613deg);
      -ms-transform: rotate(352.1487558613deg);
       -o-transform: rotate(352.1487558613deg);
          transform: rotate(352.1487558613deg);
  -webkit-animation: drop-33 4.8676633626s 0.6596090257s infinite;
       -o-animation: drop-33 4.8676633626s 0.6596090257s infinite;
          animation: drop-33 4.8676633626s 0.6596090257s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-33 {
  100% {
    top: 110%;
    left: 6%;
  }
}
@-o-keyframes drop-33 {
  100% {
    top: 110%;
    left: 6%;
  }
}
@keyframes drop-33 {
  100% {
    top: 110%;
    left: 6%;
  }
}
.confetti-wrapper .confetti-34 {
  width: 1px;
  height: 0.4px;
  background-color: #03a9f4;
  top: -10%;
  left: 74%;
  opacity: 0.6440675818;
  -webkit-transform: rotate(18.0625300057deg);
      -ms-transform: rotate(18.0625300057deg);
       -o-transform: rotate(18.0625300057deg);
          transform: rotate(18.0625300057deg);
  -webkit-animation: drop-34 4.4471440736s 0.7336153711s infinite;
       -o-animation: drop-34 4.4471440736s 0.7336153711s infinite;
          animation: drop-34 4.4471440736s 0.7336153711s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-34 {
  100% {
    top: 110%;
    left: 77%;
  }
}
@-o-keyframes drop-34 {
  100% {
    top: 110%;
    left: 77%;
  }
}
@keyframes drop-34 {
  100% {
    top: 110%;
    left: 77%;
  }
}
.confetti-wrapper .confetti-35 {
  width: 6px;
  height: 2.4px;
  background-color: #ffc107;
  top: -10%;
  left: 100%;
  opacity: 0.9013903809;
  -webkit-transform: rotate(112.9176490473deg);
      -ms-transform: rotate(112.9176490473deg);
       -o-transform: rotate(112.9176490473deg);
          transform: rotate(112.9176490473deg);
  -webkit-animation: drop-35 4.3393620702s 0.5409122169s infinite;
       -o-animation: drop-35 4.3393620702s 0.5409122169s infinite;
          animation: drop-35 4.3393620702s 0.5409122169s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-35 {
  100% {
    top: 110%;
    left: 107%;
  }
}
@-o-keyframes drop-35 {
  100% {
    top: 110%;
    left: 107%;
  }
}
@keyframes drop-35 {
  100% {
    top: 110%;
    left: 107%;
  }
}
.confetti-wrapper .confetti-36 {
  width: 9px;
  height: 3.6px;
  background-color: #03a9f4;
  top: -10%;
  left: 79%;
  opacity: 0.6199586595;
  -webkit-transform: rotate(102.066509009deg);
      -ms-transform: rotate(102.066509009deg);
       -o-transform: rotate(102.066509009deg);
          transform: rotate(102.066509009deg);
  -webkit-animation: drop-36 4.5295015358s 0.6154580501s infinite;
       -o-animation: drop-36 4.5295015358s 0.6154580501s infinite;
          animation: drop-36 4.5295015358s 0.6154580501s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-36 {
  100% {
    top: 110%;
    left: 90%;
  }
}
@-o-keyframes drop-36 {
  100% {
    top: 110%;
    left: 90%;
  }
}
@keyframes drop-36 {
  100% {
    top: 110%;
    left: 90%;
  }
}
.confetti-wrapper .confetti-37 {
  width: 6px;
  height: 2.4px;
  background-color: #009688;
  top: -10%;
  left: 95%;
  opacity: 1.414199415;
  -webkit-transform: rotate(292.6475971651deg);
      -ms-transform: rotate(292.6475971651deg);
       -o-transform: rotate(292.6475971651deg);
          transform: rotate(292.6475971651deg);
  -webkit-animation: drop-37 4.4421446673s 0.5041481406s infinite;
       -o-animation: drop-37 4.4421446673s 0.5041481406s infinite;
          animation: drop-37 4.4421446673s 0.5041481406s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-37 {
  100% {
    top: 110%;
    left: 102%;
  }
}
@-o-keyframes drop-37 {
  100% {
    top: 110%;
    left: 102%;
  }
}
@keyframes drop-37 {
  100% {
    top: 110%;
    left: 102%;
  }
}
.confetti-wrapper .confetti-38 {
  width: 7px;
  height: 2.8px;
  background-color: #ffc107;
  top: -10%;
  left: 40%;
  opacity: 0.736595973;
  -webkit-transform: rotate(68.624934418deg);
      -ms-transform: rotate(68.624934418deg);
       -o-transform: rotate(68.624934418deg);
          transform: rotate(68.624934418deg);
  -webkit-animation: drop-38 4.2788529746s 0.0808059438s infinite;
       -o-animation: drop-38 4.2788529746s 0.0808059438s infinite;
          animation: drop-38 4.2788529746s 0.0808059438s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-38 {
  100% {
    top: 110%;
    left: 49%;
  }
}
@-o-keyframes drop-38 {
  100% {
    top: 110%;
    left: 49%;
  }
}
@keyframes drop-38 {
  100% {
    top: 110%;
    left: 49%;
  }
}
.confetti-wrapper .confetti-39 {
  width: 10px;
  height: 4px;
  background-color: #F44336;
  top: -10%;
  left: 34%;
  opacity: 1.2758041596;
  -webkit-transform: rotate(237.4458097298deg);
      -ms-transform: rotate(237.4458097298deg);
       -o-transform: rotate(237.4458097298deg);
          transform: rotate(237.4458097298deg);
  -webkit-animation: drop-39 4.022755673s 0.6460920612s infinite;
       -o-animation: drop-39 4.022755673s 0.6460920612s infinite;
          animation: drop-39 4.022755673s 0.6460920612s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-39 {
  100% {
    top: 110%;
    left: 48%;
  }
}
@-o-keyframes drop-39 {
  100% {
    top: 110%;
    left: 48%;
  }
}
@keyframes drop-39 {
  100% {
    top: 110%;
    left: 48%;
  }
}
.confetti-wrapper .confetti-40 {
  width: 4px;
  height: 1.6px;
  background-color: #F44336;
  top: -10%;
  left: 6%;
  opacity: 1.1132672981;
  -webkit-transform: rotate(216.834799501deg);
      -ms-transform: rotate(216.834799501deg);
       -o-transform: rotate(216.834799501deg);
          transform: rotate(216.834799501deg);
  -webkit-animation: drop-40 4.5321161221s 0.6351275074s infinite;
       -o-animation: drop-40 4.5321161221s 0.6351275074s infinite;
          animation: drop-40 4.5321161221s 0.6351275074s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-40 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@-o-keyframes drop-40 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@keyframes drop-40 {
  100% {
    top: 110%;
    left: 19%;
  }
}
.confetti-wrapper .confetti-41 {
  width: 1px;
  height: 0.4px;
  background-color: #00bcd4;
  top: -10%;
  left: 50%;
  opacity: 0.648285118;
  -webkit-transform: rotate(88.5447167052deg);
      -ms-transform: rotate(88.5447167052deg);
       -o-transform: rotate(88.5447167052deg);
          transform: rotate(88.5447167052deg);
  -webkit-animation: drop-41 4.0579575402s 0.2926185703s infinite;
       -o-animation: drop-41 4.0579575402s 0.2926185703s infinite;
          animation: drop-41 4.0579575402s 0.2926185703s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-41 {
  100% {
    top: 110%;
    left: 59%;
  }
}
@-o-keyframes drop-41 {
  100% {
    top: 110%;
    left: 59%;
  }
}
@keyframes drop-41 {
  100% {
    top: 110%;
    left: 59%;
  }
}
.confetti-wrapper .confetti-42 {
  width: 3px;
  height: 1.2px;
  background-color: #673ab7;
  top: -10%;
  left: 27%;
  opacity: 1.2176569788;
  -webkit-transform: rotate(295.3853427deg);
      -ms-transform: rotate(295.3853427deg);
       -o-transform: rotate(295.3853427deg);
          transform: rotate(295.3853427deg);
  -webkit-animation: drop-42 4.7060535927s 0.2340844112s infinite;
       -o-animation: drop-42 4.7060535927s 0.2340844112s infinite;
          animation: drop-42 4.7060535927s 0.2340844112s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-42 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@-o-keyframes drop-42 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@keyframes drop-42 {
  100% {
    top: 110%;
    left: 41%;
  }
}
.confetti-wrapper .confetti-43 {
  width: 9px;
  height: 3.6px;
  background-color: #03a9f4;
  top: -10%;
  left: 75%;
  opacity: 1.1008144863;
  -webkit-transform: rotate(40.8920961144deg);
      -ms-transform: rotate(40.8920961144deg);
       -o-transform: rotate(40.8920961144deg);
          transform: rotate(40.8920961144deg);
  -webkit-animation: drop-43 4.7724765842s 0.4680703501s infinite;
       -o-animation: drop-43 4.7724765842s 0.4680703501s infinite;
          animation: drop-43 4.7724765842s 0.4680703501s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-43 {
  100% {
    top: 110%;
    left: 77%;
  }
}
@-o-keyframes drop-43 {
  100% {
    top: 110%;
    left: 77%;
  }
}
@keyframes drop-43 {
  100% {
    top: 110%;
    left: 77%;
  }
}
.confetti-wrapper .confetti-44 {
  width: 3px;
  height: 1.2px;
  background-color: #673ab7;
  top: -10%;
  left: 59%;
  opacity: 0.9428830289;
  -webkit-transform: rotate(180.215295923deg);
      -ms-transform: rotate(180.215295923deg);
       -o-transform: rotate(180.215295923deg);
          transform: rotate(180.215295923deg);
  -webkit-animation: drop-44 4.4836846043s 0.7569300207s infinite;
       -o-animation: drop-44 4.4836846043s 0.7569300207s infinite;
          animation: drop-44 4.4836846043s 0.7569300207s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-44 {
  100% {
    top: 110%;
    left: 71%;
  }
}
@-o-keyframes drop-44 {
  100% {
    top: 110%;
    left: 71%;
  }
}
@keyframes drop-44 {
  100% {
    top: 110%;
    left: 71%;
  }
}
.confetti-wrapper .confetti-45 {
  width: 6px;
  height: 2.4px;
  background-color: #009688;
  top: -10%;
  left: 74%;
  opacity: 1.1392751859;
  -webkit-transform: rotate(299.4272849966deg);
      -ms-transform: rotate(299.4272849966deg);
       -o-transform: rotate(299.4272849966deg);
          transform: rotate(299.4272849966deg);
  -webkit-animation: drop-45 4.877189344s 0.4366561601s infinite;
       -o-animation: drop-45 4.877189344s 0.4366561601s infinite;
          animation: drop-45 4.877189344s 0.4366561601s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-45 {
  100% {
    top: 110%;
    left: 79%;
  }
}
@-o-keyframes drop-45 {
  100% {
    top: 110%;
    left: 79%;
  }
}
@keyframes drop-45 {
  100% {
    top: 110%;
    left: 79%;
  }
}
.confetti-wrapper .confetti-46 {
  width: 10px;
  height: 4px;
  background-color: #e91e63;
  top: -10%;
  left: 41%;
  opacity: 0.7556934104;
  -webkit-transform: rotate(285.3993395264deg);
      -ms-transform: rotate(285.3993395264deg);
       -o-transform: rotate(285.3993395264deg);
          transform: rotate(285.3993395264deg);
  -webkit-animation: drop-46 4.5866829295s 0.6664671853s infinite;
       -o-animation: drop-46 4.5866829295s 0.6664671853s infinite;
          animation: drop-46 4.5866829295s 0.6664671853s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-46 {
  100% {
    top: 110%;
    left: 42%;
  }
}
@-o-keyframes drop-46 {
  100% {
    top: 110%;
    left: 42%;
  }
}
@keyframes drop-46 {
  100% {
    top: 110%;
    left: 42%;
  }
}
.confetti-wrapper .confetti-47 {
  width: 7px;
  height: 2.8px;
  background-color: #F44336;
  top: -10%;
  left: 48%;
  opacity: 0.8890623883;
  -webkit-transform: rotate(356.5308354274deg);
      -ms-transform: rotate(356.5308354274deg);
       -o-transform: rotate(356.5308354274deg);
          transform: rotate(356.5308354274deg);
  -webkit-animation: drop-47 4.1551947266s 0.3756867322s infinite;
       -o-animation: drop-47 4.1551947266s 0.3756867322s infinite;
          animation: drop-47 4.1551947266s 0.3756867322s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-47 {
  100% {
    top: 110%;
    left: 54%;
  }
}
@-o-keyframes drop-47 {
  100% {
    top: 110%;
    left: 54%;
  }
}
@keyframes drop-47 {
  100% {
    top: 110%;
    left: 54%;
  }
}
.confetti-wrapper .confetti-48 {
  width: 4px;
  height: 1.6px;
  background-color: #cddc39;
  top: -10%;
  left: 63%;
  opacity: 1.1658360536;
  -webkit-transform: rotate(20.7283294245deg);
      -ms-transform: rotate(20.7283294245deg);
       -o-transform: rotate(20.7283294245deg);
          transform: rotate(20.7283294245deg);
  -webkit-animation: drop-48 4.2303816817s 0.3028727145s infinite;
       -o-animation: drop-48 4.2303816817s 0.3028727145s infinite;
          animation: drop-48 4.2303816817s 0.3028727145s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-48 {
  100% {
    top: 110%;
    left: 65%;
  }
}
@-o-keyframes drop-48 {
  100% {
    top: 110%;
    left: 65%;
  }
}
@keyframes drop-48 {
  100% {
    top: 110%;
    left: 65%;
  }
}
.confetti-wrapper .confetti-49 {
  width: 7px;
  height: 2.8px;
  background-color: #ff5722;
  top: -10%;
  left: 56%;
  opacity: 0.8311220523;
  -webkit-transform: rotate(16.7201897888deg);
      -ms-transform: rotate(16.7201897888deg);
       -o-transform: rotate(16.7201897888deg);
          transform: rotate(16.7201897888deg);
  -webkit-animation: drop-49 4.8270157038s 0.7445512297s infinite;
       -o-animation: drop-49 4.8270157038s 0.7445512297s infinite;
          animation: drop-49 4.8270157038s 0.7445512297s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-49 {
  100% {
    top: 110%;
    left: 61%;
  }
}
@-o-keyframes drop-49 {
  100% {
    top: 110%;
    left: 61%;
  }
}
@keyframes drop-49 {
  100% {
    top: 110%;
    left: 61%;
  }
}
.confetti-wrapper .confetti-50 {
  width: 5px;
  height: 2px;
  background-color: #F44336;
  top: -10%;
  left: 25%;
  opacity: 0.8786606045;
  -webkit-transform: rotate(342.8007536886deg);
      -ms-transform: rotate(342.8007536886deg);
       -o-transform: rotate(342.8007536886deg);
          transform: rotate(342.8007536886deg);
  -webkit-animation: drop-50 4.9379411206s 0.9081538821s infinite;
       -o-animation: drop-50 4.9379411206s 0.9081538821s infinite;
          animation: drop-50 4.9379411206s 0.9081538821s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-50 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@-o-keyframes drop-50 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@keyframes drop-50 {
  100% {
    top: 110%;
    left: 27%;
  }
}
.confetti-wrapper .confetti-51 {
  width: 5px;
  height: 2px;
  background-color: #F44336;
  top: -10%;
  left: 5%;
  opacity: 0.5348875524;
  -webkit-transform: rotate(48.8736315753deg);
      -ms-transform: rotate(48.8736315753deg);
       -o-transform: rotate(48.8736315753deg);
          transform: rotate(48.8736315753deg);
  -webkit-animation: drop-51 4.758280743s 0.1839121356s infinite;
       -o-animation: drop-51 4.758280743s 0.1839121356s infinite;
          animation: drop-51 4.758280743s 0.1839121356s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-51 {
  100% {
    top: 110%;
    left: 7%;
  }
}
@-o-keyframes drop-51 {
  100% {
    top: 110%;
    left: 7%;
  }
}
@keyframes drop-51 {
  100% {
    top: 110%;
    left: 7%;
  }
}
.confetti-wrapper .confetti-52 {
  width: 6px;
  height: 2.4px;
  background-color: #e91e63;
  top: -10%;
  left: 1%;
  opacity: 0.5525899557;
  -webkit-transform: rotate(36.413713557deg);
      -ms-transform: rotate(36.413713557deg);
       -o-transform: rotate(36.413713557deg);
          transform: rotate(36.413713557deg);
  -webkit-animation: drop-52 4.7616362378s 0.3521153337s infinite;
       -o-animation: drop-52 4.7616362378s 0.3521153337s infinite;
          animation: drop-52 4.7616362378s 0.3521153337s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-52 {
  100% {
    top: 110%;
    left: 6%;
  }
}
@-o-keyframes drop-52 {
  100% {
    top: 110%;
    left: 6%;
  }
}
@keyframes drop-52 {
  100% {
    top: 110%;
    left: 6%;
  }
}
.confetti-wrapper .confetti-53 {
  width: 4px;
  height: 1.6px;
  background-color: #ffeb3b;
  top: -10%;
  left: 73%;
  opacity: 1.2996056812;
  -webkit-transform: rotate(327.881238006deg);
      -ms-transform: rotate(327.881238006deg);
       -o-transform: rotate(327.881238006deg);
          transform: rotate(327.881238006deg);
  -webkit-animation: drop-53 4.9636468679s 0.6319938592s infinite;
       -o-animation: drop-53 4.9636468679s 0.6319938592s infinite;
          animation: drop-53 4.9636468679s 0.6319938592s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-53 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@-o-keyframes drop-53 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@keyframes drop-53 {
  100% {
    top: 110%;
    left: 76%;
  }
}
.confetti-wrapper .confetti-54 {
  width: 10px;
  height: 4px;
  background-color: #ffeb3b;
  top: -10%;
  left: 30%;
  opacity: 1.3818910844;
  -webkit-transform: rotate(56.8460530373deg);
      -ms-transform: rotate(56.8460530373deg);
       -o-transform: rotate(56.8460530373deg);
          transform: rotate(56.8460530373deg);
  -webkit-animation: drop-54 4.7275491301s 0.9320798501s infinite;
       -o-animation: drop-54 4.7275491301s 0.9320798501s infinite;
          animation: drop-54 4.7275491301s 0.9320798501s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-54 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@-o-keyframes drop-54 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@keyframes drop-54 {
  100% {
    top: 110%;
    left: 39%;
  }
}
.confetti-wrapper .confetti-55 {
  width: 9px;
  height: 3.6px;
  background-color: #3f51b5;
  top: -10%;
  left: 77%;
  opacity: 1.4947108542;
  -webkit-transform: rotate(263.579993892deg);
      -ms-transform: rotate(263.579993892deg);
       -o-transform: rotate(263.579993892deg);
          transform: rotate(263.579993892deg);
  -webkit-animation: drop-55 4.9312808121s 0.6418009928s infinite;
       -o-animation: drop-55 4.9312808121s 0.6418009928s infinite;
          animation: drop-55 4.9312808121s 0.6418009928s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-55 {
  100% {
    top: 110%;
    left: 92%;
  }
}
@-o-keyframes drop-55 {
  100% {
    top: 110%;
    left: 92%;
  }
}
@keyframes drop-55 {
  100% {
    top: 110%;
    left: 92%;
  }
}
.confetti-wrapper .confetti-56 {
  width: 9px;
  height: 3.6px;
  background-color: #03a9f4;
  top: -10%;
  left: 63%;
  opacity: 1.2383351865;
  -webkit-transform: rotate(220.8659327281deg);
      -ms-transform: rotate(220.8659327281deg);
       -o-transform: rotate(220.8659327281deg);
          transform: rotate(220.8659327281deg);
  -webkit-animation: drop-56 4.8406286199s 0.3581554971s infinite;
       -o-animation: drop-56 4.8406286199s 0.3581554971s infinite;
          animation: drop-56 4.8406286199s 0.3581554971s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-56 {
  100% {
    top: 110%;
    left: 73%;
  }
}
@-o-keyframes drop-56 {
  100% {
    top: 110%;
    left: 73%;
  }
}
@keyframes drop-56 {
  100% {
    top: 110%;
    left: 73%;
  }
}
.confetti-wrapper .confetti-57 {
  width: 7px;
  height: 2.8px;
  background-color: #8bc34a;
  top: -10%;
  left: 29%;
  opacity: 0.8144081461;
  -webkit-transform: rotate(82.7859651487deg);
      -ms-transform: rotate(82.7859651487deg);
       -o-transform: rotate(82.7859651487deg);
          transform: rotate(82.7859651487deg);
  -webkit-animation: drop-57 4.1829307398s 0.6595669672s infinite;
       -o-animation: drop-57 4.1829307398s 0.6595669672s infinite;
          animation: drop-57 4.1829307398s 0.6595669672s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-57 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@-o-keyframes drop-57 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@keyframes drop-57 {
  100% {
    top: 110%;
    left: 43%;
  }
}
.confetti-wrapper .confetti-58 {
  width: 5px;
  height: 2px;
  background-color: #4CAF50;
  top: -10%;
  left: 78%;
  opacity: 0.6684359873;
  -webkit-transform: rotate(121.1438766927deg);
      -ms-transform: rotate(121.1438766927deg);
       -o-transform: rotate(121.1438766927deg);
          transform: rotate(121.1438766927deg);
  -webkit-animation: drop-58 4.147603434s 0.9987269101s infinite;
       -o-animation: drop-58 4.147603434s 0.9987269101s infinite;
          animation: drop-58 4.147603434s 0.9987269101s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-58 {
  100% {
    top: 110%;
    left: 84%;
  }
}
@-o-keyframes drop-58 {
  100% {
    top: 110%;
    left: 84%;
  }
}
@keyframes drop-58 {
  100% {
    top: 110%;
    left: 84%;
  }
}
.confetti-wrapper .confetti-59 {
  width: 5px;
  height: 2px;
  background-color: #cddc39;
  top: -10%;
  left: 70%;
  opacity: 0.840422597;
  -webkit-transform: rotate(152.4481584929deg);
      -ms-transform: rotate(152.4481584929deg);
       -o-transform: rotate(152.4481584929deg);
          transform: rotate(152.4481584929deg);
  -webkit-animation: drop-59 4.3370407135s 0.7805211534s infinite;
       -o-animation: drop-59 4.3370407135s 0.7805211534s infinite;
          animation: drop-59 4.3370407135s 0.7805211534s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-59 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@-o-keyframes drop-59 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@keyframes drop-59 {
  100% {
    top: 110%;
    left: 76%;
  }
}
.confetti-wrapper .confetti-60 {
  width: 2px;
  height: 0.8px;
  background-color: #9c27b0;
  top: -10%;
  left: 31%;
  opacity: 0.654523442;
  -webkit-transform: rotate(14.6166253465deg);
      -ms-transform: rotate(14.6166253465deg);
       -o-transform: rotate(14.6166253465deg);
          transform: rotate(14.6166253465deg);
  -webkit-animation: drop-60 4.321587622s 0.8350123828s infinite;
       -o-animation: drop-60 4.321587622s 0.8350123828s infinite;
          animation: drop-60 4.321587622s 0.8350123828s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-60 {
  100% {
    top: 110%;
    left: 34%;
  }
}
@-o-keyframes drop-60 {
  100% {
    top: 110%;
    left: 34%;
  }
}
@keyframes drop-60 {
  100% {
    top: 110%;
    left: 34%;
  }
}
.confetti-wrapper .confetti-61 {
  width: 4px;
  height: 1.6px;
  background-color: #ff9800;
  top: -10%;
  left: 14%;
  opacity: 1.0079709851;
  -webkit-transform: rotate(335.7238422793deg);
      -ms-transform: rotate(335.7238422793deg);
       -o-transform: rotate(335.7238422793deg);
          transform: rotate(335.7238422793deg);
  -webkit-animation: drop-61 4.1443653443s 0.5080728868s infinite;
       -o-animation: drop-61 4.1443653443s 0.5080728868s infinite;
          animation: drop-61 4.1443653443s 0.5080728868s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-61 {
  100% {
    top: 110%;
    left: 26%;
  }
}
@-o-keyframes drop-61 {
  100% {
    top: 110%;
    left: 26%;
  }
}
@keyframes drop-61 {
  100% {
    top: 110%;
    left: 26%;
  }
}
.confetti-wrapper .confetti-62 {
  width: 2px;
  height: 0.8px;
  background-color: #03a9f4;
  top: -10%;
  left: 48%;
  opacity: 0.8310926534;
  -webkit-transform: rotate(292.969616477deg);
      -ms-transform: rotate(292.969616477deg);
       -o-transform: rotate(292.969616477deg);
          transform: rotate(292.969616477deg);
  -webkit-animation: drop-62 4.2062354909s 0.8212954061s infinite;
       -o-animation: drop-62 4.2062354909s 0.8212954061s infinite;
          animation: drop-62 4.2062354909s 0.8212954061s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-62 {
  100% {
    top: 110%;
    left: 58%;
  }
}
@-o-keyframes drop-62 {
  100% {
    top: 110%;
    left: 58%;
  }
}
@keyframes drop-62 {
  100% {
    top: 110%;
    left: 58%;
  }
}
.confetti-wrapper .confetti-63 {
  width: 9px;
  height: 3.6px;
  background-color: #9c27b0;
  top: -10%;
  left: 24%;
  opacity: 0.9161129236;
  -webkit-transform: rotate(98.1802669724deg);
      -ms-transform: rotate(98.1802669724deg);
       -o-transform: rotate(98.1802669724deg);
          transform: rotate(98.1802669724deg);
  -webkit-animation: drop-63 4.994250276s 0.8178860444s infinite;
       -o-animation: drop-63 4.994250276s 0.8178860444s infinite;
          animation: drop-63 4.994250276s 0.8178860444s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-63 {
  100% {
    top: 110%;
    left: 34%;
  }
}
@-o-keyframes drop-63 {
  100% {
    top: 110%;
    left: 34%;
  }
}
@keyframes drop-63 {
  100% {
    top: 110%;
    left: 34%;
  }
}
.confetti-wrapper .confetti-64 {
  width: 4px;
  height: 1.6px;
  background-color: #3f51b5;
  top: -10%;
  left: 77%;
  opacity: 1.4257313595;
  -webkit-transform: rotate(320.6794729317deg);
      -ms-transform: rotate(320.6794729317deg);
       -o-transform: rotate(320.6794729317deg);
          transform: rotate(320.6794729317deg);
  -webkit-animation: drop-64 4.7557966456s 0.2089073697s infinite;
       -o-animation: drop-64 4.7557966456s 0.2089073697s infinite;
          animation: drop-64 4.7557966456s 0.2089073697s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-64 {
  100% {
    top: 110%;
    left: 92%;
  }
}
@-o-keyframes drop-64 {
  100% {
    top: 110%;
    left: 92%;
  }
}
@keyframes drop-64 {
  100% {
    top: 110%;
    left: 92%;
  }
}
.confetti-wrapper .confetti-65 {
  width: 4px;
  height: 1.6px;
  background-color: #03a9f4;
  top: -10%;
  left: 31%;
  opacity: 0.6494189982;
  -webkit-transform: rotate(213.3754275961deg);
      -ms-transform: rotate(213.3754275961deg);
       -o-transform: rotate(213.3754275961deg);
          transform: rotate(213.3754275961deg);
  -webkit-animation: drop-65 4.6807664781s 0.5437816022s infinite;
       -o-animation: drop-65 4.6807664781s 0.5437816022s infinite;
          animation: drop-65 4.6807664781s 0.5437816022s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-65 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@-o-keyframes drop-65 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@keyframes drop-65 {
  100% {
    top: 110%;
    left: 39%;
  }
}
.confetti-wrapper .confetti-66 {
  width: 9px;
  height: 3.6px;
  background-color: #009688;
  top: -10%;
  left: 48%;
  opacity: 0.6753886731;
  -webkit-transform: rotate(194.200080871deg);
      -ms-transform: rotate(194.200080871deg);
       -o-transform: rotate(194.200080871deg);
          transform: rotate(194.200080871deg);
  -webkit-animation: drop-66 4.6777981573s 0.5105886036s infinite;
       -o-animation: drop-66 4.6777981573s 0.5105886036s infinite;
          animation: drop-66 4.6777981573s 0.5105886036s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-66 {
  100% {
    top: 110%;
    left: 50%;
  }
}
@-o-keyframes drop-66 {
  100% {
    top: 110%;
    left: 50%;
  }
}
@keyframes drop-66 {
  100% {
    top: 110%;
    left: 50%;
  }
}
.confetti-wrapper .confetti-67 {
  width: 2px;
  height: 0.8px;
  background-color: #009688;
  top: -10%;
  left: 16%;
  opacity: 0.7810294909;
  -webkit-transform: rotate(341.9950906878deg);
      -ms-transform: rotate(341.9950906878deg);
       -o-transform: rotate(341.9950906878deg);
          transform: rotate(341.9950906878deg);
  -webkit-animation: drop-67 4.4114608604s 0.4313934986s infinite;
       -o-animation: drop-67 4.4114608604s 0.4313934986s infinite;
          animation: drop-67 4.4114608604s 0.4313934986s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-67 {
  100% {
    top: 110%;
    left: 31%;
  }
}
@-o-keyframes drop-67 {
  100% {
    top: 110%;
    left: 31%;
  }
}
@keyframes drop-67 {
  100% {
    top: 110%;
    left: 31%;
  }
}
.confetti-wrapper .confetti-68 {
  width: 10px;
  height: 4px;
  background-color: #8bc34a;
  top: -10%;
  left: 82%;
  opacity: 1.0727986143;
  -webkit-transform: rotate(269.9488244487deg);
      -ms-transform: rotate(269.9488244487deg);
       -o-transform: rotate(269.9488244487deg);
          transform: rotate(269.9488244487deg);
  -webkit-animation: drop-68 4.7998479463s 0.3587167453s infinite;
       -o-animation: drop-68 4.7998479463s 0.3587167453s infinite;
          animation: drop-68 4.7998479463s 0.3587167453s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-68 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@-o-keyframes drop-68 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@keyframes drop-68 {
  100% {
    top: 110%;
    left: 83%;
  }
}
.confetti-wrapper .confetti-69 {
  width: 7px;
  height: 2.8px;
  background-color: #03a9f4;
  top: -10%;
  left: 29%;
  opacity: 0.8517849451;
  -webkit-transform: rotate(278.8681896161deg);
      -ms-transform: rotate(278.8681896161deg);
       -o-transform: rotate(278.8681896161deg);
          transform: rotate(278.8681896161deg);
  -webkit-animation: drop-69 4.7716560782s 0.7248434696s infinite;
       -o-animation: drop-69 4.7716560782s 0.7248434696s infinite;
          animation: drop-69 4.7716560782s 0.7248434696s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-69 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@-o-keyframes drop-69 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@keyframes drop-69 {
  100% {
    top: 110%;
    left: 41%;
  }
}
.confetti-wrapper .confetti-70 {
  width: 10px;
  height: 4px;
  background-color: #F44336;
  top: -10%;
  left: 75%;
  opacity: 1.4495974991;
  -webkit-transform: rotate(76.9500785797deg);
      -ms-transform: rotate(76.9500785797deg);
       -o-transform: rotate(76.9500785797deg);
          transform: rotate(76.9500785797deg);
  -webkit-animation: drop-70 4.781898028s 0.751615792s infinite;
       -o-animation: drop-70 4.781898028s 0.751615792s infinite;
          animation: drop-70 4.781898028s 0.751615792s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-70 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@-o-keyframes drop-70 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@keyframes drop-70 {
  100% {
    top: 110%;
    left: 80%;
  }
}
.confetti-wrapper .confetti-71 {
  width: 7px;
  height: 2.8px;
  background-color: #ffc107;
  top: -10%;
  left: 74%;
  opacity: 0.7975772691;
  -webkit-transform: rotate(62.026136357deg);
      -ms-transform: rotate(62.026136357deg);
       -o-transform: rotate(62.026136357deg);
          transform: rotate(62.026136357deg);
  -webkit-animation: drop-71 4.6306858963s 0.2678204523s infinite;
       -o-animation: drop-71 4.6306858963s 0.2678204523s infinite;
          animation: drop-71 4.6306858963s 0.2678204523s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-71 {
  100% {
    top: 110%;
    left: 86%;
  }
}
@-o-keyframes drop-71 {
  100% {
    top: 110%;
    left: 86%;
  }
}
@keyframes drop-71 {
  100% {
    top: 110%;
    left: 86%;
  }
}
.confetti-wrapper .confetti-72 {
  width: 1px;
  height: 0.4px;
  background-color: #ffc107;
  top: -10%;
  left: 99%;
  opacity: 1.3110538735;
  -webkit-transform: rotate(251.6598137376deg);
      -ms-transform: rotate(251.6598137376deg);
       -o-transform: rotate(251.6598137376deg);
          transform: rotate(251.6598137376deg);
  -webkit-animation: drop-72 4.1272395094s 0.9008714505s infinite;
       -o-animation: drop-72 4.1272395094s 0.9008714505s infinite;
          animation: drop-72 4.1272395094s 0.9008714505s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-72 {
  100% {
    top: 110%;
    left: 105%;
  }
}
@-o-keyframes drop-72 {
  100% {
    top: 110%;
    left: 105%;
  }
}
@keyframes drop-72 {
  100% {
    top: 110%;
    left: 105%;
  }
}
.confetti-wrapper .confetti-73 {
  width: 10px;
  height: 4px;
  background-color: #8bc34a;
  top: -10%;
  left: 79%;
  opacity: 1.2056846209;
  -webkit-transform: rotate(223.9901039056deg);
      -ms-transform: rotate(223.9901039056deg);
       -o-transform: rotate(223.9901039056deg);
          transform: rotate(223.9901039056deg);
  -webkit-animation: drop-73 4.7551876208s 0.6271897548s infinite;
       -o-animation: drop-73 4.7551876208s 0.6271897548s infinite;
          animation: drop-73 4.7551876208s 0.6271897548s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-73 {
  100% {
    top: 110%;
    left: 93%;
  }
}
@-o-keyframes drop-73 {
  100% {
    top: 110%;
    left: 93%;
  }
}
@keyframes drop-73 {
  100% {
    top: 110%;
    left: 93%;
  }
}
.confetti-wrapper .confetti-74 {
  width: 2px;
  height: 0.8px;
  background-color: #ffc107;
  top: -10%;
  left: 100%;
  opacity: 0.5776359026;
  -webkit-transform: rotate(121.0079931596deg);
      -ms-transform: rotate(121.0079931596deg);
       -o-transform: rotate(121.0079931596deg);
          transform: rotate(121.0079931596deg);
  -webkit-animation: drop-74 4.5345579148s 0.80622495s infinite;
       -o-animation: drop-74 4.5345579148s 0.80622495s infinite;
          animation: drop-74 4.5345579148s 0.80622495s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-74 {
  100% {
    top: 110%;
    left: 104%;
  }
}
@-o-keyframes drop-74 {
  100% {
    top: 110%;
    left: 104%;
  }
}
@keyframes drop-74 {
  100% {
    top: 110%;
    left: 104%;
  }
}
.confetti-wrapper .confetti-75 {
  width: 10px;
  height: 4px;
  background-color: #00bcd4;
  top: -10%;
  left: 49%;
  opacity: 1.1712107429;
  -webkit-transform: rotate(56.4990348823deg);
      -ms-transform: rotate(56.4990348823deg);
       -o-transform: rotate(56.4990348823deg);
          transform: rotate(56.4990348823deg);
  -webkit-animation: drop-75 4.3934862498s 0.5709526384s infinite;
       -o-animation: drop-75 4.3934862498s 0.5709526384s infinite;
          animation: drop-75 4.3934862498s 0.5709526384s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-75 {
  100% {
    top: 110%;
    left: 54%;
  }
}
@-o-keyframes drop-75 {
  100% {
    top: 110%;
    left: 54%;
  }
}
@keyframes drop-75 {
  100% {
    top: 110%;
    left: 54%;
  }
}
.confetti-wrapper .confetti-76 {
  width: 2px;
  height: 0.8px;
  background-color: #009688;
  top: -10%;
  left: 24%;
  opacity: 1.1402185645;
  -webkit-transform: rotate(271.0711720597deg);
      -ms-transform: rotate(271.0711720597deg);
       -o-transform: rotate(271.0711720597deg);
          transform: rotate(271.0711720597deg);
  -webkit-animation: drop-76 4.6678623901s 0.9199026233s infinite;
       -o-animation: drop-76 4.6678623901s 0.9199026233s infinite;
          animation: drop-76 4.6678623901s 0.9199026233s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-76 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@-o-keyframes drop-76 {
  100% {
    top: 110%;
    left: 39%;
  }
}
@keyframes drop-76 {
  100% {
    top: 110%;
    left: 39%;
  }
}
.confetti-wrapper .confetti-77 {
  width: 6px;
  height: 2.4px;
  background-color: #673ab7;
  top: -10%;
  left: 64%;
  opacity: 0.9977934726;
  -webkit-transform: rotate(243.6812201606deg);
      -ms-transform: rotate(243.6812201606deg);
       -o-transform: rotate(243.6812201606deg);
          transform: rotate(243.6812201606deg);
  -webkit-animation: drop-77 4.9417247461s 0.4254427183s infinite;
       -o-animation: drop-77 4.9417247461s 0.4254427183s infinite;
          animation: drop-77 4.9417247461s 0.4254427183s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-77 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@-o-keyframes drop-77 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@keyframes drop-77 {
  100% {
    top: 110%;
    left: 68%;
  }
}
.confetti-wrapper .confetti-78 {
  width: 10px;
  height: 4px;
  background-color: #03a9f4;
  top: -10%;
  left: -3%;
  opacity: 1.1944756104;
  -webkit-transform: rotate(306.7250230569deg);
      -ms-transform: rotate(306.7250230569deg);
       -o-transform: rotate(306.7250230569deg);
          transform: rotate(306.7250230569deg);
  -webkit-animation: drop-78 4.6217791983s 0.8405398811s infinite;
       -o-animation: drop-78 4.6217791983s 0.8405398811s infinite;
          animation: drop-78 4.6217791983s 0.8405398811s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-78 {
  100% {
    top: 110%;
    left: 3%;
  }
}
@-o-keyframes drop-78 {
  100% {
    top: 110%;
    left: 3%;
  }
}
@keyframes drop-78 {
  100% {
    top: 110%;
    left: 3%;
  }
}
.confetti-wrapper .confetti-79 {
  width: 9px;
  height: 3.6px;
  background-color: #03a9f4;
  top: -10%;
  left: 91%;
  opacity: 1.2011857369;
  -webkit-transform: rotate(317.4488378247deg);
      -ms-transform: rotate(317.4488378247deg);
       -o-transform: rotate(317.4488378247deg);
          transform: rotate(317.4488378247deg);
  -webkit-animation: drop-79 4.9615085889s 0.6505170693s infinite;
       -o-animation: drop-79 4.9615085889s 0.6505170693s infinite;
          animation: drop-79 4.9615085889s 0.6505170693s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-79 {
  100% {
    top: 110%;
    left: 104%;
  }
}
@-o-keyframes drop-79 {
  100% {
    top: 110%;
    left: 104%;
  }
}
@keyframes drop-79 {
  100% {
    top: 110%;
    left: 104%;
  }
}
.confetti-wrapper .confetti-80 {
  width: 4px;
  height: 1.6px;
  background-color: #03a9f4;
  top: -10%;
  left: 32%;
  opacity: 1.4153585497;
  -webkit-transform: rotate(164.8140092756deg);
      -ms-transform: rotate(164.8140092756deg);
       -o-transform: rotate(164.8140092756deg);
          transform: rotate(164.8140092756deg);
  -webkit-animation: drop-80 4.9462537083s 0.8335247424s infinite;
       -o-animation: drop-80 4.9462537083s 0.8335247424s infinite;
          animation: drop-80 4.9462537083s 0.8335247424s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-80 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@-o-keyframes drop-80 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@keyframes drop-80 {
  100% {
    top: 110%;
    left: 35%;
  }
}
.confetti-wrapper .confetti-81 {
  width: 2px;
  height: 0.8px;
  background-color: #ff5722;
  top: -10%;
  left: 10%;
  opacity: 0.8101769393;
  -webkit-transform: rotate(15.1056746895deg);
      -ms-transform: rotate(15.1056746895deg);
       -o-transform: rotate(15.1056746895deg);
          transform: rotate(15.1056746895deg);
  -webkit-animation: drop-81 4.7692145711s 0.8676709013s infinite;
       -o-animation: drop-81 4.7692145711s 0.8676709013s infinite;
          animation: drop-81 4.7692145711s 0.8676709013s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-81 {
  100% {
    top: 110%;
    left: 11%;
  }
}
@-o-keyframes drop-81 {
  100% {
    top: 110%;
    left: 11%;
  }
}
@keyframes drop-81 {
  100% {
    top: 110%;
    left: 11%;
  }
}
.confetti-wrapper .confetti-82 {
  width: 10px;
  height: 4px;
  background-color: #ffeb3b;
  top: -10%;
  left: 9%;
  opacity: 1.4222492053;
  -webkit-transform: rotate(152.9066545937deg);
      -ms-transform: rotate(152.9066545937deg);
       -o-transform: rotate(152.9066545937deg);
          transform: rotate(152.9066545937deg);
  -webkit-animation: drop-82 4.8552242938s 0.800515232s infinite;
       -o-animation: drop-82 4.8552242938s 0.800515232s infinite;
          animation: drop-82 4.8552242938s 0.800515232s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-82 {
  100% {
    top: 110%;
    left: 12%;
  }
}
@-o-keyframes drop-82 {
  100% {
    top: 110%;
    left: 12%;
  }
}
@keyframes drop-82 {
  100% {
    top: 110%;
    left: 12%;
  }
}
.confetti-wrapper .confetti-83 {
  width: 9px;
  height: 3.6px;
  background-color: #673ab7;
  top: -10%;
  left: 60%;
  opacity: 0.8471564719;
  -webkit-transform: rotate(306.2086077684deg);
      -ms-transform: rotate(306.2086077684deg);
       -o-transform: rotate(306.2086077684deg);
          transform: rotate(306.2086077684deg);
  -webkit-animation: drop-83 4.5547405553s 0.0355799639s infinite;
       -o-animation: drop-83 4.5547405553s 0.0355799639s infinite;
          animation: drop-83 4.5547405553s 0.0355799639s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-83 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@-o-keyframes drop-83 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@keyframes drop-83 {
  100% {
    top: 110%;
    left: 68%;
  }
}
.confetti-wrapper .confetti-84 {
  width: 9px;
  height: 3.6px;
  background-color: #8bc34a;
  top: -10%;
  left: 49%;
  opacity: 1.0171715287;
  -webkit-transform: rotate(249.1928653825deg);
      -ms-transform: rotate(249.1928653825deg);
       -o-transform: rotate(249.1928653825deg);
          transform: rotate(249.1928653825deg);
  -webkit-animation: drop-84 4.9406956162s 0.3070589376s infinite;
       -o-animation: drop-84 4.9406956162s 0.3070589376s infinite;
          animation: drop-84 4.9406956162s 0.3070589376s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-84 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@-o-keyframes drop-84 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@keyframes drop-84 {
  100% {
    top: 110%;
    left: 53%;
  }
}
.confetti-wrapper .confetti-85 {
  width: 2px;
  height: 0.8px;
  background-color: #03a9f4;
  top: -10%;
  left: 60%;
  opacity: 0.6470949114;
  -webkit-transform: rotate(96.3479528545deg);
      -ms-transform: rotate(96.3479528545deg);
       -o-transform: rotate(96.3479528545deg);
          transform: rotate(96.3479528545deg);
  -webkit-animation: drop-85 4.6480993949s 0.8565283093s infinite;
       -o-animation: drop-85 4.6480993949s 0.8565283093s infinite;
          animation: drop-85 4.6480993949s 0.8565283093s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-85 {
  100% {
    top: 110%;
    left: 63%;
  }
}
@-o-keyframes drop-85 {
  100% {
    top: 110%;
    left: 63%;
  }
}
@keyframes drop-85 {
  100% {
    top: 110%;
    left: 63%;
  }
}
.confetti-wrapper .confetti-86 {
  width: 2px;
  height: 0.8px;
  background-color: #ffc107;
  top: -10%;
  left: 39%;
  opacity: 1.497306012;
  -webkit-transform: rotate(351.8893338951deg);
      -ms-transform: rotate(351.8893338951deg);
       -o-transform: rotate(351.8893338951deg);
          transform: rotate(351.8893338951deg);
  -webkit-animation: drop-86 4.0056002159s 0.7577082192s infinite;
       -o-animation: drop-86 4.0056002159s 0.7577082192s infinite;
          animation: drop-86 4.0056002159s 0.7577082192s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-86 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@-o-keyframes drop-86 {
  100% {
    top: 110%;
    left: 43%;
  }
}
@keyframes drop-86 {
  100% {
    top: 110%;
    left: 43%;
  }
}
.confetti-wrapper .confetti-87 {
  width: 2px;
  height: 0.8px;
  background-color: #ffeb3b;
  top: -10%;
  left: 37%;
  opacity: 1.2697937731;
  -webkit-transform: rotate(35.3138085155deg);
      -ms-transform: rotate(35.3138085155deg);
       -o-transform: rotate(35.3138085155deg);
          transform: rotate(35.3138085155deg);
  -webkit-animation: drop-87 4.9001677309s 0.1196791985s infinite;
       -o-animation: drop-87 4.9001677309s 0.1196791985s infinite;
          animation: drop-87 4.9001677309s 0.1196791985s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-87 {
  100% {
    top: 110%;
    left: 45%;
  }
}
@-o-keyframes drop-87 {
  100% {
    top: 110%;
    left: 45%;
  }
}
@keyframes drop-87 {
  100% {
    top: 110%;
    left: 45%;
  }
}
.confetti-wrapper .confetti-88 {
  width: 8px;
  height: 3.2px;
  background-color: #e91e63;
  top: -10%;
  left: 76%;
  opacity: 1.1201828774;
  -webkit-transform: rotate(192.8698918838deg);
      -ms-transform: rotate(192.8698918838deg);
       -o-transform: rotate(192.8698918838deg);
          transform: rotate(192.8698918838deg);
  -webkit-animation: drop-88 4.100605852s 0.2131862223s infinite;
       -o-animation: drop-88 4.100605852s 0.2131862223s infinite;
          animation: drop-88 4.100605852s 0.2131862223s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-88 {
  100% {
    top: 110%;
    left: 82%;
  }
}
@-o-keyframes drop-88 {
  100% {
    top: 110%;
    left: 82%;
  }
}
@keyframes drop-88 {
  100% {
    top: 110%;
    left: 82%;
  }
}
.confetti-wrapper .confetti-89 {
  width: 5px;
  height: 2px;
  background-color: #ff9800;
  top: -10%;
  left: 43%;
  opacity: 0.5986234137;
  -webkit-transform: rotate(195.8067713623deg);
      -ms-transform: rotate(195.8067713623deg);
       -o-transform: rotate(195.8067713623deg);
          transform: rotate(195.8067713623deg);
  -webkit-animation: drop-89 4.0856695808s 0.7401722836s infinite;
       -o-animation: drop-89 4.0856695808s 0.7401722836s infinite;
          animation: drop-89 4.0856695808s 0.7401722836s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-89 {
  100% {
    top: 110%;
    left: 48%;
  }
}
@-o-keyframes drop-89 {
  100% {
    top: 110%;
    left: 48%;
  }
}
@keyframes drop-89 {
  100% {
    top: 110%;
    left: 48%;
  }
}
.confetti-wrapper .confetti-90 {
  width: 2px;
  height: 0.8px;
  background-color: #3f51b5;
  top: -10%;
  left: 94%;
  opacity: 0.837986051;
  -webkit-transform: rotate(144.4221235728deg);
      -ms-transform: rotate(144.4221235728deg);
       -o-transform: rotate(144.4221235728deg);
          transform: rotate(144.4221235728deg);
  -webkit-animation: drop-90 4.2130712302s 0.0922437048s infinite;
       -o-animation: drop-90 4.2130712302s 0.0922437048s infinite;
          animation: drop-90 4.2130712302s 0.0922437048s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-90 {
  100% {
    top: 110%;
    left: 95%;
  }
}
@-o-keyframes drop-90 {
  100% {
    top: 110%;
    left: 95%;
  }
}
@keyframes drop-90 {
  100% {
    top: 110%;
    left: 95%;
  }
}
.confetti-wrapper .confetti-91 {
  width: 9px;
  height: 3.6px;
  background-color: #ffc107;
  top: -10%;
  left: 88%;
  opacity: 0.5682542663;
  -webkit-transform: rotate(273.5058416297deg);
      -ms-transform: rotate(273.5058416297deg);
       -o-transform: rotate(273.5058416297deg);
          transform: rotate(273.5058416297deg);
  -webkit-animation: drop-91 4.6231892465s 0.2780017978s infinite;
       -o-animation: drop-91 4.6231892465s 0.2780017978s infinite;
          animation: drop-91 4.6231892465s 0.2780017978s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-91 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@-o-keyframes drop-91 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@keyframes drop-91 {
  100% {
    top: 110%;
    left: 100%;
  }
}
.confetti-wrapper .confetti-92 {
  width: 2px;
  height: 0.8px;
  background-color: #cddc39;
  top: -10%;
  left: 44%;
  opacity: 1.1433004386;
  -webkit-transform: rotate(40.656927559deg);
      -ms-transform: rotate(40.656927559deg);
       -o-transform: rotate(40.656927559deg);
          transform: rotate(40.656927559deg);
  -webkit-animation: drop-92 4.3354682837s 0.990620578s infinite;
       -o-animation: drop-92 4.3354682837s 0.990620578s infinite;
          animation: drop-92 4.3354682837s 0.990620578s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-92 {
  100% {
    top: 110%;
    left: 59%;
  }
}
@-o-keyframes drop-92 {
  100% {
    top: 110%;
    left: 59%;
  }
}
@keyframes drop-92 {
  100% {
    top: 110%;
    left: 59%;
  }
}
.confetti-wrapper .confetti-93 {
  width: 10px;
  height: 4px;
  background-color: #9c27b0;
  top: -10%;
  left: 92%;
  opacity: 0.8566844557;
  -webkit-transform: rotate(173.1681803188deg);
      -ms-transform: rotate(173.1681803188deg);
       -o-transform: rotate(173.1681803188deg);
          transform: rotate(173.1681803188deg);
  -webkit-animation: drop-93 4.8241592583s 0.5777027878s infinite;
       -o-animation: drop-93 4.8241592583s 0.5777027878s infinite;
          animation: drop-93 4.8241592583s 0.5777027878s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-93 {
  100% {
    top: 110%;
    left: 107%;
  }
}
@-o-keyframes drop-93 {
  100% {
    top: 110%;
    left: 107%;
  }
}
@keyframes drop-93 {
  100% {
    top: 110%;
    left: 107%;
  }
}
.confetti-wrapper .confetti-94 {
  width: 8px;
  height: 3.2px;
  background-color: #e91e63;
  top: -10%;
  left: 5%;
  opacity: 1.3382344516;
  -webkit-transform: rotate(277.9709261429deg);
      -ms-transform: rotate(277.9709261429deg);
       -o-transform: rotate(277.9709261429deg);
          transform: rotate(277.9709261429deg);
  -webkit-animation: drop-94 4.0516369483s 0.2085636845s infinite;
       -o-animation: drop-94 4.0516369483s 0.2085636845s infinite;
          animation: drop-94 4.0516369483s 0.2085636845s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-94 {
  100% {
    top: 110%;
    left: 16%;
  }
}
@-o-keyframes drop-94 {
  100% {
    top: 110%;
    left: 16%;
  }
}
@keyframes drop-94 {
  100% {
    top: 110%;
    left: 16%;
  }
}
.confetti-wrapper .confetti-95 {
  width: 6px;
  height: 2.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 50%;
  opacity: 1.0887528057;
  -webkit-transform: rotate(197.2339142361deg);
      -ms-transform: rotate(197.2339142361deg);
       -o-transform: rotate(197.2339142361deg);
          transform: rotate(197.2339142361deg);
  -webkit-animation: drop-95 4.2996098205s 0.0045432755s infinite;
       -o-animation: drop-95 4.2996098205s 0.0045432755s infinite;
          animation: drop-95 4.2996098205s 0.0045432755s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-95 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@-o-keyframes drop-95 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@keyframes drop-95 {
  100% {
    top: 110%;
    left: 53%;
  }
}
.confetti-wrapper .confetti-96 {
  width: 4px;
  height: 1.6px;
  background-color: #ff9800;
  top: -10%;
  left: 77%;
  opacity: 1.2667134963;
  -webkit-transform: rotate(80.9273149365deg);
      -ms-transform: rotate(80.9273149365deg);
       -o-transform: rotate(80.9273149365deg);
          transform: rotate(80.9273149365deg);
  -webkit-animation: drop-96 4.1864065912s 0.7908932762s infinite;
       -o-animation: drop-96 4.1864065912s 0.7908932762s infinite;
          animation: drop-96 4.1864065912s 0.7908932762s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-96 {
  100% {
    top: 110%;
    left: 91%;
  }
}
@-o-keyframes drop-96 {
  100% {
    top: 110%;
    left: 91%;
  }
}
@keyframes drop-96 {
  100% {
    top: 110%;
    left: 91%;
  }
}
.confetti-wrapper .confetti-97 {
  width: 6px;
  height: 2.4px;
  background-color: #673ab7;
  top: -10%;
  left: 93%;
  opacity: 1.2378728829;
  -webkit-transform: rotate(228.0173840964deg);
      -ms-transform: rotate(228.0173840964deg);
       -o-transform: rotate(228.0173840964deg);
          transform: rotate(228.0173840964deg);
  -webkit-animation: drop-97 4.1777907849s 0.8830466781s infinite;
       -o-animation: drop-97 4.1777907849s 0.8830466781s infinite;
          animation: drop-97 4.1777907849s 0.8830466781s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-97 {
  100% {
    top: 110%;
    left: 108%;
  }
}
@-o-keyframes drop-97 {
  100% {
    top: 110%;
    left: 108%;
  }
}
@keyframes drop-97 {
  100% {
    top: 110%;
    left: 108%;
  }
}
.confetti-wrapper .confetti-98 {
  width: 10px;
  height: 4px;
  background-color: #3f51b5;
  top: -10%;
  left: 42%;
  opacity: 0.9373569423;
  -webkit-transform: rotate(281.8463909831deg);
      -ms-transform: rotate(281.8463909831deg);
       -o-transform: rotate(281.8463909831deg);
          transform: rotate(281.8463909831deg);
  -webkit-animation: drop-98 4.8335921108s 0.057733545s infinite;
       -o-animation: drop-98 4.8335921108s 0.057733545s infinite;
          animation: drop-98 4.8335921108s 0.057733545s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-98 {
  100% {
    top: 110%;
    left: 46%;
  }
}
@-o-keyframes drop-98 {
  100% {
    top: 110%;
    left: 46%;
  }
}
@keyframes drop-98 {
  100% {
    top: 110%;
    left: 46%;
  }
}
.confetti-wrapper .confetti-99 {
  width: 3px;
  height: 1.2px;
  background-color: #cddc39;
  top: -10%;
  left: 43%;
  opacity: 0.7547074871;
  -webkit-transform: rotate(326.8678100513deg);
      -ms-transform: rotate(326.8678100513deg);
       -o-transform: rotate(326.8678100513deg);
          transform: rotate(326.8678100513deg);
  -webkit-animation: drop-99 4.3149090977s 0.0154008545s infinite;
       -o-animation: drop-99 4.3149090977s 0.0154008545s infinite;
          animation: drop-99 4.3149090977s 0.0154008545s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-99 {
  100% {
    top: 110%;
    left: 57%;
  }
}
@-o-keyframes drop-99 {
  100% {
    top: 110%;
    left: 57%;
  }
}
@keyframes drop-99 {
  100% {
    top: 110%;
    left: 57%;
  }
}
.confetti-wrapper .confetti-100 {
  width: 10px;
  height: 4px;
  background-color: #673ab7;
  top: -10%;
  left: 31%;
  opacity: 0.8443087699;
  -webkit-transform: rotate(239.1406405094deg);
      -ms-transform: rotate(239.1406405094deg);
       -o-transform: rotate(239.1406405094deg);
          transform: rotate(239.1406405094deg);
  -webkit-animation: drop-100 4.7574147227s 0.5882168997s infinite;
       -o-animation: drop-100 4.7574147227s 0.5882168997s infinite;
          animation: drop-100 4.7574147227s 0.5882168997s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-100 {
  100% {
    top: 110%;
    left: 40%;
  }
}
@-o-keyframes drop-100 {
  100% {
    top: 110%;
    left: 40%;
  }
}
@keyframes drop-100 {
  100% {
    top: 110%;
    left: 40%;
  }
}
.confetti-wrapper .confetti-101 {
  width: 3px;
  height: 1.2px;
  background-color: #ffeb3b;
  top: -10%;
  left: 26%;
  opacity: 1.3052139891;
  -webkit-transform: rotate(286.8645205675deg);
      -ms-transform: rotate(286.8645205675deg);
       -o-transform: rotate(286.8645205675deg);
          transform: rotate(286.8645205675deg);
  -webkit-animation: drop-101 4.3271068737s 0.128759947s infinite;
       -o-animation: drop-101 4.3271068737s 0.128759947s infinite;
          animation: drop-101 4.3271068737s 0.128759947s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-101 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@-o-keyframes drop-101 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@keyframes drop-101 {
  100% {
    top: 110%;
    left: 35%;
  }
}
.confetti-wrapper .confetti-102 {
  width: 9px;
  height: 3.6px;
  background-color: #3f51b5;
  top: -10%;
  left: 2%;
  opacity: 1.0909011322;
  -webkit-transform: rotate(76.2386346145deg);
      -ms-transform: rotate(76.2386346145deg);
       -o-transform: rotate(76.2386346145deg);
          transform: rotate(76.2386346145deg);
  -webkit-animation: drop-102 4.6349142808s 0.3156224212s infinite;
       -o-animation: drop-102 4.6349142808s 0.3156224212s infinite;
          animation: drop-102 4.6349142808s 0.3156224212s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-102 {
  100% {
    top: 110%;
    left: 15%;
  }
}
@-o-keyframes drop-102 {
  100% {
    top: 110%;
    left: 15%;
  }
}
@keyframes drop-102 {
  100% {
    top: 110%;
    left: 15%;
  }
}
.confetti-wrapper .confetti-103 {
  width: 4px;
  height: 1.6px;
  background-color: #009688;
  top: -10%;
  left: 45%;
  opacity: 0.6263708141;
  -webkit-transform: rotate(39.3726278569deg);
      -ms-transform: rotate(39.3726278569deg);
       -o-transform: rotate(39.3726278569deg);
          transform: rotate(39.3726278569deg);
  -webkit-animation: drop-103 4.640400123s 0.7893835838s infinite;
       -o-animation: drop-103 4.640400123s 0.7893835838s infinite;
          animation: drop-103 4.640400123s 0.7893835838s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-103 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@-o-keyframes drop-103 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@keyframes drop-103 {
  100% {
    top: 110%;
    left: 53%;
  }
}
.confetti-wrapper .confetti-104 {
  width: 7px;
  height: 2.8px;
  background-color: #ffeb3b;
  top: -10%;
  left: 23%;
  opacity: 1.399851354;
  -webkit-transform: rotate(263.9089659308deg);
      -ms-transform: rotate(263.9089659308deg);
       -o-transform: rotate(263.9089659308deg);
          transform: rotate(263.9089659308deg);
  -webkit-animation: drop-104 4.9248956075s 0.3646282005s infinite;
       -o-animation: drop-104 4.9248956075s 0.3646282005s infinite;
          animation: drop-104 4.9248956075s 0.3646282005s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-104 {
  100% {
    top: 110%;
    left: 24%;
  }
}
@-o-keyframes drop-104 {
  100% {
    top: 110%;
    left: 24%;
  }
}
@keyframes drop-104 {
  100% {
    top: 110%;
    left: 24%;
  }
}
.confetti-wrapper .confetti-105 {
  width: 1px;
  height: 0.4px;
  background-color: #009688;
  top: -10%;
  left: 48%;
  opacity: 1.4517932236;
  -webkit-transform: rotate(252.6117860991deg);
      -ms-transform: rotate(252.6117860991deg);
       -o-transform: rotate(252.6117860991deg);
          transform: rotate(252.6117860991deg);
  -webkit-animation: drop-105 4.5763408931s 0.1925429187s infinite;
       -o-animation: drop-105 4.5763408931s 0.1925429187s infinite;
          animation: drop-105 4.5763408931s 0.1925429187s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-105 {
  100% {
    top: 110%;
    left: 52%;
  }
}
@-o-keyframes drop-105 {
  100% {
    top: 110%;
    left: 52%;
  }
}
@keyframes drop-105 {
  100% {
    top: 110%;
    left: 52%;
  }
}
.confetti-wrapper .confetti-106 {
  width: 4px;
  height: 1.6px;
  background-color: #ffeb3b;
  top: -10%;
  left: 64%;
  opacity: 0.8768935733;
  -webkit-transform: rotate(48.9313687003deg);
      -ms-transform: rotate(48.9313687003deg);
       -o-transform: rotate(48.9313687003deg);
          transform: rotate(48.9313687003deg);
  -webkit-animation: drop-106 4.8967656675s 0.1284053212s infinite;
       -o-animation: drop-106 4.8967656675s 0.1284053212s infinite;
          animation: drop-106 4.8967656675s 0.1284053212s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-106 {
  100% {
    top: 110%;
    left: 71%;
  }
}
@-o-keyframes drop-106 {
  100% {
    top: 110%;
    left: 71%;
  }
}
@keyframes drop-106 {
  100% {
    top: 110%;
    left: 71%;
  }
}
.confetti-wrapper .confetti-107 {
  width: 1px;
  height: 0.4px;
  background-color: #ff9800;
  top: -10%;
  left: 37%;
  opacity: 0.6762763298;
  -webkit-transform: rotate(328.7758744878deg);
      -ms-transform: rotate(328.7758744878deg);
       -o-transform: rotate(328.7758744878deg);
          transform: rotate(328.7758744878deg);
  -webkit-animation: drop-107 4.9427719366s 0.9691856953s infinite;
       -o-animation: drop-107 4.9427719366s 0.9691856953s infinite;
          animation: drop-107 4.9427719366s 0.9691856953s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-107 {
  100% {
    top: 110%;
    left: 44%;
  }
}
@-o-keyframes drop-107 {
  100% {
    top: 110%;
    left: 44%;
  }
}
@keyframes drop-107 {
  100% {
    top: 110%;
    left: 44%;
  }
}
.confetti-wrapper .confetti-108 {
  width: 3px;
  height: 1.2px;
  background-color: #673ab7;
  top: -10%;
  left: 48%;
  opacity: 1.1329643487;
  -webkit-transform: rotate(33.3792775151deg);
      -ms-transform: rotate(33.3792775151deg);
       -o-transform: rotate(33.3792775151deg);
          transform: rotate(33.3792775151deg);
  -webkit-animation: drop-108 4.0529300832s 0.8578858085s infinite;
       -o-animation: drop-108 4.0529300832s 0.8578858085s infinite;
          animation: drop-108 4.0529300832s 0.8578858085s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-108 {
  100% {
    top: 110%;
    left: 52%;
  }
}
@-o-keyframes drop-108 {
  100% {
    top: 110%;
    left: 52%;
  }
}
@keyframes drop-108 {
  100% {
    top: 110%;
    left: 52%;
  }
}
.confetti-wrapper .confetti-109 {
  width: 2px;
  height: 0.8px;
  background-color: #ff9800;
  top: -10%;
  left: 99%;
  opacity: 1.0270799048;
  -webkit-transform: rotate(169.6081795829deg);
      -ms-transform: rotate(169.6081795829deg);
       -o-transform: rotate(169.6081795829deg);
          transform: rotate(169.6081795829deg);
  -webkit-animation: drop-109 4.2052378934s 0.386172078s infinite;
       -o-animation: drop-109 4.2052378934s 0.386172078s infinite;
          animation: drop-109 4.2052378934s 0.386172078s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-109 {
  100% {
    top: 110%;
    left: 102%;
  }
}
@-o-keyframes drop-109 {
  100% {
    top: 110%;
    left: 102%;
  }
}
@keyframes drop-109 {
  100% {
    top: 110%;
    left: 102%;
  }
}
.confetti-wrapper .confetti-110 {
  width: 9px;
  height: 3.6px;
  background-color: #4CAF50;
  top: -10%;
  left: 15%;
  opacity: 1.0302407743;
  -webkit-transform: rotate(306.5649372094deg);
      -ms-transform: rotate(306.5649372094deg);
       -o-transform: rotate(306.5649372094deg);
          transform: rotate(306.5649372094deg);
  -webkit-animation: drop-110 4.1486665586s 0.0205020873s infinite;
       -o-animation: drop-110 4.1486665586s 0.0205020873s infinite;
          animation: drop-110 4.1486665586s 0.0205020873s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-110 {
  100% {
    top: 110%;
    left: 25%;
  }
}
@-o-keyframes drop-110 {
  100% {
    top: 110%;
    left: 25%;
  }
}
@keyframes drop-110 {
  100% {
    top: 110%;
    left: 25%;
  }
}
.confetti-wrapper .confetti-111 {
  width: 1px;
  height: 0.4px;
  background-color: #e91e63;
  top: -10%;
  left: 65%;
  opacity: 1.082939263;
  -webkit-transform: rotate(145.3396292496deg);
      -ms-transform: rotate(145.3396292496deg);
       -o-transform: rotate(145.3396292496deg);
          transform: rotate(145.3396292496deg);
  -webkit-animation: drop-111 4.5678948625s 0.5586743919s infinite;
       -o-animation: drop-111 4.5678948625s 0.5586743919s infinite;
          animation: drop-111 4.5678948625s 0.5586743919s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-111 {
  100% {
    top: 110%;
    left: 69%;
  }
}
@-o-keyframes drop-111 {
  100% {
    top: 110%;
    left: 69%;
  }
}
@keyframes drop-111 {
  100% {
    top: 110%;
    left: 69%;
  }
}
.confetti-wrapper .confetti-112 {
  width: 9px;
  height: 3.6px;
  background-color: #F44336;
  top: -10%;
  left: 77%;
  opacity: 0.9997291266;
  -webkit-transform: rotate(322.7843116321deg);
      -ms-transform: rotate(322.7843116321deg);
       -o-transform: rotate(322.7843116321deg);
          transform: rotate(322.7843116321deg);
  -webkit-animation: drop-112 4.7602203307s 0.1963473658s infinite;
       -o-animation: drop-112 4.7602203307s 0.1963473658s infinite;
          animation: drop-112 4.7602203307s 0.1963473658s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-112 {
  100% {
    top: 110%;
    left: 81%;
  }
}
@-o-keyframes drop-112 {
  100% {
    top: 110%;
    left: 81%;
  }
}
@keyframes drop-112 {
  100% {
    top: 110%;
    left: 81%;
  }
}
.confetti-wrapper .confetti-113 {
  width: 10px;
  height: 4px;
  background-color: #ffc107;
  top: -10%;
  left: 99%;
  opacity: 0.9314570756;
  -webkit-transform: rotate(74.6665858517deg);
      -ms-transform: rotate(74.6665858517deg);
       -o-transform: rotate(74.6665858517deg);
          transform: rotate(74.6665858517deg);
  -webkit-animation: drop-113 4.6972367657s 0.9207414895s infinite;
       -o-animation: drop-113 4.6972367657s 0.9207414895s infinite;
          animation: drop-113 4.6972367657s 0.9207414895s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-113 {
  100% {
    top: 110%;
    left: 106%;
  }
}
@-o-keyframes drop-113 {
  100% {
    top: 110%;
    left: 106%;
  }
}
@keyframes drop-113 {
  100% {
    top: 110%;
    left: 106%;
  }
}
.confetti-wrapper .confetti-114 {
  width: 9px;
  height: 3.6px;
  background-color: #F44336;
  top: -10%;
  left: 7%;
  opacity: 1.307907815;
  -webkit-transform: rotate(19.4163511975deg);
      -ms-transform: rotate(19.4163511975deg);
       -o-transform: rotate(19.4163511975deg);
          transform: rotate(19.4163511975deg);
  -webkit-animation: drop-114 4.2515743391s 0.083116692s infinite;
       -o-animation: drop-114 4.2515743391s 0.083116692s infinite;
          animation: drop-114 4.2515743391s 0.083116692s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-114 {
  100% {
    top: 110%;
    left: 18%;
  }
}
@-o-keyframes drop-114 {
  100% {
    top: 110%;
    left: 18%;
  }
}
@keyframes drop-114 {
  100% {
    top: 110%;
    left: 18%;
  }
}
.confetti-wrapper .confetti-115 {
  width: 2px;
  height: 0.8px;
  background-color: #009688;
  top: -10%;
  left: 10%;
  opacity: 1.1689211317;
  -webkit-transform: rotate(197.850267281deg);
      -ms-transform: rotate(197.850267281deg);
       -o-transform: rotate(197.850267281deg);
          transform: rotate(197.850267281deg);
  -webkit-animation: drop-115 4.5059051095s 0.4069618458s infinite;
       -o-animation: drop-115 4.5059051095s 0.4069618458s infinite;
          animation: drop-115 4.5059051095s 0.4069618458s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-115 {
  100% {
    top: 110%;
    left: 18%;
  }
}
@-o-keyframes drop-115 {
  100% {
    top: 110%;
    left: 18%;
  }
}
@keyframes drop-115 {
  100% {
    top: 110%;
    left: 18%;
  }
}
.confetti-wrapper .confetti-116 {
  width: 1px;
  height: 0.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 64%;
  opacity: 0.7524086345;
  -webkit-transform: rotate(52.1644684164deg);
      -ms-transform: rotate(52.1644684164deg);
       -o-transform: rotate(52.1644684164deg);
          transform: rotate(52.1644684164deg);
  -webkit-animation: drop-116 4.6991579344s 0.112524455s infinite;
       -o-animation: drop-116 4.6991579344s 0.112524455s infinite;
          animation: drop-116 4.6991579344s 0.112524455s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-116 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@-o-keyframes drop-116 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@keyframes drop-116 {
  100% {
    top: 110%;
    left: 76%;
  }
}
.confetti-wrapper .confetti-117 {
  width: 4px;
  height: 1.6px;
  background-color: #4CAF50;
  top: -10%;
  left: 35%;
  opacity: 0.8792424834;
  -webkit-transform: rotate(212.2905986681deg);
      -ms-transform: rotate(212.2905986681deg);
       -o-transform: rotate(212.2905986681deg);
          transform: rotate(212.2905986681deg);
  -webkit-animation: drop-117 4.3166604819s 0.5085568753s infinite;
       -o-animation: drop-117 4.3166604819s 0.5085568753s infinite;
          animation: drop-117 4.3166604819s 0.5085568753s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-117 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@-o-keyframes drop-117 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@keyframes drop-117 {
  100% {
    top: 110%;
    left: 41%;
  }
}
.confetti-wrapper .confetti-118 {
  width: 6px;
  height: 2.4px;
  background-color: #ffeb3b;
  top: -10%;
  left: 79%;
  opacity: 0.800365271;
  -webkit-transform: rotate(79.0656293029deg);
      -ms-transform: rotate(79.0656293029deg);
       -o-transform: rotate(79.0656293029deg);
          transform: rotate(79.0656293029deg);
  -webkit-animation: drop-118 4.3772099113s 0.9596755118s infinite;
       -o-animation: drop-118 4.3772099113s 0.9596755118s infinite;
          animation: drop-118 4.3772099113s 0.9596755118s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-118 {
  100% {
    top: 110%;
    left: 92%;
  }
}
@-o-keyframes drop-118 {
  100% {
    top: 110%;
    left: 92%;
  }
}
@keyframes drop-118 {
  100% {
    top: 110%;
    left: 92%;
  }
}
.confetti-wrapper .confetti-119 {
  width: 8px;
  height: 3.2px;
  background-color: #4CAF50;
  top: -10%;
  left: 4%;
  opacity: 1.4861679698;
  -webkit-transform: rotate(131.7532046414deg);
      -ms-transform: rotate(131.7532046414deg);
       -o-transform: rotate(131.7532046414deg);
          transform: rotate(131.7532046414deg);
  -webkit-animation: drop-119 4.5865223668s 0.8935009461s infinite;
       -o-animation: drop-119 4.5865223668s 0.8935009461s infinite;
          animation: drop-119 4.5865223668s 0.8935009461s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-119 {
  100% {
    top: 110%;
    left: 18%;
  }
}
@-o-keyframes drop-119 {
  100% {
    top: 110%;
    left: 18%;
  }
}
@keyframes drop-119 {
  100% {
    top: 110%;
    left: 18%;
  }
}
.confetti-wrapper .confetti-120 {
  width: 6px;
  height: 2.4px;
  background-color: #ffc107;
  top: -10%;
  left: 100%;
  opacity: 0.9883397513;
  -webkit-transform: rotate(72.4129274173deg);
      -ms-transform: rotate(72.4129274173deg);
       -o-transform: rotate(72.4129274173deg);
          transform: rotate(72.4129274173deg);
  -webkit-animation: drop-120 4.558710546s 0.326033942s infinite;
       -o-animation: drop-120 4.558710546s 0.326033942s infinite;
          animation: drop-120 4.558710546s 0.326033942s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-120 {
  100% {
    top: 110%;
    left: 107%;
  }
}
@-o-keyframes drop-120 {
  100% {
    top: 110%;
    left: 107%;
  }
}
@keyframes drop-120 {
  100% {
    top: 110%;
    left: 107%;
  }
}
.confetti-wrapper .confetti-121 {
  width: 3px;
  height: 1.2px;
  background-color: #cddc39;
  top: -10%;
  left: 59%;
  opacity: 0.9266171901;
  -webkit-transform: rotate(155.5424036271deg);
      -ms-transform: rotate(155.5424036271deg);
       -o-transform: rotate(155.5424036271deg);
          transform: rotate(155.5424036271deg);
  -webkit-animation: drop-121 4.6576002445s 0.7086293044s infinite;
       -o-animation: drop-121 4.6576002445s 0.7086293044s infinite;
          animation: drop-121 4.6576002445s 0.7086293044s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-121 {
  100% {
    top: 110%;
    left: 73%;
  }
}
@-o-keyframes drop-121 {
  100% {
    top: 110%;
    left: 73%;
  }
}
@keyframes drop-121 {
  100% {
    top: 110%;
    left: 73%;
  }
}
.confetti-wrapper .confetti-122 {
  width: 10px;
  height: 4px;
  background-color: #F44336;
  top: -10%;
  left: 58%;
  opacity: 0.6622887728;
  -webkit-transform: rotate(178.8261941094deg);
      -ms-transform: rotate(178.8261941094deg);
       -o-transform: rotate(178.8261941094deg);
          transform: rotate(178.8261941094deg);
  -webkit-animation: drop-122 4.8798939846s 0.1636577507s infinite;
       -o-animation: drop-122 4.8798939846s 0.1636577507s infinite;
          animation: drop-122 4.8798939846s 0.1636577507s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-122 {
  100% {
    top: 110%;
    left: 59%;
  }
}
@-o-keyframes drop-122 {
  100% {
    top: 110%;
    left: 59%;
  }
}
@keyframes drop-122 {
  100% {
    top: 110%;
    left: 59%;
  }
}
.confetti-wrapper .confetti-123 {
  width: 7px;
  height: 2.8px;
  background-color: #03a9f4;
  top: -10%;
  left: 44%;
  opacity: 0.9299299186;
  -webkit-transform: rotate(231.7550292545deg);
      -ms-transform: rotate(231.7550292545deg);
       -o-transform: rotate(231.7550292545deg);
          transform: rotate(231.7550292545deg);
  -webkit-animation: drop-123 4.2287444995s 0.188818655s infinite;
       -o-animation: drop-123 4.2287444995s 0.188818655s infinite;
          animation: drop-123 4.2287444995s 0.188818655s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-123 {
  100% {
    top: 110%;
    left: 58%;
  }
}
@-o-keyframes drop-123 {
  100% {
    top: 110%;
    left: 58%;
  }
}
@keyframes drop-123 {
  100% {
    top: 110%;
    left: 58%;
  }
}
.confetti-wrapper .confetti-124 {
  width: 7px;
  height: 2.8px;
  background-color: #ffeb3b;
  top: -10%;
  left: 25%;
  opacity: 0.6886855928;
  -webkit-transform: rotate(81.820498188deg);
      -ms-transform: rotate(81.820498188deg);
       -o-transform: rotate(81.820498188deg);
          transform: rotate(81.820498188deg);
  -webkit-animation: drop-124 4.5878645909s 0.5407300173s infinite;
       -o-animation: drop-124 4.5878645909s 0.5407300173s infinite;
          animation: drop-124 4.5878645909s 0.5407300173s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-124 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@-o-keyframes drop-124 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@keyframes drop-124 {
  100% {
    top: 110%;
    left: 27%;
  }
}
.confetti-wrapper .confetti-125 {
  width: 4px;
  height: 1.6px;
  background-color: #8bc34a;
  top: -10%;
  left: 3%;
  opacity: 0.7617664538;
  -webkit-transform: rotate(197.4210685479deg);
      -ms-transform: rotate(197.4210685479deg);
       -o-transform: rotate(197.4210685479deg);
          transform: rotate(197.4210685479deg);
  -webkit-animation: drop-125 4.3389349699s 0.6022939983s infinite;
       -o-animation: drop-125 4.3389349699s 0.6022939983s infinite;
          animation: drop-125 4.3389349699s 0.6022939983s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-125 {
  100% {
    top: 110%;
    left: 5%;
  }
}
@-o-keyframes drop-125 {
  100% {
    top: 110%;
    left: 5%;
  }
}
@keyframes drop-125 {
  100% {
    top: 110%;
    left: 5%;
  }
}
.confetti-wrapper .confetti-126 {
  width: 7px;
  height: 2.8px;
  background-color: #673ab7;
  top: -10%;
  left: -3%;
  opacity: 0.9133729054;
  -webkit-transform: rotate(105.0905532369deg);
      -ms-transform: rotate(105.0905532369deg);
       -o-transform: rotate(105.0905532369deg);
          transform: rotate(105.0905532369deg);
  -webkit-animation: drop-126 4.0678674216s 0.2900927149s infinite;
       -o-animation: drop-126 4.0678674216s 0.2900927149s infinite;
          animation: drop-126 4.0678674216s 0.2900927149s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-126 {
  100% {
    top: 110%;
    left: 12%;
  }
}
@-o-keyframes drop-126 {
  100% {
    top: 110%;
    left: 12%;
  }
}
@keyframes drop-126 {
  100% {
    top: 110%;
    left: 12%;
  }
}
.confetti-wrapper .confetti-127 {
  width: 8px;
  height: 3.2px;
  background-color: #00bcd4;
  top: -10%;
  left: 21%;
  opacity: 0.7563453729;
  -webkit-transform: rotate(61.1852686909deg);
      -ms-transform: rotate(61.1852686909deg);
       -o-transform: rotate(61.1852686909deg);
          transform: rotate(61.1852686909deg);
  -webkit-animation: drop-127 4.746716241s 0.7788502368s infinite;
       -o-animation: drop-127 4.746716241s 0.7788502368s infinite;
          animation: drop-127 4.746716241s 0.7788502368s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-127 {
  100% {
    top: 110%;
    left: 26%;
  }
}
@-o-keyframes drop-127 {
  100% {
    top: 110%;
    left: 26%;
  }
}
@keyframes drop-127 {
  100% {
    top: 110%;
    left: 26%;
  }
}
.confetti-wrapper .confetti-128 {
  width: 5px;
  height: 2px;
  background-color: #ffeb3b;
  top: -10%;
  left: 12%;
  opacity: 1.0118781017;
  -webkit-transform: rotate(236.0022756893deg);
      -ms-transform: rotate(236.0022756893deg);
       -o-transform: rotate(236.0022756893deg);
          transform: rotate(236.0022756893deg);
  -webkit-animation: drop-128 4.5756063124s 0.9822623122s infinite;
       -o-animation: drop-128 4.5756063124s 0.9822623122s infinite;
          animation: drop-128 4.5756063124s 0.9822623122s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-128 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@-o-keyframes drop-128 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@keyframes drop-128 {
  100% {
    top: 110%;
    left: 27%;
  }
}
.confetti-wrapper .confetti-129 {
  width: 9px;
  height: 3.6px;
  background-color: #009688;
  top: -10%;
  left: 22%;
  opacity: 1.4633554099;
  -webkit-transform: rotate(150.5146213289deg);
      -ms-transform: rotate(150.5146213289deg);
       -o-transform: rotate(150.5146213289deg);
          transform: rotate(150.5146213289deg);
  -webkit-animation: drop-129 4.2678336916s 0.1977847951s infinite;
       -o-animation: drop-129 4.2678336916s 0.1977847951s infinite;
          animation: drop-129 4.2678336916s 0.1977847951s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-129 {
  100% {
    top: 110%;
    left: 31%;
  }
}
@-o-keyframes drop-129 {
  100% {
    top: 110%;
    left: 31%;
  }
}
@keyframes drop-129 {
  100% {
    top: 110%;
    left: 31%;
  }
}
.confetti-wrapper .confetti-130 {
  width: 8px;
  height: 3.2px;
  background-color: #ff9800;
  top: -10%;
  left: -7%;
  opacity: 0.8588425658;
  -webkit-transform: rotate(200.1746832312deg);
      -ms-transform: rotate(200.1746832312deg);
       -o-transform: rotate(200.1746832312deg);
          transform: rotate(200.1746832312deg);
  -webkit-animation: drop-130 4.4140387459s 0.1221276489s infinite;
       -o-animation: drop-130 4.4140387459s 0.1221276489s infinite;
          animation: drop-130 4.4140387459s 0.1221276489s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-130 {
  100% {
    top: 110%;
    left: 0%;
  }
}
@-o-keyframes drop-130 {
  100% {
    top: 110%;
    left: 0%;
  }
}
@keyframes drop-130 {
  100% {
    top: 110%;
    left: 0%;
  }
}
.confetti-wrapper .confetti-131 {
  width: 2px;
  height: 0.8px;
  background-color: #00bcd4;
  top: -10%;
  left: 94%;
  opacity: 1.49196697;
  -webkit-transform: rotate(265.2094524842deg);
      -ms-transform: rotate(265.2094524842deg);
       -o-transform: rotate(265.2094524842deg);
          transform: rotate(265.2094524842deg);
  -webkit-animation: drop-131 4.5778480557s 0.9851412565s infinite;
       -o-animation: drop-131 4.5778480557s 0.9851412565s infinite;
          animation: drop-131 4.5778480557s 0.9851412565s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-131 {
  100% {
    top: 110%;
    left: 107%;
  }
}
@-o-keyframes drop-131 {
  100% {
    top: 110%;
    left: 107%;
  }
}
@keyframes drop-131 {
  100% {
    top: 110%;
    left: 107%;
  }
}
.confetti-wrapper .confetti-132 {
  width: 9px;
  height: 3.6px;
  background-color: #e91e63;
  top: -10%;
  left: 3%;
  opacity: 1.0297737811;
  -webkit-transform: rotate(334.1572143318deg);
      -ms-transform: rotate(334.1572143318deg);
       -o-transform: rotate(334.1572143318deg);
          transform: rotate(334.1572143318deg);
  -webkit-animation: drop-132 4.3133914742s 0.4777416922s infinite;
       -o-animation: drop-132 4.3133914742s 0.4777416922s infinite;
          animation: drop-132 4.3133914742s 0.4777416922s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-132 {
  100% {
    top: 110%;
    left: 7%;
  }
}
@-o-keyframes drop-132 {
  100% {
    top: 110%;
    left: 7%;
  }
}
@keyframes drop-132 {
  100% {
    top: 110%;
    left: 7%;
  }
}
.confetti-wrapper .confetti-133 {
  width: 7px;
  height: 2.8px;
  background-color: #4CAF50;
  top: -10%;
  left: 44%;
  opacity: 0.5954358337;
  -webkit-transform: rotate(272.369798886deg);
      -ms-transform: rotate(272.369798886deg);
       -o-transform: rotate(272.369798886deg);
          transform: rotate(272.369798886deg);
  -webkit-animation: drop-133 4.0492211382s 0.8932625249s infinite;
       -o-animation: drop-133 4.0492211382s 0.8932625249s infinite;
          animation: drop-133 4.0492211382s 0.8932625249s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-133 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@-o-keyframes drop-133 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@keyframes drop-133 {
  100% {
    top: 110%;
    left: 47%;
  }
}
.confetti-wrapper .confetti-134 {
  width: 10px;
  height: 4px;
  background-color: #ff9800;
  top: -10%;
  left: 30%;
  opacity: 1.3302181169;
  -webkit-transform: rotate(154.023786891deg);
      -ms-transform: rotate(154.023786891deg);
       -o-transform: rotate(154.023786891deg);
          transform: rotate(154.023786891deg);
  -webkit-animation: drop-134 4.9642013683s 0.8130041712s infinite;
       -o-animation: drop-134 4.9642013683s 0.8130041712s infinite;
          animation: drop-134 4.9642013683s 0.8130041712s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-134 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@-o-keyframes drop-134 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@keyframes drop-134 {
  100% {
    top: 110%;
    left: 35%;
  }
}
.confetti-wrapper .confetti-135 {
  width: 2px;
  height: 0.8px;
  background-color: #cddc39;
  top: -10%;
  left: 79%;
  opacity: 0.8229986004;
  -webkit-transform: rotate(199.1887996141deg);
      -ms-transform: rotate(199.1887996141deg);
       -o-transform: rotate(199.1887996141deg);
          transform: rotate(199.1887996141deg);
  -webkit-animation: drop-135 4.2852400048s 0.2884994212s infinite;
       -o-animation: drop-135 4.2852400048s 0.2884994212s infinite;
          animation: drop-135 4.2852400048s 0.2884994212s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-135 {
  100% {
    top: 110%;
    left: 81%;
  }
}
@-o-keyframes drop-135 {
  100% {
    top: 110%;
    left: 81%;
  }
}
@keyframes drop-135 {
  100% {
    top: 110%;
    left: 81%;
  }
}
.confetti-wrapper .confetti-136 {
  width: 4px;
  height: 1.6px;
  background-color: #ff9800;
  top: -10%;
  left: 6%;
  opacity: 1.3489151497;
  -webkit-transform: rotate(145.0202758287deg);
      -ms-transform: rotate(145.0202758287deg);
       -o-transform: rotate(145.0202758287deg);
          transform: rotate(145.0202758287deg);
  -webkit-animation: drop-136 4.4504382374s 0.8488870623s infinite;
       -o-animation: drop-136 4.4504382374s 0.8488870623s infinite;
          animation: drop-136 4.4504382374s 0.8488870623s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-136 {
  100% {
    top: 110%;
    left: 17%;
  }
}
@-o-keyframes drop-136 {
  100% {
    top: 110%;
    left: 17%;
  }
}
@keyframes drop-136 {
  100% {
    top: 110%;
    left: 17%;
  }
}
.confetti-wrapper .confetti-137 {
  width: 1px;
  height: 0.4px;
  background-color: #8bc34a;
  top: -10%;
  left: 92%;
  opacity: 0.5582718923;
  -webkit-transform: rotate(175.4941400337deg);
      -ms-transform: rotate(175.4941400337deg);
       -o-transform: rotate(175.4941400337deg);
          transform: rotate(175.4941400337deg);
  -webkit-animation: drop-137 4.1180874613s 0.538090577s infinite;
       -o-animation: drop-137 4.1180874613s 0.538090577s infinite;
          animation: drop-137 4.1180874613s 0.538090577s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-137 {
  100% {
    top: 110%;
    left: 105%;
  }
}
@-o-keyframes drop-137 {
  100% {
    top: 110%;
    left: 105%;
  }
}
@keyframes drop-137 {
  100% {
    top: 110%;
    left: 105%;
  }
}
.confetti-wrapper .confetti-138 {
  width: 3px;
  height: 1.2px;
  background-color: #e91e63;
  top: -10%;
  left: 80%;
  opacity: 0.7640602043;
  -webkit-transform: rotate(52.4484084342deg);
      -ms-transform: rotate(52.4484084342deg);
       -o-transform: rotate(52.4484084342deg);
          transform: rotate(52.4484084342deg);
  -webkit-animation: drop-138 4.2837964513s 0.7880191376s infinite;
       -o-animation: drop-138 4.2837964513s 0.7880191376s infinite;
          animation: drop-138 4.2837964513s 0.7880191376s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-138 {
  100% {
    top: 110%;
    left: 86%;
  }
}
@-o-keyframes drop-138 {
  100% {
    top: 110%;
    left: 86%;
  }
}
@keyframes drop-138 {
  100% {
    top: 110%;
    left: 86%;
  }
}
.confetti-wrapper .confetti-139 {
  width: 10px;
  height: 4px;
  background-color: #cddc39;
  top: -10%;
  left: 42%;
  opacity: 1.0934569165;
  -webkit-transform: rotate(287.0965681719deg);
      -ms-transform: rotate(287.0965681719deg);
       -o-transform: rotate(287.0965681719deg);
          transform: rotate(287.0965681719deg);
  -webkit-animation: drop-139 4.2790503635s 0.4748766361s infinite;
       -o-animation: drop-139 4.2790503635s 0.4748766361s infinite;
          animation: drop-139 4.2790503635s 0.4748766361s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-139 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@-o-keyframes drop-139 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@keyframes drop-139 {
  100% {
    top: 110%;
    left: 47%;
  }
}
.confetti-wrapper .confetti-140 {
  width: 1px;
  height: 0.4px;
  background-color: #ff5722;
  top: -10%;
  left: 87%;
  opacity: 0.8811424274;
  -webkit-transform: rotate(270.5896296606deg);
      -ms-transform: rotate(270.5896296606deg);
       -o-transform: rotate(270.5896296606deg);
          transform: rotate(270.5896296606deg);
  -webkit-animation: drop-140 4.9766715001s 0.9047462782s infinite;
       -o-animation: drop-140 4.9766715001s 0.9047462782s infinite;
          animation: drop-140 4.9766715001s 0.9047462782s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-140 {
  100% {
    top: 110%;
    left: 96%;
  }
}
@-o-keyframes drop-140 {
  100% {
    top: 110%;
    left: 96%;
  }
}
@keyframes drop-140 {
  100% {
    top: 110%;
    left: 96%;
  }
}
.confetti-wrapper .confetti-141 {
  width: 10px;
  height: 4px;
  background-color: #03a9f4;
  top: -10%;
  left: 84%;
  opacity: 1.3675593834;
  -webkit-transform: rotate(310.7659864038deg);
      -ms-transform: rotate(310.7659864038deg);
       -o-transform: rotate(310.7659864038deg);
          transform: rotate(310.7659864038deg);
  -webkit-animation: drop-141 4.3095511681s 0.5876367119s infinite;
       -o-animation: drop-141 4.3095511681s 0.5876367119s infinite;
          animation: drop-141 4.3095511681s 0.5876367119s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-141 {
  100% {
    top: 110%;
    left: 96%;
  }
}
@-o-keyframes drop-141 {
  100% {
    top: 110%;
    left: 96%;
  }
}
@keyframes drop-141 {
  100% {
    top: 110%;
    left: 96%;
  }
}
.confetti-wrapper .confetti-142 {
  width: 4px;
  height: 1.6px;
  background-color: #03a9f4;
  top: -10%;
  left: 96%;
  opacity: 0.5633207558;
  -webkit-transform: rotate(297.9302320643deg);
      -ms-transform: rotate(297.9302320643deg);
       -o-transform: rotate(297.9302320643deg);
          transform: rotate(297.9302320643deg);
  -webkit-animation: drop-142 4.6948025758s 0.5970687842s infinite;
       -o-animation: drop-142 4.6948025758s 0.5970687842s infinite;
          animation: drop-142 4.6948025758s 0.5970687842s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-142 {
  100% {
    top: 110%;
    left: 111%;
  }
}
@-o-keyframes drop-142 {
  100% {
    top: 110%;
    left: 111%;
  }
}
@keyframes drop-142 {
  100% {
    top: 110%;
    left: 111%;
  }
}
.confetti-wrapper .confetti-143 {
  width: 2px;
  height: 0.8px;
  background-color: #03a9f4;
  top: -10%;
  left: 67%;
  opacity: 0.8211905844;
  -webkit-transform: rotate(160.6093656895deg);
      -ms-transform: rotate(160.6093656895deg);
       -o-transform: rotate(160.6093656895deg);
          transform: rotate(160.6093656895deg);
  -webkit-animation: drop-143 4.089718129s 0.0165372346s infinite;
       -o-animation: drop-143 4.089718129s 0.0165372346s infinite;
          animation: drop-143 4.089718129s 0.0165372346s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-143 {
  100% {
    top: 110%;
    left: 77%;
  }
}
@-o-keyframes drop-143 {
  100% {
    top: 110%;
    left: 77%;
  }
}
@keyframes drop-143 {
  100% {
    top: 110%;
    left: 77%;
  }
}
.confetti-wrapper .confetti-144 {
  width: 4px;
  height: 1.6px;
  background-color: #4CAF50;
  top: -10%;
  left: 16%;
  opacity: 1.077009372;
  -webkit-transform: rotate(302.1093374286deg);
      -ms-transform: rotate(302.1093374286deg);
       -o-transform: rotate(302.1093374286deg);
          transform: rotate(302.1093374286deg);
  -webkit-animation: drop-144 4.766292513s 0.0228055823s infinite;
       -o-animation: drop-144 4.766292513s 0.0228055823s infinite;
          animation: drop-144 4.766292513s 0.0228055823s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-144 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@-o-keyframes drop-144 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@keyframes drop-144 {
  100% {
    top: 110%;
    left: 27%;
  }
}
.confetti-wrapper .confetti-145 {
  width: 1px;
  height: 0.4px;
  background-color: #ffeb3b;
  top: -10%;
  left: 79%;
  opacity: 1.3567243279;
  -webkit-transform: rotate(105.8138032126deg);
      -ms-transform: rotate(105.8138032126deg);
       -o-transform: rotate(105.8138032126deg);
          transform: rotate(105.8138032126deg);
  -webkit-animation: drop-145 4.4478888376s 0.0430393454s infinite;
       -o-animation: drop-145 4.4478888376s 0.0430393454s infinite;
          animation: drop-145 4.4478888376s 0.0430393454s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-145 {
  100% {
    top: 110%;
    left: 88%;
  }
}
@-o-keyframes drop-145 {
  100% {
    top: 110%;
    left: 88%;
  }
}
@keyframes drop-145 {
  100% {
    top: 110%;
    left: 88%;
  }
}
.confetti-wrapper .confetti-146 {
  width: 6px;
  height: 2.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 62%;
  opacity: 0.9437653232;
  -webkit-transform: rotate(191.2960779945deg);
      -ms-transform: rotate(191.2960779945deg);
       -o-transform: rotate(191.2960779945deg);
          transform: rotate(191.2960779945deg);
  -webkit-animation: drop-146 4.8362677319s 0.1231533594s infinite;
       -o-animation: drop-146 4.8362677319s 0.1231533594s infinite;
          animation: drop-146 4.8362677319s 0.1231533594s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-146 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@-o-keyframes drop-146 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@keyframes drop-146 {
  100% {
    top: 110%;
    left: 76%;
  }
}
.confetti-wrapper .confetti-147 {
  width: 2px;
  height: 0.8px;
  background-color: #3f51b5;
  top: -10%;
  left: -6%;
  opacity: 0.6758351601;
  -webkit-transform: rotate(87.7587616945deg);
      -ms-transform: rotate(87.7587616945deg);
       -o-transform: rotate(87.7587616945deg);
          transform: rotate(87.7587616945deg);
  -webkit-animation: drop-147 4.4239755273s 0.3591045183s infinite;
       -o-animation: drop-147 4.4239755273s 0.3591045183s infinite;
          animation: drop-147 4.4239755273s 0.3591045183s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-147 {
  100% {
    top: 110%;
    left: 5%;
  }
}
@-o-keyframes drop-147 {
  100% {
    top: 110%;
    left: 5%;
  }
}
@keyframes drop-147 {
  100% {
    top: 110%;
    left: 5%;
  }
}
.confetti-wrapper .confetti-148 {
  width: 3px;
  height: 1.2px;
  background-color: #8bc34a;
  top: -10%;
  left: 15%;
  opacity: 1.2736832503;
  -webkit-transform: rotate(266.1483844013deg);
      -ms-transform: rotate(266.1483844013deg);
       -o-transform: rotate(266.1483844013deg);
          transform: rotate(266.1483844013deg);
  -webkit-animation: drop-148 4.6280290911s 0.8843181133s infinite;
       -o-animation: drop-148 4.6280290911s 0.8843181133s infinite;
          animation: drop-148 4.6280290911s 0.8843181133s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-148 {
  100% {
    top: 110%;
    left: 29%;
  }
}
@-o-keyframes drop-148 {
  100% {
    top: 110%;
    left: 29%;
  }
}
@keyframes drop-148 {
  100% {
    top: 110%;
    left: 29%;
  }
}
.confetti-wrapper .confetti-149 {
  width: 8px;
  height: 3.2px;
  background-color: #4CAF50;
  top: -10%;
  left: 8%;
  opacity: 0.5108808645;
  -webkit-transform: rotate(163.8728813748deg);
      -ms-transform: rotate(163.8728813748deg);
       -o-transform: rotate(163.8728813748deg);
          transform: rotate(163.8728813748deg);
  -webkit-animation: drop-149 4.1794590185s 0.4739588649s infinite;
       -o-animation: drop-149 4.1794590185s 0.4739588649s infinite;
          animation: drop-149 4.1794590185s 0.4739588649s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-149 {
  100% {
    top: 110%;
    left: 11%;
  }
}
@-o-keyframes drop-149 {
  100% {
    top: 110%;
    left: 11%;
  }
}
@keyframes drop-149 {
  100% {
    top: 110%;
    left: 11%;
  }
}
.confetti-wrapper .confetti-150 {
  width: 5px;
  height: 2px;
  background-color: #ff5722;
  top: -10%;
  left: 45%;
  opacity: 0.5842351378;
  -webkit-transform: rotate(290.1394909521deg);
      -ms-transform: rotate(290.1394909521deg);
       -o-transform: rotate(290.1394909521deg);
          transform: rotate(290.1394909521deg);
  -webkit-animation: drop-150 4.1955326692s 0.4936243901s infinite;
       -o-animation: drop-150 4.1955326692s 0.4936243901s infinite;
          animation: drop-150 4.1955326692s 0.4936243901s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-150 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@-o-keyframes drop-150 {
  100% {
    top: 110%;
    left: 53%;
  }
}
@keyframes drop-150 {
  100% {
    top: 110%;
    left: 53%;
  }
}
.confetti-wrapper .confetti-151 {
  width: 3px;
  height: 1.2px;
  background-color: #673ab7;
  top: -10%;
  left: 26%;
  opacity: 0.6197749192;
  -webkit-transform: rotate(156.1021314427deg);
      -ms-transform: rotate(156.1021314427deg);
       -o-transform: rotate(156.1021314427deg);
          transform: rotate(156.1021314427deg);
  -webkit-animation: drop-151 4.7337060918s 0.4940875371s infinite;
       -o-animation: drop-151 4.7337060918s 0.4940875371s infinite;
          animation: drop-151 4.7337060918s 0.4940875371s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-151 {
  100% {
    top: 110%;
    left: 40%;
  }
}
@-o-keyframes drop-151 {
  100% {
    top: 110%;
    left: 40%;
  }
}
@keyframes drop-151 {
  100% {
    top: 110%;
    left: 40%;
  }
}
.confetti-wrapper .confetti-152 {
  width: 6px;
  height: 2.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 9%;
  opacity: 0.8668539734;
  -webkit-transform: rotate(256.8462592629deg);
      -ms-transform: rotate(256.8462592629deg);
       -o-transform: rotate(256.8462592629deg);
          transform: rotate(256.8462592629deg);
  -webkit-animation: drop-152 4.9818323926s 0.923326806s infinite;
       -o-animation: drop-152 4.9818323926s 0.923326806s infinite;
          animation: drop-152 4.9818323926s 0.923326806s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-152 {
  100% {
    top: 110%;
    left: 16%;
  }
}
@-o-keyframes drop-152 {
  100% {
    top: 110%;
    left: 16%;
  }
}
@keyframes drop-152 {
  100% {
    top: 110%;
    left: 16%;
  }
}
.confetti-wrapper .confetti-153 {
  width: 5px;
  height: 2px;
  background-color: #673ab7;
  top: -10%;
  left: 77%;
  opacity: 0.6023076621;
  -webkit-transform: rotate(186.0710905688deg);
      -ms-transform: rotate(186.0710905688deg);
       -o-transform: rotate(186.0710905688deg);
          transform: rotate(186.0710905688deg);
  -webkit-animation: drop-153 4.4462010769s 0.4248388534s infinite;
       -o-animation: drop-153 4.4462010769s 0.4248388534s infinite;
          animation: drop-153 4.4462010769s 0.4248388534s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-153 {
  100% {
    top: 110%;
    left: 78%;
  }
}
@-o-keyframes drop-153 {
  100% {
    top: 110%;
    left: 78%;
  }
}
@keyframes drop-153 {
  100% {
    top: 110%;
    left: 78%;
  }
}
.confetti-wrapper .confetti-154 {
  width: 1px;
  height: 0.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 58%;
  opacity: 0.791464103;
  -webkit-transform: rotate(218.7455434974deg);
      -ms-transform: rotate(218.7455434974deg);
       -o-transform: rotate(218.7455434974deg);
          transform: rotate(218.7455434974deg);
  -webkit-animation: drop-154 4.763013729s 0.5592551045s infinite;
       -o-animation: drop-154 4.763013729s 0.5592551045s infinite;
          animation: drop-154 4.763013729s 0.5592551045s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-154 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@-o-keyframes drop-154 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@keyframes drop-154 {
  100% {
    top: 110%;
    left: 68%;
  }
}
.confetti-wrapper .confetti-155 {
  width: 3px;
  height: 1.2px;
  background-color: #ffeb3b;
  top: -10%;
  left: 82%;
  opacity: 1.4676353692;
  -webkit-transform: rotate(80.3773524386deg);
      -ms-transform: rotate(80.3773524386deg);
       -o-transform: rotate(80.3773524386deg);
          transform: rotate(80.3773524386deg);
  -webkit-animation: drop-155 4.1814708565s 0.3723866083s infinite;
       -o-animation: drop-155 4.1814708565s 0.3723866083s infinite;
          animation: drop-155 4.1814708565s 0.3723866083s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-155 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@-o-keyframes drop-155 {
  100% {
    top: 110%;
    left: 83%;
  }
}
@keyframes drop-155 {
  100% {
    top: 110%;
    left: 83%;
  }
}
.confetti-wrapper .confetti-156 {
  width: 3px;
  height: 1.2px;
  background-color: #673ab7;
  top: -10%;
  left: 96%;
  opacity: 0.8631764915;
  -webkit-transform: rotate(288.1679381679deg);
      -ms-transform: rotate(288.1679381679deg);
       -o-transform: rotate(288.1679381679deg);
          transform: rotate(288.1679381679deg);
  -webkit-animation: drop-156 4.7174004501s 0.2154819129s infinite;
       -o-animation: drop-156 4.7174004501s 0.2154819129s infinite;
          animation: drop-156 4.7174004501s 0.2154819129s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-156 {
  100% {
    top: 110%;
    left: 110%;
  }
}
@-o-keyframes drop-156 {
  100% {
    top: 110%;
    left: 110%;
  }
}
@keyframes drop-156 {
  100% {
    top: 110%;
    left: 110%;
  }
}
.confetti-wrapper .confetti-157 {
  width: 6px;
  height: 2.4px;
  background-color: #ff9800;
  top: -10%;
  left: 55%;
  opacity: 1.1121197522;
  -webkit-transform: rotate(246.6404000613deg);
      -ms-transform: rotate(246.6404000613deg);
       -o-transform: rotate(246.6404000613deg);
          transform: rotate(246.6404000613deg);
  -webkit-animation: drop-157 4.651083539s 0.6057939021s infinite;
       -o-animation: drop-157 4.651083539s 0.6057939021s infinite;
          animation: drop-157 4.651083539s 0.6057939021s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-157 {
  100% {
    top: 110%;
    left: 66%;
  }
}
@-o-keyframes drop-157 {
  100% {
    top: 110%;
    left: 66%;
  }
}
@keyframes drop-157 {
  100% {
    top: 110%;
    left: 66%;
  }
}
.confetti-wrapper .confetti-158 {
  width: 3px;
  height: 1.2px;
  background-color: #673ab7;
  top: -10%;
  left: 55%;
  opacity: 1.0114684934;
  -webkit-transform: rotate(301.6613708196deg);
      -ms-transform: rotate(301.6613708196deg);
       -o-transform: rotate(301.6613708196deg);
          transform: rotate(301.6613708196deg);
  -webkit-animation: drop-158 4.251015911s 0.6961957388s infinite;
       -o-animation: drop-158 4.251015911s 0.6961957388s infinite;
          animation: drop-158 4.251015911s 0.6961957388s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-158 {
  100% {
    top: 110%;
    left: 61%;
  }
}
@-o-keyframes drop-158 {
  100% {
    top: 110%;
    left: 61%;
  }
}
@keyframes drop-158 {
  100% {
    top: 110%;
    left: 61%;
  }
}
.confetti-wrapper .confetti-159 {
  width: 3px;
  height: 1.2px;
  background-color: #009688;
  top: -10%;
  left: 72%;
  opacity: 1.138727775;
  -webkit-transform: rotate(153.8567317679deg);
      -ms-transform: rotate(153.8567317679deg);
       -o-transform: rotate(153.8567317679deg);
          transform: rotate(153.8567317679deg);
  -webkit-animation: drop-159 4.2043397402s 0.6956125401s infinite;
       -o-animation: drop-159 4.2043397402s 0.6956125401s infinite;
          animation: drop-159 4.2043397402s 0.6956125401s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-159 {
  100% {
    top: 110%;
    left: 77%;
  }
}
@-o-keyframes drop-159 {
  100% {
    top: 110%;
    left: 77%;
  }
}
@keyframes drop-159 {
  100% {
    top: 110%;
    left: 77%;
  }
}
.confetti-wrapper .confetti-160 {
  width: 4px;
  height: 1.6px;
  background-color: #ffc107;
  top: -10%;
  left: 75%;
  opacity: 1.0280624689;
  -webkit-transform: rotate(306.5236841348deg);
      -ms-transform: rotate(306.5236841348deg);
       -o-transform: rotate(306.5236841348deg);
          transform: rotate(306.5236841348deg);
  -webkit-animation: drop-160 4.4164755588s 0.5576836137s infinite;
       -o-animation: drop-160 4.4164755588s 0.5576836137s infinite;
          animation: drop-160 4.4164755588s 0.5576836137s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-160 {
  100% {
    top: 110%;
    left: 78%;
  }
}
@-o-keyframes drop-160 {
  100% {
    top: 110%;
    left: 78%;
  }
}
@keyframes drop-160 {
  100% {
    top: 110%;
    left: 78%;
  }
}
.confetti-wrapper .confetti-161 {
  width: 5px;
  height: 2px;
  background-color: #ff9800;
  top: -10%;
  left: 23%;
  opacity: 0.509833165;
  -webkit-transform: rotate(302.9499380454deg);
      -ms-transform: rotate(302.9499380454deg);
       -o-transform: rotate(302.9499380454deg);
          transform: rotate(302.9499380454deg);
  -webkit-animation: drop-161 4.102435924s 0.822946921s infinite;
       -o-animation: drop-161 4.102435924s 0.822946921s infinite;
          animation: drop-161 4.102435924s 0.822946921s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-161 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@-o-keyframes drop-161 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@keyframes drop-161 {
  100% {
    top: 110%;
    left: 27%;
  }
}
.confetti-wrapper .confetti-162 {
  width: 2px;
  height: 0.8px;
  background-color: #9c27b0;
  top: -10%;
  left: 29%;
  opacity: 1.1710481568;
  -webkit-transform: rotate(149.3397712247deg);
      -ms-transform: rotate(149.3397712247deg);
       -o-transform: rotate(149.3397712247deg);
          transform: rotate(149.3397712247deg);
  -webkit-animation: drop-162 4.4601723795s 0.2671484182s infinite;
       -o-animation: drop-162 4.4601723795s 0.2671484182s infinite;
          animation: drop-162 4.4601723795s 0.2671484182s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-162 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@-o-keyframes drop-162 {
  100% {
    top: 110%;
    left: 41%;
  }
}
@keyframes drop-162 {
  100% {
    top: 110%;
    left: 41%;
  }
}
.confetti-wrapper .confetti-163 {
  width: 4px;
  height: 1.6px;
  background-color: #673ab7;
  top: -10%;
  left: 78%;
  opacity: 1.0038902752;
  -webkit-transform: rotate(148.9849879738deg);
      -ms-transform: rotate(148.9849879738deg);
       -o-transform: rotate(148.9849879738deg);
          transform: rotate(148.9849879738deg);
  -webkit-animation: drop-163 4.6574349707s 0.7598996177s infinite;
       -o-animation: drop-163 4.6574349707s 0.7598996177s infinite;
          animation: drop-163 4.6574349707s 0.7598996177s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-163 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@-o-keyframes drop-163 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@keyframes drop-163 {
  100% {
    top: 110%;
    left: 80%;
  }
}
.confetti-wrapper .confetti-164 {
  width: 10px;
  height: 4px;
  background-color: #F44336;
  top: -10%;
  left: 39%;
  opacity: 0.5803892792;
  -webkit-transform: rotate(297.8287450393deg);
      -ms-transform: rotate(297.8287450393deg);
       -o-transform: rotate(297.8287450393deg);
          transform: rotate(297.8287450393deg);
  -webkit-animation: drop-164 4.8964710395s 0.5148383032s infinite;
       -o-animation: drop-164 4.8964710395s 0.5148383032s infinite;
          animation: drop-164 4.8964710395s 0.5148383032s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-164 {
  100% {
    top: 110%;
    left: 44%;
  }
}
@-o-keyframes drop-164 {
  100% {
    top: 110%;
    left: 44%;
  }
}
@keyframes drop-164 {
  100% {
    top: 110%;
    left: 44%;
  }
}
.confetti-wrapper .confetti-165 {
  width: 6px;
  height: 2.4px;
  background-color: #ff9800;
  top: -10%;
  left: 8%;
  opacity: 1.1187007402;
  -webkit-transform: rotate(138.0100445643deg);
      -ms-transform: rotate(138.0100445643deg);
       -o-transform: rotate(138.0100445643deg);
          transform: rotate(138.0100445643deg);
  -webkit-animation: drop-165 4.8744260239s 0.5240170042s infinite;
       -o-animation: drop-165 4.8744260239s 0.5240170042s infinite;
          animation: drop-165 4.8744260239s 0.5240170042s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-165 {
  100% {
    top: 110%;
    left: 22%;
  }
}
@-o-keyframes drop-165 {
  100% {
    top: 110%;
    left: 22%;
  }
}
@keyframes drop-165 {
  100% {
    top: 110%;
    left: 22%;
  }
}
.confetti-wrapper .confetti-166 {
  width: 1px;
  height: 0.4px;
  background-color: #009688;
  top: -10%;
  left: 76%;
  opacity: 0.9100136626;
  -webkit-transform: rotate(256.1780982201deg);
      -ms-transform: rotate(256.1780982201deg);
       -o-transform: rotate(256.1780982201deg);
          transform: rotate(256.1780982201deg);
  -webkit-animation: drop-166 4.3854695244s 0.4965967739s infinite;
       -o-animation: drop-166 4.3854695244s 0.4965967739s infinite;
          animation: drop-166 4.3854695244s 0.4965967739s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-166 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@-o-keyframes drop-166 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@keyframes drop-166 {
  100% {
    top: 110%;
    left: 80%;
  }
}
.confetti-wrapper .confetti-167 {
  width: 5px;
  height: 2px;
  background-color: #3f51b5;
  top: -10%;
  left: 1%;
  opacity: 1.4765149626;
  -webkit-transform: rotate(230.6292511655deg);
      -ms-transform: rotate(230.6292511655deg);
       -o-transform: rotate(230.6292511655deg);
          transform: rotate(230.6292511655deg);
  -webkit-animation: drop-167 4.8696191923s 0.3453433676s infinite;
       -o-animation: drop-167 4.8696191923s 0.3453433676s infinite;
          animation: drop-167 4.8696191923s 0.3453433676s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-167 {
  100% {
    top: 110%;
    left: 6%;
  }
}
@-o-keyframes drop-167 {
  100% {
    top: 110%;
    left: 6%;
  }
}
@keyframes drop-167 {
  100% {
    top: 110%;
    left: 6%;
  }
}
.confetti-wrapper .confetti-168 {
  width: 9px;
  height: 3.6px;
  background-color: #009688;
  top: -10%;
  left: 18%;
  opacity: 1.4616288459;
  -webkit-transform: rotate(331.7915553847deg);
      -ms-transform: rotate(331.7915553847deg);
       -o-transform: rotate(331.7915553847deg);
          transform: rotate(331.7915553847deg);
  -webkit-animation: drop-168 4.8516313768s 0.4215095463s infinite;
       -o-animation: drop-168 4.8516313768s 0.4215095463s infinite;
          animation: drop-168 4.8516313768s 0.4215095463s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-168 {
  100% {
    top: 110%;
    left: 23%;
  }
}
@-o-keyframes drop-168 {
  100% {
    top: 110%;
    left: 23%;
  }
}
@keyframes drop-168 {
  100% {
    top: 110%;
    left: 23%;
  }
}
.confetti-wrapper .confetti-169 {
  width: 4px;
  height: 1.6px;
  background-color: #03a9f4;
  top: -10%;
  left: 71%;
  opacity: 0.5027206774;
  -webkit-transform: rotate(233.9899917977deg);
      -ms-transform: rotate(233.9899917977deg);
       -o-transform: rotate(233.9899917977deg);
          transform: rotate(233.9899917977deg);
  -webkit-animation: drop-169 4.8602678156s 0.940075343s infinite;
       -o-animation: drop-169 4.8602678156s 0.940075343s infinite;
          animation: drop-169 4.8602678156s 0.940075343s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-169 {
  100% {
    top: 110%;
    left: 74%;
  }
}
@-o-keyframes drop-169 {
  100% {
    top: 110%;
    left: 74%;
  }
}
@keyframes drop-169 {
  100% {
    top: 110%;
    left: 74%;
  }
}
.confetti-wrapper .confetti-170 {
  width: 7px;
  height: 2.8px;
  background-color: #8bc34a;
  top: -10%;
  left: 64%;
  opacity: 0.5096123438;
  -webkit-transform: rotate(78.1584811992deg);
      -ms-transform: rotate(78.1584811992deg);
       -o-transform: rotate(78.1584811992deg);
          transform: rotate(78.1584811992deg);
  -webkit-animation: drop-170 4.9890125733s 0.5053048487s infinite;
       -o-animation: drop-170 4.9890125733s 0.5053048487s infinite;
          animation: drop-170 4.9890125733s 0.5053048487s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-170 {
  100% {
    top: 110%;
    left: 70%;
  }
}
@-o-keyframes drop-170 {
  100% {
    top: 110%;
    left: 70%;
  }
}
@keyframes drop-170 {
  100% {
    top: 110%;
    left: 70%;
  }
}
.confetti-wrapper .confetti-171 {
  width: 6px;
  height: 2.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 35%;
  opacity: 1.3062255846;
  -webkit-transform: rotate(59.1327384997deg);
      -ms-transform: rotate(59.1327384997deg);
       -o-transform: rotate(59.1327384997deg);
          transform: rotate(59.1327384997deg);
  -webkit-animation: drop-171 4.7281319341s 0.6854557093s infinite;
       -o-animation: drop-171 4.7281319341s 0.6854557093s infinite;
          animation: drop-171 4.7281319341s 0.6854557093s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-171 {
  100% {
    top: 110%;
    left: 50%;
  }
}
@-o-keyframes drop-171 {
  100% {
    top: 110%;
    left: 50%;
  }
}
@keyframes drop-171 {
  100% {
    top: 110%;
    left: 50%;
  }
}
.confetti-wrapper .confetti-172 {
  width: 7px;
  height: 2.8px;
  background-color: #4CAF50;
  top: -10%;
  left: 51%;
  opacity: 1.2275205261;
  -webkit-transform: rotate(165.6926829447deg);
      -ms-transform: rotate(165.6926829447deg);
       -o-transform: rotate(165.6926829447deg);
          transform: rotate(165.6926829447deg);
  -webkit-animation: drop-172 4.3161974694s 0.4926249214s infinite;
       -o-animation: drop-172 4.3161974694s 0.4926249214s infinite;
          animation: drop-172 4.3161974694s 0.4926249214s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-172 {
  100% {
    top: 110%;
    left: 60%;
  }
}
@-o-keyframes drop-172 {
  100% {
    top: 110%;
    left: 60%;
  }
}
@keyframes drop-172 {
  100% {
    top: 110%;
    left: 60%;
  }
}
.confetti-wrapper .confetti-173 {
  width: 6px;
  height: 2.4px;
  background-color: #ff5722;
  top: -10%;
  left: 31%;
  opacity: 0.8002430721;
  -webkit-transform: rotate(318.6784220513deg);
      -ms-transform: rotate(318.6784220513deg);
       -o-transform: rotate(318.6784220513deg);
          transform: rotate(318.6784220513deg);
  -webkit-animation: drop-173 4.4856423179s 0.6333855674s infinite;
       -o-animation: drop-173 4.4856423179s 0.6333855674s infinite;
          animation: drop-173 4.4856423179s 0.6333855674s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-173 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@-o-keyframes drop-173 {
  100% {
    top: 110%;
    left: 35%;
  }
}
@keyframes drop-173 {
  100% {
    top: 110%;
    left: 35%;
  }
}
.confetti-wrapper .confetti-174 {
  width: 8px;
  height: 3.2px;
  background-color: #9c27b0;
  top: -10%;
  left: 91%;
  opacity: 1.4042972883;
  -webkit-transform: rotate(326.0369315219deg);
      -ms-transform: rotate(326.0369315219deg);
       -o-transform: rotate(326.0369315219deg);
          transform: rotate(326.0369315219deg);
  -webkit-animation: drop-174 4.3477364003s 0.7446345093s infinite;
       -o-animation: drop-174 4.3477364003s 0.7446345093s infinite;
          animation: drop-174 4.3477364003s 0.7446345093s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-174 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@-o-keyframes drop-174 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@keyframes drop-174 {
  100% {
    top: 110%;
    left: 100%;
  }
}
.confetti-wrapper .confetti-175 {
  width: 1px;
  height: 0.4px;
  background-color: #03a9f4;
  top: -10%;
  left: 74%;
  opacity: 1.3071725147;
  -webkit-transform: rotate(193.3077951296deg);
      -ms-transform: rotate(193.3077951296deg);
       -o-transform: rotate(193.3077951296deg);
          transform: rotate(193.3077951296deg);
  -webkit-animation: drop-175 4.5252034374s 0.5928256031s infinite;
       -o-animation: drop-175 4.5252034374s 0.5928256031s infinite;
          animation: drop-175 4.5252034374s 0.5928256031s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-175 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@-o-keyframes drop-175 {
  100% {
    top: 110%;
    left: 76%;
  }
}
@keyframes drop-175 {
  100% {
    top: 110%;
    left: 76%;
  }
}
.confetti-wrapper .confetti-176 {
  width: 3px;
  height: 1.2px;
  background-color: #8bc34a;
  top: -10%;
  left: 19%;
  opacity: 0.510270657;
  -webkit-transform: rotate(116.9882988937deg);
      -ms-transform: rotate(116.9882988937deg);
       -o-transform: rotate(116.9882988937deg);
          transform: rotate(116.9882988937deg);
  -webkit-animation: drop-176 4.5180344382s 0.5184958368s infinite;
       -o-animation: drop-176 4.5180344382s 0.5184958368s infinite;
          animation: drop-176 4.5180344382s 0.5184958368s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-176 {
  100% {
    top: 110%;
    left: 22%;
  }
}
@-o-keyframes drop-176 {
  100% {
    top: 110%;
    left: 22%;
  }
}
@keyframes drop-176 {
  100% {
    top: 110%;
    left: 22%;
  }
}
.confetti-wrapper .confetti-177 {
  width: 3px;
  height: 1.2px;
  background-color: #8bc34a;
  top: -10%;
  left: 84%;
  opacity: 0.6719179538;
  -webkit-transform: rotate(245.9241978525deg);
      -ms-transform: rotate(245.9241978525deg);
       -o-transform: rotate(245.9241978525deg);
          transform: rotate(245.9241978525deg);
  -webkit-animation: drop-177 4.0436951564s 0.3719999767s infinite;
       -o-animation: drop-177 4.0436951564s 0.3719999767s infinite;
          animation: drop-177 4.0436951564s 0.3719999767s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-177 {
  100% {
    top: 110%;
    left: 86%;
  }
}
@-o-keyframes drop-177 {
  100% {
    top: 110%;
    left: 86%;
  }
}
@keyframes drop-177 {
  100% {
    top: 110%;
    left: 86%;
  }
}
.confetti-wrapper .confetti-178 {
  width: 5px;
  height: 2px;
  background-color: #ff5722;
  top: -10%;
  left: -4%;
  opacity: 1.1606024236;
  -webkit-transform: rotate(265.6128456157deg);
      -ms-transform: rotate(265.6128456157deg);
       -o-transform: rotate(265.6128456157deg);
          transform: rotate(265.6128456157deg);
  -webkit-animation: drop-178 4.6074495979s 0.887169691s infinite;
       -o-animation: drop-178 4.6074495979s 0.887169691s infinite;
          animation: drop-178 4.6074495979s 0.887169691s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-178 {
  100% {
    top: 110%;
    left: -1%;
  }
}
@-o-keyframes drop-178 {
  100% {
    top: 110%;
    left: -1%;
  }
}
@keyframes drop-178 {
  100% {
    top: 110%;
    left: -1%;
  }
}
.confetti-wrapper .confetti-179 {
  width: 2px;
  height: 0.8px;
  background-color: #9c27b0;
  top: -10%;
  left: 88%;
  opacity: 1.0664242799;
  -webkit-transform: rotate(42.8243956387deg);
      -ms-transform: rotate(42.8243956387deg);
       -o-transform: rotate(42.8243956387deg);
          transform: rotate(42.8243956387deg);
  -webkit-animation: drop-179 4.4955993824s 0.6792337658s infinite;
       -o-animation: drop-179 4.4955993824s 0.6792337658s infinite;
          animation: drop-179 4.4955993824s 0.6792337658s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-179 {
  100% {
    top: 110%;
    left: 96%;
  }
}
@-o-keyframes drop-179 {
  100% {
    top: 110%;
    left: 96%;
  }
}
@keyframes drop-179 {
  100% {
    top: 110%;
    left: 96%;
  }
}
.confetti-wrapper .confetti-180 {
  width: 5px;
  height: 2px;
  background-color: #ff5722;
  top: -10%;
  left: 21%;
  opacity: 1.1457232006;
  -webkit-transform: rotate(174.4922942141deg);
      -ms-transform: rotate(174.4922942141deg);
       -o-transform: rotate(174.4922942141deg);
          transform: rotate(174.4922942141deg);
  -webkit-animation: drop-180 4.8089252814s 0.8334508419s infinite;
       -o-animation: drop-180 4.8089252814s 0.8334508419s infinite;
          animation: drop-180 4.8089252814s 0.8334508419s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-180 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@-o-keyframes drop-180 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@keyframes drop-180 {
  100% {
    top: 110%;
    left: 27%;
  }
}
.confetti-wrapper .confetti-181 {
  width: 10px;
  height: 4px;
  background-color: #00bcd4;
  top: -10%;
  left: 79%;
  opacity: 0.7894465449;
  -webkit-transform: rotate(210.0802108205deg);
      -ms-transform: rotate(210.0802108205deg);
       -o-transform: rotate(210.0802108205deg);
          transform: rotate(210.0802108205deg);
  -webkit-animation: drop-181 4.5485047489s 0.2017828425s infinite;
       -o-animation: drop-181 4.5485047489s 0.2017828425s infinite;
          animation: drop-181 4.5485047489s 0.2017828425s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-181 {
  100% {
    top: 110%;
    left: 85%;
  }
}
@-o-keyframes drop-181 {
  100% {
    top: 110%;
    left: 85%;
  }
}
@keyframes drop-181 {
  100% {
    top: 110%;
    left: 85%;
  }
}
.confetti-wrapper .confetti-182 {
  width: 6px;
  height: 2.4px;
  background-color: #ff5722;
  top: -10%;
  left: 43%;
  opacity: 0.9532819437;
  -webkit-transform: rotate(41.3212790855deg);
      -ms-transform: rotate(41.3212790855deg);
       -o-transform: rotate(41.3212790855deg);
          transform: rotate(41.3212790855deg);
  -webkit-animation: drop-182 4.4265139123s 0.4091986532s infinite;
       -o-animation: drop-182 4.4265139123s 0.4091986532s infinite;
          animation: drop-182 4.4265139123s 0.4091986532s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-182 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@-o-keyframes drop-182 {
  100% {
    top: 110%;
    left: 47%;
  }
}
@keyframes drop-182 {
  100% {
    top: 110%;
    left: 47%;
  }
}
.confetti-wrapper .confetti-183 {
  width: 4px;
  height: 1.6px;
  background-color: #ff5722;
  top: -10%;
  left: -4%;
  opacity: 1.3747876754;
  -webkit-transform: rotate(192.7715912867deg);
      -ms-transform: rotate(192.7715912867deg);
       -o-transform: rotate(192.7715912867deg);
          transform: rotate(192.7715912867deg);
  -webkit-animation: drop-183 4.7191330115s 0.2292853649s infinite;
       -o-animation: drop-183 4.7191330115s 0.2292853649s infinite;
          animation: drop-183 4.7191330115s 0.2292853649s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-183 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@-o-keyframes drop-183 {
  100% {
    top: 110%;
    left: 9%;
  }
}
@keyframes drop-183 {
  100% {
    top: 110%;
    left: 9%;
  }
}
.confetti-wrapper .confetti-184 {
  width: 1px;
  height: 0.4px;
  background-color: #9c27b0;
  top: -10%;
  left: 4%;
  opacity: 0.9356544934;
  -webkit-transform: rotate(352.830293499deg);
      -ms-transform: rotate(352.830293499deg);
       -o-transform: rotate(352.830293499deg);
          transform: rotate(352.830293499deg);
  -webkit-animation: drop-184 4.9039372737s 0.4991305332s infinite;
       -o-animation: drop-184 4.9039372737s 0.4991305332s infinite;
          animation: drop-184 4.9039372737s 0.4991305332s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-184 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@-o-keyframes drop-184 {
  100% {
    top: 110%;
    left: 10%;
  }
}
@keyframes drop-184 {
  100% {
    top: 110%;
    left: 10%;
  }
}
.confetti-wrapper .confetti-185 {
  width: 1px;
  height: 0.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 74%;
  opacity: 0.7807359079;
  -webkit-transform: rotate(235.3678616924deg);
      -ms-transform: rotate(235.3678616924deg);
       -o-transform: rotate(235.3678616924deg);
          transform: rotate(235.3678616924deg);
  -webkit-animation: drop-185 4.7251282153s 0.6673572902s infinite;
       -o-animation: drop-185 4.7251282153s 0.6673572902s infinite;
          animation: drop-185 4.7251282153s 0.6673572902s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-185 {
  100% {
    top: 110%;
    left: 89%;
  }
}
@-o-keyframes drop-185 {
  100% {
    top: 110%;
    left: 89%;
  }
}
@keyframes drop-185 {
  100% {
    top: 110%;
    left: 89%;
  }
}
.confetti-wrapper .confetti-186 {
  width: 3px;
  height: 1.2px;
  background-color: #8bc34a;
  top: -10%;
  left: 88%;
  opacity: 1.0959266013;
  -webkit-transform: rotate(270.7332077472deg);
      -ms-transform: rotate(270.7332077472deg);
       -o-transform: rotate(270.7332077472deg);
          transform: rotate(270.7332077472deg);
  -webkit-animation: drop-186 4.8062092766s 0.6296869143s infinite;
       -o-animation: drop-186 4.8062092766s 0.6296869143s infinite;
          animation: drop-186 4.8062092766s 0.6296869143s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-186 {
  100% {
    top: 110%;
    left: 101%;
  }
}
@-o-keyframes drop-186 {
  100% {
    top: 110%;
    left: 101%;
  }
}
@keyframes drop-186 {
  100% {
    top: 110%;
    left: 101%;
  }
}
.confetti-wrapper .confetti-187 {
  width: 6px;
  height: 2.4px;
  background-color: #3f51b5;
  top: -10%;
  left: 14%;
  opacity: 0.9883927927;
  -webkit-transform: rotate(114.2342681077deg);
      -ms-transform: rotate(114.2342681077deg);
       -o-transform: rotate(114.2342681077deg);
          transform: rotate(114.2342681077deg);
  -webkit-animation: drop-187 4.2712697281s 0.6199631146s infinite;
       -o-animation: drop-187 4.2712697281s 0.6199631146s infinite;
          animation: drop-187 4.2712697281s 0.6199631146s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-187 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@-o-keyframes drop-187 {
  100% {
    top: 110%;
    left: 27%;
  }
}
@keyframes drop-187 {
  100% {
    top: 110%;
    left: 27%;
  }
}
.confetti-wrapper .confetti-188 {
  width: 4px;
  height: 1.6px;
  background-color: #e91e63;
  top: -10%;
  left: 76%;
  opacity: 1.4848576256;
  -webkit-transform: rotate(247.258749016deg);
      -ms-transform: rotate(247.258749016deg);
       -o-transform: rotate(247.258749016deg);
          transform: rotate(247.258749016deg);
  -webkit-animation: drop-188 4.4197993543s 0.5085049004s infinite;
       -o-animation: drop-188 4.4197993543s 0.5085049004s infinite;
          animation: drop-188 4.4197993543s 0.5085049004s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-188 {
  100% {
    top: 110%;
    left: 90%;
  }
}
@-o-keyframes drop-188 {
  100% {
    top: 110%;
    left: 90%;
  }
}
@keyframes drop-188 {
  100% {
    top: 110%;
    left: 90%;
  }
}
.confetti-wrapper .confetti-189 {
  width: 1px;
  height: 0.4px;
  background-color: #00bcd4;
  top: -10%;
  left: 4%;
  opacity: 1.0676965553;
  -webkit-transform: rotate(158.5490898377deg);
      -ms-transform: rotate(158.5490898377deg);
       -o-transform: rotate(158.5490898377deg);
          transform: rotate(158.5490898377deg);
  -webkit-animation: drop-189 4.5578363459s 0.5513068975s infinite;
       -o-animation: drop-189 4.5578363459s 0.5513068975s infinite;
          animation: drop-189 4.5578363459s 0.5513068975s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-189 {
  100% {
    top: 110%;
    left: 8%;
  }
}
@-o-keyframes drop-189 {
  100% {
    top: 110%;
    left: 8%;
  }
}
@keyframes drop-189 {
  100% {
    top: 110%;
    left: 8%;
  }
}
.confetti-wrapper .confetti-190 {
  width: 10px;
  height: 4px;
  background-color: #03a9f4;
  top: -10%;
  left: 8%;
  opacity: 1.4570636774;
  -webkit-transform: rotate(148.766426975deg);
      -ms-transform: rotate(148.766426975deg);
       -o-transform: rotate(148.766426975deg);
          transform: rotate(148.766426975deg);
  -webkit-animation: drop-190 4.3809827553s 0.4318793299s infinite;
       -o-animation: drop-190 4.3809827553s 0.4318793299s infinite;
          animation: drop-190 4.3809827553s 0.4318793299s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-190 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@-o-keyframes drop-190 {
  100% {
    top: 110%;
    left: 19%;
  }
}
@keyframes drop-190 {
  100% {
    top: 110%;
    left: 19%;
  }
}
.confetti-wrapper .confetti-191 {
  width: 5px;
  height: 2px;
  background-color: #ff9800;
  top: -10%;
  left: 4%;
  opacity: 1.4403590872;
  -webkit-transform: rotate(163.294491352deg);
      -ms-transform: rotate(163.294491352deg);
       -o-transform: rotate(163.294491352deg);
          transform: rotate(163.294491352deg);
  -webkit-animation: drop-191 4.4928032134s 0.9257800462s infinite;
       -o-animation: drop-191 4.4928032134s 0.9257800462s infinite;
          animation: drop-191 4.4928032134s 0.9257800462s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-191 {
  100% {
    top: 110%;
    left: 6%;
  }
}
@-o-keyframes drop-191 {
  100% {
    top: 110%;
    left: 6%;
  }
}
@keyframes drop-191 {
  100% {
    top: 110%;
    left: 6%;
  }
}
.confetti-wrapper .confetti-192 {
  width: 9px;
  height: 3.6px;
  background-color: #ff5722;
  top: -10%;
  left: 71%;
  opacity: 0.6430276305;
  -webkit-transform: rotate(32.9537809697deg);
      -ms-transform: rotate(32.9537809697deg);
       -o-transform: rotate(32.9537809697deg);
          transform: rotate(32.9537809697deg);
  -webkit-animation: drop-192 4.5917137435s 0.3608447884s infinite;
       -o-animation: drop-192 4.5917137435s 0.3608447884s infinite;
          animation: drop-192 4.5917137435s 0.3608447884s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-192 {
  100% {
    top: 110%;
    left: 79%;
  }
}
@-o-keyframes drop-192 {
  100% {
    top: 110%;
    left: 79%;
  }
}
@keyframes drop-192 {
  100% {
    top: 110%;
    left: 79%;
  }
}
.confetti-wrapper .confetti-193 {
  width: 4px;
  height: 1.6px;
  background-color: #8bc34a;
  top: -10%;
  left: 12%;
  opacity: 0.594769742;
  -webkit-transform: rotate(304.032527382deg);
      -ms-transform: rotate(304.032527382deg);
       -o-transform: rotate(304.032527382deg);
          transform: rotate(304.032527382deg);
  -webkit-animation: drop-193 4.7025143447s 0.5894387946s infinite;
       -o-animation: drop-193 4.7025143447s 0.5894387946s infinite;
          animation: drop-193 4.7025143447s 0.5894387946s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-193 {
  100% {
    top: 110%;
    left: 24%;
  }
}
@-o-keyframes drop-193 {
  100% {
    top: 110%;
    left: 24%;
  }
}
@keyframes drop-193 {
  100% {
    top: 110%;
    left: 24%;
  }
}
.confetti-wrapper .confetti-194 {
  width: 5px;
  height: 2px;
  background-color: #cddc39;
  top: -10%;
  left: 56%;
  opacity: 0.7803238903;
  -webkit-transform: rotate(108.0691065398deg);
      -ms-transform: rotate(108.0691065398deg);
       -o-transform: rotate(108.0691065398deg);
          transform: rotate(108.0691065398deg);
  -webkit-animation: drop-194 4.4290498974s 0.81566047s infinite;
       -o-animation: drop-194 4.4290498974s 0.81566047s infinite;
          animation: drop-194 4.4290498974s 0.81566047s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-194 {
  100% {
    top: 110%;
    left: 57%;
  }
}
@-o-keyframes drop-194 {
  100% {
    top: 110%;
    left: 57%;
  }
}
@keyframes drop-194 {
  100% {
    top: 110%;
    left: 57%;
  }
}
.confetti-wrapper .confetti-195 {
  width: 5px;
  height: 2px;
  background-color: #4CAF50;
  top: -10%;
  left: 20%;
  opacity: 1.4377179823;
  -webkit-transform: rotate(324.6282548243deg);
      -ms-transform: rotate(324.6282548243deg);
       -o-transform: rotate(324.6282548243deg);
          transform: rotate(324.6282548243deg);
  -webkit-animation: drop-195 4.1695028831s 0.0751007639s infinite;
       -o-animation: drop-195 4.1695028831s 0.0751007639s infinite;
          animation: drop-195 4.1695028831s 0.0751007639s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-195 {
  100% {
    top: 110%;
    left: 22%;
  }
}
@-o-keyframes drop-195 {
  100% {
    top: 110%;
    left: 22%;
  }
}
@keyframes drop-195 {
  100% {
    top: 110%;
    left: 22%;
  }
}
.confetti-wrapper .confetti-196 {
  width: 8px;
  height: 3.2px;
  background-color: #cddc39;
  top: -10%;
  left: 72%;
  opacity: 1.3090892554;
  -webkit-transform: rotate(0.4359553708deg);
      -ms-transform: rotate(0.4359553708deg);
       -o-transform: rotate(0.4359553708deg);
          transform: rotate(0.4359553708deg);
  -webkit-animation: drop-196 4.3785165174s 0.7727057512s infinite;
       -o-animation: drop-196 4.3785165174s 0.7727057512s infinite;
          animation: drop-196 4.3785165174s 0.7727057512s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-196 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@-o-keyframes drop-196 {
  100% {
    top: 110%;
    left: 80%;
  }
}
@keyframes drop-196 {
  100% {
    top: 110%;
    left: 80%;
  }
}
.confetti-wrapper .confetti-197 {
  width: 2px;
  height: 0.8px;
  background-color: #ffc107;
  top: -10%;
  left: -5%;
  opacity: 1.3276079204;
  -webkit-transform: rotate(52.5946955141deg);
      -ms-transform: rotate(52.5946955141deg);
       -o-transform: rotate(52.5946955141deg);
          transform: rotate(52.5946955141deg);
  -webkit-animation: drop-197 4.0121223878s 0.2008197406s infinite;
       -o-animation: drop-197 4.0121223878s 0.2008197406s infinite;
          animation: drop-197 4.0121223878s 0.2008197406s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-197 {
  100% {
    top: 110%;
    left: 8%;
  }
}
@-o-keyframes drop-197 {
  100% {
    top: 110%;
    left: 8%;
  }
}
@keyframes drop-197 {
  100% {
    top: 110%;
    left: 8%;
  }
}
.confetti-wrapper .confetti-198 {
  width: 9px;
  height: 3.6px;
  background-color: #00bcd4;
  top: -10%;
  left: 16%;
  opacity: 1.444234462;
  -webkit-transform: rotate(31.7816255405deg);
      -ms-transform: rotate(31.7816255405deg);
       -o-transform: rotate(31.7816255405deg);
          transform: rotate(31.7816255405deg);
  -webkit-animation: drop-198 4.9906427985s 0.4348089119s infinite;
       -o-animation: drop-198 4.9906427985s 0.4348089119s infinite;
          animation: drop-198 4.9906427985s 0.4348089119s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-198 {
  100% {
    top: 110%;
    left: 20%;
  }
}
@-o-keyframes drop-198 {
  100% {
    top: 110%;
    left: 20%;
  }
}
@keyframes drop-198 {
  100% {
    top: 110%;
    left: 20%;
  }
}
.confetti-wrapper .confetti-199 {
  width: 9px;
  height: 3.6px;
  background-color: #673ab7;
  top: -10%;
  left: 58%;
  opacity: 0.5737458527;
  -webkit-transform: rotate(187.0296908243deg);
      -ms-transform: rotate(187.0296908243deg);
       -o-transform: rotate(187.0296908243deg);
          transform: rotate(187.0296908243deg);
  -webkit-animation: drop-199 4.1128810099s 0.6232266461s infinite;
       -o-animation: drop-199 4.1128810099s 0.6232266461s infinite;
          animation: drop-199 4.1128810099s 0.6232266461s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-199 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@-o-keyframes drop-199 {
  100% {
    top: 110%;
    left: 68%;
  }
}
@keyframes drop-199 {
  100% {
    top: 110%;
    left: 68%;
  }
}
.confetti-wrapper .confetti-200 {
  width: 3px;
  height: 1.2px;
  background-color: #4CAF50;
  top: -10%;
  left: 96%;
  opacity: 0.7553219061;
  -webkit-transform: rotate(166.4470609546deg);
      -ms-transform: rotate(166.4470609546deg);
       -o-transform: rotate(166.4470609546deg);
          transform: rotate(166.4470609546deg);
  -webkit-animation: drop-200 4.1685426366s 0.4863507906s infinite;
       -o-animation: drop-200 4.1685426366s 0.4863507906s infinite;
          animation: drop-200 4.1685426366s 0.4863507906s infinite;
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
@-webkit-keyframes drop-200 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@-o-keyframes drop-200 {
  100% {
    top: 110%;
    left: 100%;
  }
}
@keyframes drop-200 {
  100% {
    top: 110%;
    left: 100%;
  }
}