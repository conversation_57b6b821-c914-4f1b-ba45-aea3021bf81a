<?xml version="1.0" encoding="utf-8" ?>
<Action autoNext="true">

  <!-- ************************************************************ -->
  <!-- *********************** AGGREGATION  *********************** -->
  <!-- ************************************************************ -->

  <Aggregator doneText="">
    <ParticipantFilter>
        <Question>Filter_Agg</Question>
    </ParticipantFilter>

    <!-- KPI1 -->
    <Score result="Score_SIM_R5_Scenario2_KPI1" type="Choice"> 
      <Question name="Q_SIM_R5_Scenario2">
          <Choice value="1" Response="!{SIM_R5_Scenario2_Opt1_KPI1}"></Choice>
          <Choice value="2" Response="!{SIM_R5_Scenario2_Opt2_KPI1}"></Choice>
          <Choice value="3" Response="!{SIM_R5_Scenario2_Opt3_KPI1}"></Choice>
          <Choice value="4" Response="!{SIM_R5_Scenario2_Opt4_KPI1}"></Choice>
      </Question>
    </Score> 
    <Total result="Score_SIM_Total_KPI1" method="sum">
      <Question validate="false">Score_SIM_Total_R4_KPI1</Question>
      <Question validate="false">Score_SIM_R5_Scenario1_KPI1</Question>
      <Question validate="false">Score_SIM_R5_Scenario2_KPI1</Question>
    </Total>    
    
    <!-- KPI2 -->
    <Score result="Score_SIM_R5_Scenario2_KPI2" type="Choice"> 
      <Question name="Q_SIM_R5_Scenario2">
          <Choice value="1" Response="!{SIM_R5_Scenario2_Opt1_KPI2}"></Choice>
          <Choice value="2" Response="!{SIM_R5_Scenario2_Opt2_KPI2}"></Choice>
          <Choice value="3" Response="!{SIM_R5_Scenario2_Opt3_KPI2}"></Choice>
          <Choice value="4" Response="!{SIM_R5_Scenario2_Opt4_KPI2}"></Choice>
      </Question>
    </Score> 
    <Total result="Score_SIM_Total_KPI2" method="sum">
      <Question validate="false">Score_SIM_Total_R4_KPI2</Question>
      <Question validate="false">Score_SIM_R5_Scenario1_KPI2</Question>
      <Question validate="false">Score_SIM_R5_Scenario2_KPI2</Question>
    </Total>      
    
    <!-- KPI3 -->
    <Score result="Score_SIM_R5_Scenario2_KPI3" type="Choice"> 
      <Question name="Q_SIM_R5_Scenario2">
          <Choice value="1" Response="!{SIM_R5_Scenario2_Opt1_KPI3}"></Choice>
          <Choice value="2" Response="!{SIM_R5_Scenario2_Opt2_KPI3}"></Choice>
          <Choice value="3" Response="!{SIM_R5_Scenario2_Opt3_KPI3}"></Choice>
          <Choice value="4" Response="!{SIM_R5_Scenario2_Opt4_KPI3}"></Choice>
      </Question>
    </Score> 
    <Total result="Score_SIM_Total_KPI3" method="sum">
      <Question validate="false">Score_SIM_Total_R4_KPI3</Question>
      <Question validate="false">Score_SIM_R5_Scenario1_KPI3</Question>
      <Question validate="false">Score_SIM_R5_Scenario2_KPI3</Question>
    </Total>    



    <!-- KPI: TOTAL -->
    <Total result="Score_SIM_Total" method="avg">
      <Question validate="false">Score_SIM_Total_KPI1</Question>
      <Question validate="false">Score_SIM_Total_KPI2</Question>
      <Question validate="false">Score_SIM_Total_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_Rank" method="samerank">
        <Question validate="false">Score_SIM_Total</Question>
    </Total>
    
    <Total result="Score_SIM_Total_R5_KPI1" method="sum">
      <Question validate="false">Score_SIM_Total_KPI1</Question>
    </Total>
    <Total result="Score_SIM_Total_R5_KPI2" method="sum">
      <Question validate="false">Score_SIM_Total_KPI2</Question>
    </Total>
    <Total result="Score_SIM_Total_R5_KPI3" method="sum">
      <Question validate="false">Score_SIM_Total_KPI3</Question>
    </Total>
    <Total result="Score_SIM_Total_R5" method="sum">
      <Question validate="false">Score_SIM_Total</Question>
    </Total>
    <Total result="Score_SIM_Total_R5_Rank" method="samerank">
        <Question validate="false">Score_SIM_Total</Question>
    </Total>

  </Aggregator>


</Action>
