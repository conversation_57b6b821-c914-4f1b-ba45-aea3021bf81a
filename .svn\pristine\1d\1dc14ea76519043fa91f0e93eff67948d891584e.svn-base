.wizlet.wizletHCLineChart .header.with-label {
  position: relative;
  padding-left: 2.5rem;
  margin-bottom: 1rem;
}
.wizlet.wizletHCLineChart .header.with-label .badge.header-label {
  position: absolute;
  left: 0;
  top: 0;
  background-color: #cc4a3d;
  min-width: 2rem;
  min-height: 2rem;
  font-size: 1.25rem;
  font-weight: bold;
  border-radius: 50%;
  line-height: 2rem;
  margin: 0;
  margin-top: -5px;
}
.wizlet.wizletHCLineChart .card .card-content {
  padding: 6px 12px;
}
.wizlet.wizletHCLineChart .card .card-content .row {
  margin: 0;
}
.wizlet.wizletHCLineChart .highcharts-container > svg {
  width: 100% !important;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-background {
  fill: transparent;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-title {
  font-size: 1.25rem;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-subtitle {
  font-size: 1rem;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-label,
.wizlet.wizletHCLineChart .highcharts-container .highcharts-axis-labels {
  color: initial;
  fill: initial;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-label.highcharts-xaxis-labels,
.wizlet.wizletHCLineChart .highcharts-container .highcharts-axis-labels.highcharts-xaxis-labels {
  font-size: 125%;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-color-0 {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-color-1 {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-color-2 {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-color-3 {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-color-4 {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-color-5 {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-color-6 {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-color-7 {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-color-8 {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-color-9 {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-color-10 {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-color-11 {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCLineChart .highcharts-container .highcharts-tooltip table tr, .wizlet.wizletHCLineChart .highcharts-container .highcharts-tooltip table td {
  padding: 0;
}
.wizlet.wizletHCLineChart .answers.card-panel {
  padding: 8px 12px;
}
.wizlet.wizletHCLineChart .answers.card-panel .btn-text {
  display: inline-block;
  vertical-align: sub;
}