<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayoutFAC">
  <Include name="HeaderFAC"></Include>

    
  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{SIM_R2_Finish_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R2_Finish_Img}",  alt: "!{SIM_R2_Finish_Title}" ,
          position: "right large", style: ""
        },
        _img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R2_Finish_Img}",  alt: "!{SIM_R2_Finish_Title}" ,
          src_vert: "!{}"
        },
        position: "up",
        title: "!{SIM_R2_Finish_Title}",
        body: "!{SIM_R2_Finish_Text}"
      }      
    }]]>
  </Component>
  
<!--   
  <Component type="HCBarChart" customJS="true"><![CDATA[{
    templateInEvent: "html/HCBarChart.dot",
    css: "styles/HCBarChart.css",
    header: "!{}",
    subheader: "!{}",
    instructions: "!{}",   

    isStacked: true,

    isAnswersDistribution: false, 

    isRanking: false,
    isCheckBox: false,
    isPercentage: false, 

    listenModel: false,
    questions: [
      { binding: "Score_SIM_Total_KPI1" },
      { binding: "Score_SIM_Total_KPI2" }
    ],
    reversed: true,
    trackQuestion: "",
    _sorting: {
      question: "Q_B1_Intro_Range",
      isNumeric: true
    },
    chartConfig: {
      chart: {        
        _type: "column",
        _marginTop: 15,
        type: "bar",
        marginRight: 15
      },
      title: {
        text: "!{KPI_Title}"
      },
      subtitle: {
        text: ""
      },
      xAxis: [ 
        {
          categories: [ ],
          question: "Q_My_Name",
          isName: true
        }
      ],
      labels: {
        format: "{value}"
      },
      yAxis: {
        min: 0,
        _max: 100,
        title: {
          text: "!{}"
        },
        endOnTick: true,
        labels: {
          format: "{value}",
          _format: "{value:.0f} %",
          _step: 2
        },
        stackLabels: {
          enabled: true,
          _format: "{total:.1f} %",
          format: "{total}",
          style: {
            fontWeight: 'bold'
          }
        },
        reversedStacks: false
      },
      tooltip: {
        _headerFormat: "<b>{point.x}</b><br/> <table>",
        _pointFormat: '<tr><td><b>{point.y:.1f} %</b></td></tr>',
        headerFormat: "<b>{point.x}</b><br/> <table>",
        pointFormat: '<tr><td>{series.name}:  <b>{point.y}</b></td></tr>',
        footerFormat: '</table>',
        crosshairs: true,
        shared: true,
        useHTML: true
      },
      legend: {
        reversed: false,
        enabled: false,
        align: "center",
        verticalAlign: "bottom",
        floating: false
      },
      plotOptions: {
        bar: {
          minPointLength: 5,
          stacking: "normal",
          dataLabels: {
            enabled: false,
            format: '{point.y:.0f}',
            allowOverlap: true,
            inside: true
          }
        },
        column: {
          minPointLength: 5,
          stacking: "normal",
          dataLabels: {
            enabled: false,
            format: "{point.y:.0f}",
            inside: true
          }
        }
      },
      series: [ 
        {
          name: "!{KPI_Metric1}"
        },
        {
          name: "!{KPI_Metric2}"
        }
      ],
      _solutionLine: {
        type: "spline",
        name: "!{}",
        data: [50]
      }
    },

    scope: [ ],
    
    _answers: "!{Results_VotesLabel}"
  }]]></Component> -->




  
  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: false, showDelay: "",
      hasModals: true,
      _buttons: [
        {
          type: "target",
          pulse: false,
          gdActionTrack: "GD",
          gdActionSection: "SIM_R2_Start",
          targetSection: "SIM_R2_FAC_dashboard",
          label: "!{GD_SIM_R2_Start}",
          icon: "looks_two"
        }
      ],
      buttons: [
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list"
        },
        {
          type: "target",
          pulse: true,
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "R2_DebriefPage",
          targetSection: "R2_DebriefPage_FAC",
          label: "!{GD_DebriefPage}",
          icon: "insert_chart"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>



</Action>