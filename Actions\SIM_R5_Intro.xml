<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayout_SIM_R5">
  <Include name="Header_SIM_R5"></Include>
  <Include name="KPIgauges"></Include>


  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "horizontal",
      header: "!{SIM_R5_Intro_Header}",
      valign: false,
      animate: "fadeIn",
      content: {
        img_embed: {
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R5_Intro_Img}",  alt: "!{SIM_R5_Intro_Title}" ,
          position: "right", style: "",
          src_vert: "!{}",
          animate: "fadeInRight animate__delay-1s", _animateLater: "bounce"
        },
        _img: {
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R5_Intro_Img}",  alt: "!{SIM_R5_Intro_Title}" ,
          isHiddenWhenSmall: true,
          src_vert: "!{}"
        },
        position: "up",
        animate: "fadeInUp animate__delay-2s",
        title: "!{SIM_R5_Intro_Title}",
        body: "!{SIM_R5_Intro_Text}"
      }
    }]]>
  </Component>




  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "shake",
      id: "navButton",
      isHidden: true, showDelay: "5000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "",
          targetSection: "",
          label: "!{Navigation_start}",
          tooltip: "!{Navigation_start_tt}",
          icon: "forward"
        }
      ],

      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>




</Action>