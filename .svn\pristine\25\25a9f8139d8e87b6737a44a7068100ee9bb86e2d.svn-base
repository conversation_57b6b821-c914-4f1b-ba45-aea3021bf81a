﻿
define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min'], function($, Q, WizerApi, WizletBase, doT) {

    var Modal = function() {
        this.type = 'Modal';
        this.level = 1;
    };

    Modal.prototype.loadHandler = function(unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        if (wizletInfo.templateInEvent) {
            requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        }
        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function(doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    Modal.prototype.unloadHandler = function() {
        $('.material-tooltip').remove();
        WizletBase.unloadHandler({ wizlet: this });
    };

    Modal.prototype.render = function(options) {
        var self = this;
        return self.templateDefer.promise.then(function(template) {
                var fragment = template(options.wizletInfo);
                options.context.html(fragment);
                
                $('.material-tooltip').remove();

                if (options.wizletInfo.alreadyRead) {
                    
                    options.context.find('.modal').modal({
                        onCloseEnd : function(current_item) {
                            self.addVote(options.wizletInfo.alreadyRead, 1);
                        }
                    });

                    var questId = self.wizerApi.getQuestionIdByName(options.wizletInfo.alreadyRead);
                    
                    self.wizerApi.getMyVotes([questId]).then(function(response) {

                        if ((response.votes[questId].length==0) || (response.votes[questId][0] != 1)) {
                            

                            self.open(options.context.find('.modal'));
                        } 
        
                    });
                } else {
                    

                    options.context.find('.modal').modal({
                        onCloseStart : function(current_item) {
                            if (self.wizletInfo.stopVideos) {
                                self.wizletContext.find('video').get(0).pause();
                                self.wizletContext.find('iframe').attr('src', self.wizletContext.find('iframe').attr('src'));
                            }
                        }
                    });

                    //self.open(options.context.find('.modal')); => opened manually by id in other component/action
                }

                return true;
            })
            .fail(this.wizerApi.showError)
    };


    Modal.prototype.init = function (modal) {
        modal.modal();
    };

    Modal.prototype.open = function (modal) {
        setTimeout(
            function() { 
                M.Modal.getInstance( modal ).open();
            }, 
            1000
        );
    };


    /**
     * Promise function: Addvote value to a questionName
     */
    Modal.prototype.addVote = function (questionName, val) {
        var self = this;

        var questionId = self.wizerApi.getQuestionIdByName(questionName);

        var defer = new Q.defer();

        var waiting = self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});

        waiting.then(function (result) {            
            defer.resolve(result);
        });                    
        
        return defer.promise;
    };


    Modal.getRegistration = function() {
        return new Modal();
    };

    return Modal;

});