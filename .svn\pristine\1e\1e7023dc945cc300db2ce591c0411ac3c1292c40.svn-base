﻿<div class="container{{?it.animate}} animated {{=it.animate}}{{?}}{{?it.isHiddenWhenSmall}} hide-on-med-and-down{{?}}{{?it.isHiddenWhenBig}} hide-on-large-only{{?}}"
{{?it.id}}id="{{=it.id}}"{{?}} {{?it.isHidden}}hidden{{?}}> 
    
    {{?it.header}}<h4 class="header {{?it.header_color}}client-colors-text text-{{=it.header_color}}{{?}}">{{=it.header}}</h4>{{?}}
    {{?it.subheader}}<h5 class="header {{?it.header_color}}client-colors-text text-{{=it.header_color}}{{?}}">{{=it.subheader}}</h5>{{?}}
    {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}

    <div class="card">

        
        <div class="card-content row">

            {{?it.content && (it.content.title || it.content.body)}}
            <div class="content">
                {{?it.content.title}}<span class="card-title">{{=it.content.title}}</span>{{?}}
                {{?it.content.body}}<p class="flow-text">{{=it.content.body}}</p>{{?}}
            </div>
            {{?}}


            <div class="dropdown">

                
                <div class="option col {{?it.grid_option}}{{=it.grid_option}}{{??}}s6{{?}}">
                    {{?it.title}}<h5 class="header title"> {{=it.title}} </h5> {{?}}
                </div>

                
                <div class="button col {{?it.grid_button}}{{=it.grid_button}}{{??}}s6{{?}}">
                    <!-- Dropdown Trigger -->
                    <a class="dropdown-trigger btn" data-target="{{=it.id}}UL" data-value="0">
                        <span class="col s11 truncate placeholder">{{=it.placeholder}}</span>
                        <i class="col s1 material-icons right">expand_more</i>
                    </a>

                    <!-- Dropdown Structure -->
                    <ul id="{{=it.id}}UL" class="dropdown-content">   
                        {{~it.items :item:idx}}
                            <li data-value="{{=item.value}}"
                                {{?it.bind}}data-bind="{{=it.bind}}"{{?}} 
                                {{?it.binding}}data-bind="{{=it.binding}}"{{?}} >
                                
                                <a>
                                    <span class="label">{{=item.label}}</span>
                                    {{?item.icon}}<i class="material-icons">{{=item.icon}}</i>{{?}}
                                </a>
                                
                                <!-- Model renderer -->
                                {{?it.binding}}
                                    <span class="model-render" data-binding="{{=it.binding}}" data-value="{{=item.value}}"> </span>
                                {{?}}
                            </li>

                            {{?idx < (it.items.length-1)}}
                                <li class="divider" tabindex="-1"></li>
                            {{?}}
                            
                        {{~}}
                    </ul>
                    
                </div>

                
            </div>

        </div>  
                    
    </div>

</div>

