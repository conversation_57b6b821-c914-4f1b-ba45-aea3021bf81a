<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutLanding_FAC_R1">
  <Include name="Header_IntroFAC_R1"></Include>

  <Component type="Vanilla"><![CDATA[{
    templateInEvent: "html/landingPage.dot",
    css: "styles/landingPage.css",
    fullScreen: true,
    facilitator: true,
    content: {
      withBubbles: false,
      title: "!{Landing_Title}",
      subtitle: "!{Landing_Subtitle}",
      image: "!{LandingPage_ImgFac}"
    }
  }]]></Component>


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - showDelay: show the hidden button after Xms (waiting the animation)
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "center",
      animate: "zoomIn animate__delay-4s",
      id: "btn_navigation_home",
      _isHidden: true, _showDelay: 3000,
      hasModals: true,
      buttons: [
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list",
          isFloat: false, 
          tooltip: "!{}"
        },
        {
          type: "target",
          pulse: false,
          isLarge: false,
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R1_Start",
          targetSection: "SIM_R1_FAC_dashboard",
          label: "!{GD_SIM_R1_Start}",
          icon: "looks_one"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>



  <!-- Reset all participant's votes when landing -->
  <Component type="myCode_reset" customJS="true"><![CDATA[{
      clearAll: true,
      resetVotes: false
  }]]></Component> 

  
  <!-- EXPORT CSV BUTTON -->
  <!-- <Component type="ActionButton" customJS="true">
    <![CDATA[{
      templateInEvent: "html/actionButton.dot",
      css: "styles/actionButton.css",
      animate: "bounceInUp",
      id: "btn_export",
      isHidden: true, showDelay: 3000,
      title: "!{GroupDirector_ExportBtn}",
      icon: "cloud_download",
      _onclick: "window.location.href = 'Wizer/Reporting/GetSimulationReport'",
      pulse: false,
      modal: {
        modalID: "modal-export",
        header: "!{GroupDirector_Export_Modal_Title}",
        text: "!{GroupDirector_Export_Modal_Text}",
        close: "!{GroupDirector_Export_Modal_Close}",
        action: "!{GroupDirector_Export_Modal_Action}",
        onclick: "window.open('Wizer/Reporting/GetSimulationReport')",
        onclickFunction: "closeModal",
        onclickQuestion: "modal-export"
      },
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component> -->

  <!-- RESET EVENT BUTTON -->
  <Component type="ActionButton" customJS="true">
    <![CDATA[{
      templateInEvent: "html/actionButton.dot",
      css: "styles/actionButton.css",
      animate: "zoomIn animate__delay-3s",
      id: "btn_reset",
      _isHidden: true, _showDelay: 3000,
      title: "!{GroupDirector_ResetBtn}",
      icon: "delete_forever",
      onclick: "",
      pulse: true,
      color: "red",
      modal: {
        modalID: "modal-reset",
        header: "!{GroupDirector_ResetModalHeader}",
        text: "!{GroupDirector_ResetModalText}",
        close: "!{GroupDirector_ResetModalClose}",
        action: "!{GroupDirector_ResetModalReset}",
        onclick: "",
        onclickFunction: "resetEvent",
        onclickQuestion: "GD"
      },
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <Voting autoNext="false">
    <Score type="Vote" result="Q_FAC_Navigation_Tab" response="0"/> 
  </Voting>


</Action>