.wizlet.wizletHCBarChartModel .card .card-content {
  padding: 6px 12px;
}
.wizlet.wizletHCBarChartModel .card .card-content .row {
  margin: 0;
}
.wizlet.wizletHCBarChartModel .card .card-content .bach-content-stackedColumnChart--container {
  height: auto;
}
.wizlet.wizletHCBarChartModel .highcharts-container > svg {
  width: 100% !important;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-background {
  fill: transparent;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-title {
  font-size: 1.25rem;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-subtitle {
  font-size: 1rem;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-label,
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-axis-labels {
  color: initial;
  fill: initial;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-label > text, .wizlet.wizletHCBarChartModel .highcharts-container .highcharts-label > span,
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-axis-labels > text,
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-axis-labels > span {
  font-size: 1rem;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-label > text:last-child:not([transform]), .wizlet.wizletHCBarChartModel .highcharts-container .highcharts-label > span:last-child:not([transform]),
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-axis-labels > text:last-child:not([transform]),
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-axis-labels > span:last-child:not([transform]) {
  -webkit-transform: translateX(-4px);
      -ms-transform: translateX(-4px);
       -o-transform: translateX(-4px);
          transform: translateX(-4px);
  -webkit-transform: translateY(2px);
      -ms-transform: translateY(2px);
       -o-transform: translateY(2px);
          transform: translateY(2px);
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-data-label span {
  color: #ffffff;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-data-label text {
  fill: #ffffff;
  text-shadow: 1px 1px #24466b;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-spline-series .highcharts-data-label span {
  color: #24466b;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-spline-series .highcharts-data-label text {
  fill: #24466b;
  text-shadow: 1px 1px #ffffff;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-axis .highcharts-axis-title tspan {
  font-weight: bold;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-color-0 {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-color-1 {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-color-2 {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-color-3 {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-color-4 {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-color-5 {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-color-6 {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-color-7 {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-color-8 {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-color-9 {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-color-10 {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-color-11 {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(0) {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(1) {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(2) {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(3) {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(4) {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(5) {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(6) {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(7) {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(8) {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(9) {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(10) {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(11) {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-tooltip .highcharts-tooltip-box {
  opacity: 1;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-tooltip table tr, .wizlet.wizletHCBarChartModel .highcharts-container .highcharts-tooltip table td {
  padding: 0;
}
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-series-group .highcharts-spline-series,
.wizlet.wizletHCBarChartModel .highcharts-container .highcharts-series-group .highcharts-spline-series .highcharts-point {
  fill: #cc4a3d;
  stroke: #cc4a3d;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(1) {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(2) {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(3) {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(4) {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(5) {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(6) {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(7) {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(8) {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(9) {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(10) {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(11) {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(12) {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.noclickablelegend .highcharts-legend {
  pointer-events: none;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color.categoria1 .highcharts-color-0 .highcharts-point, .wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color.categoria1 .highcharts-color-0.highcharts-legend-item .highcharts-graph {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color.categoria1 .highcharts-color-0.highcharts-spline-series .highcharts-graph {
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color.categoria2 .highcharts-color-0 .highcharts-point, .wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color.categoria2 .highcharts-color-0.highcharts-legend-item .highcharts-graph {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color.categoria2 .highcharts-color-0.highcharts-spline-series .highcharts-graph {
  stroke: #727FAE;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color.categoria3 .highcharts-color-0 .highcharts-point, .wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color.categoria3 .highcharts-color-0.highcharts-legend-item .highcharts-graph {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color.categoria3 .highcharts-color-0.highcharts-spline-series .highcharts-graph {
  stroke: #72BF44;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color.categoria4 .highcharts-color-0 .highcharts-point, .wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color.categoria4 .highcharts-color-0.highcharts-legend-item .highcharts-graph {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color.categoria4 .highcharts-color-0.highcharts-spline-series .highcharts-graph {
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color1.categoria1 .highcharts-color-1 .highcharts-point, .wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color1.categoria1 .highcharts-color-1.highcharts-legend-item .highcharts-graph {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color1.categoria1 .highcharts-color-1.highcharts-spline-series .highcharts-graph {
  stroke: #727FAE;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color1.categoria2 .highcharts-color-1 .highcharts-point, .wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color1.categoria2 .highcharts-color-1.highcharts-legend-item .highcharts-graph {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color1.categoria2 .highcharts-color-1.highcharts-spline-series .highcharts-graph {
  stroke: #72BF44;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color1.categoria3 .highcharts-color-1 .highcharts-point, .wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color1.categoria3 .highcharts-color-1.highcharts-legend-item .highcharts-graph {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChartModel .bach-content-stackedColumnChart--container.color1.categoria3 .highcharts-color-1.highcharts-spline-series .highcharts-graph {
  stroke: #0AB9F0;
}