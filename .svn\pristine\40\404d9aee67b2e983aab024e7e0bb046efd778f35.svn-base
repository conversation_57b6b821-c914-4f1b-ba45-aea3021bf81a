.header-container {
  position: relative;
}
.header-container .header.help {
  padding-right: 36px;
}
.header-container i.help {
  position: absolute;
  right: 0;
  bottom: 0;
  -webkit-transform: translate(-50%, 0%);
      -ms-transform: translate(-50%, 0%);
       -o-transform: translate(-50%, 0%);
          transform: translate(-50%, 0%);
  cursor: pointer;
}
.header-container i.help:hover {
  -webkit-transform: scale(1.1) translate(-50%, 0%);
      -ms-transform: scale(1.1) translate(-50%, 0%);
       -o-transform: scale(1.1) translate(-50%, 0%);
          transform: scale(1.1) translate(-50%, 0%);
}

.wizlet.wizletTabs .tabs-holder {
  width: 104%;
  margin-left: -2%;
  margin-bottom: 5px;
}
.wizlet.wizletTabs .tabs-holder ul.tabs {
  overflow-x: auto;
  -ms-overflow-style: none;
  border-radius: 20px;
  -webkit-background-clip: padding-box;
  -moz-background-clip: padding;
  background-clip: padding-box;
}
.wizlet.wizletTabs .tabs-holder ul.tabs::-webkit-scrollbar {
  height: 0 !important;
}
.wizlet.wizletTabs .tabs-holder ul.tabs li.tab {
  width: auto;
  border-color: rgba(204, 74, 61, 0.2);
  border-style: solid;
  border-width: 0 1px 0 1px;
}
.wizlet.wizletTabs .tabs-holder ul.tabs li.tab:first-child {
  border-left-width: 0;
}
.wizlet.wizletTabs .tabs-holder ul.tabs li.tab.last {
  border-right-width: 0;
}
.wizlet.wizletTabs .tabs-holder ul.tabs li.tab a {
  color: #24466b;
  background-color: #cc4a3d;
  position: relative;
  padding: 0;
  font-size: 1rem;
}
.wizlet.wizletTabs .tabs-holder ul.tabs li.tab a:hover {
  background-color: #cc4a3d;
  color: #ffffff;
}
.wizlet.wizletTabs .tabs-holder ul.tabs li.tab a.active, .wizlet.wizletTabs .tabs-holder ul.tabs li.tab a:focus.active {
  background-color: #232323;
  color: #ffffff;
}
@media only screen and (min-width : 601px) {
  .wizlet.wizletTabs .tabs-holder ul.tabs li.tab a {
    font-size: 1.2rem;
  }
}
@media only screen and (min-width : 993px) {
  .wizlet.wizletTabs .tabs-holder ul.tabs li.tab a {
    font-size: 1.3rem;
  }
}
@media only screen and (min-width : 1201px) {
  .wizlet.wizletTabs .tabs-holder ul.tabs li.tab a {
    font-size: 1.4rem;
  }
}
@media only screen and (min-width : 1401px) {
  .wizlet.wizletTabs .tabs-holder ul.tabs li.tab a {
    font-size: 1.5rem;
  }
}
.wizlet.wizletTabs .tabs-holder ul.tabs li.tab a i.check-icon {
  position: absolute;
  right: 20px;
}
.wizlet.wizletTabs .tabs-holder ul.tabs li.indicator {
  display: none;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletTabs .tabs-holder.vertical-mode {
    width: 10%;
    left: 0;
    position: absolute;
    margin: 0;
  }
  .wizlet.wizletTabs .tabs-holder.vertical-mode ul.tabs {
    display: block;
    height: auto;
    background-color: transparent;
    -webkit-box-shadow: none;
            box-shadow: none;
    overflow-x: hidden;
    overflow-y: auto;
  }
  .wizlet.wizletTabs .tabs-holder.vertical-mode ul.tabs > li.tab {
    display: block;
    border: none;
    padding: 5px 0;
    height: 40px;
  }
  .wizlet.wizletTabs .tabs-holder.vertical-mode ul.tabs > li.tab:first-child {
    border-top-width: 0;
    padding-top: 0;
  }
  .wizlet.wizletTabs .tabs-holder.vertical-mode ul.tabs > li.tab.last {
    border-bottom-width: 0;
    padding-bottom: 0;
  }
  .wizlet.wizletTabs .tabs-holder.vertical-mode ul.tabs > li.tab > a {
    border-radius: 0 10px 10px 0;
    font-size: 20px;
    line-height: 35px;
  }
}
@media only screen and (min-width : 601px) {
  .wizlet.wizletTabs .tabs-holder.small ul.tabs li.tab a {
    font-size: 1rem;
  }
}
@media only screen and (min-width : 993px) {
  .wizlet.wizletTabs .tabs-holder.small ul.tabs li.tab a {
    font-size: 1.1rem;
  }
}
@media only screen and (min-width : 1201px) {
  .wizlet.wizletTabs .tabs-holder.small ul.tabs li.tab a {
    font-size: 1.2rem;
  }
}
@media only screen and (min-width : 1401px) {
  .wizlet.wizletTabs .tabs-holder.small ul.tabs li.tab a {
    font-size: 1.3rem;
  }
}

.tabs-content.carousel.carousel-slider {
  height: auto !important;
}
.tabs-content.carousel.carousel-slider .carousel-item {
  min-height: auto;
  overflow-x: auto;
  -ms-overflow-style: none;
  overflow-y: auto;
  -ms-overflow-style: none;
}
.tabs-content.carousel.carousel-slider .carousel-item.active {
  position: relative;
}
.tabs-content.carousel.carousel-slider .carousel-item::-webkit-scrollbar {
  height: 0 !important;
}
.tabs-content.carousel.carousel-slider .carousel-item::-webkit-scrollbar {
  width: 0 !important;
}

.modal-content .wizlet.wizletTabs .tabs-holder {
  width: initial;
  margin-left: 0;
}