.wizlet.wizletVanilla .actionbutton.fixed,
.wizlet.wizletActionButton .actionbutton.fixed {
  position: fixed;
  right: 10px;
  bottom: 40px;
  z-index: 999;
}
.wizlet.wizletVanilla .actionbutton > .row,
.wizlet.wizletActionButton .actionbutton > .row {
  clear: both;
}
.wizlet.wizletVanilla .actionbutton > .row.fixed,
.wizlet.wizletActionButton .actionbutton > .row.fixed {
  float: none !important;
}
.wizlet.wizletVanilla .actionbutton > .row.fixed.left,
.wizlet.wizletActionButton .actionbutton > .row.fixed.left {
  text-align: left;
}
.wizlet.wizletVanilla .actionbutton > .row.fixed.center,
.wizlet.wizletActionButton .actionbutton > .row.fixed.center {
  text-align: center;
}
.wizlet.wizletVanilla .actionbutton > .row.fixed.right,
.wizlet.wizletActionButton .actionbutton > .row.fixed.right {
  text-align: right;
}
.wizlet.wizletVanilla .actionbutton .btn-large,
.wizlet.wizletActionButton .actionbutton .btn-large {
  font-size: inherit;
}
.wizlet.wizletVanilla .actionbutton span[disabled] a,
.wizlet.wizletActionButton .actionbutton span[disabled] a {
  pointer-events: none;
  background-color: #DFDFDF !important;
  -webkit-box-shadow: none;
          box-shadow: none;
  color: #9F9F9F !important;
  cursor: default;
}
.wizlet.wizletVanilla .actionbutton span[disabled] a:hover,
.wizlet.wizletActionButton .actionbutton span[disabled] a:hover {
  background-color: #DFDFDF !important;
  color: #9F9F9F !important;
}
.wizlet.wizletVanilla .actionbutton .row.submit,
.wizlet.wizletActionButton .actionbutton .row.submit {
  padding-top: 5px;
  margin-right: 0;
  text-align: right;
}
.wizlet.wizletVanilla .actionbutton .row.submit #checkBtn[hidden],
.wizlet.wizletActionButton .actionbutton .row.submit #checkBtn[hidden] {
  display: inline-block !important;
  visibility: hidden;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletVanilla .actionbutton .row.submit .btn,
  .wizlet.wizletActionButton .actionbutton .row.submit .btn {
    font-size: 0;
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
  .wizlet.wizletVanilla .actionbutton .row.submit .btn i,
  .wizlet.wizletActionButton .actionbutton .row.submit .btn i {
    width: inherit;
    display: inline-block;
    text-align: center;
    color: #fff;
    font-size: 1.6rem;
    line-height: 40px;
  }
}