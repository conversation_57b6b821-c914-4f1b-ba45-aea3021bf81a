﻿define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT'], function ($, Q, WizerApi, WizletBase, doT) {

    var Table = function () {
        this.type = 'Table';
        this.level = 1;                
    };

    Table.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        this.votesBeforeUpdate = [];

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];

        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    Table.prototype.unloadHandler = function () {
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        WizletBase.unloadHandler({ wizlet: this });
    };


    Table.prototype.render = function (options) {
        var self = this;
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
            

            if (self.wizletInfo.saveInModel) {
                  

                var inputs = options.context.find('input');
                $.each (inputs, function(idx, input) {
                    var bind = $(input).data('binding');
                    var binding = self.wizletInfo.bindings[ bind ];
                    
                    self.saveCalcValueToDB(bind, binding.bind, binding.model).then(function(value){
                        input.value = value;
                    });
                });


                inputs.on('change', function(event) {
                    var value = this.value;
                    var bind = $(this).data('bind') || $(this).data('binding');

                    $(this).attr('data-value', value);
                    self.addVote( bind,  value);

                    if (self.wizletInfo.saveInModelLater) {
                        $(this).addClass('saveInModelLater');
                    } else {
                        $(this).trigger('updateModelInput');
                    }
                });

                
                inputs.on("keydown", function(event) {
                    self.previousValue = this.value;
                });
                inputs.on("keyup", function(event) {
                    
                    if ((this.value > parseInt($(this).attr('max'))) || (this.value < parseInt($(this).attr('min')))) {
                        this.value = self.previousValue;
                    }
                });
            
            }

            return true;
        })
        .fail(this.wizerApi.showError);
    };


    
    Table.prototype.saveCalcValueToDB = function(simQuestion, tlQuestion, modelname){
        var self = this;

        tlQuestion = tlQuestion.replace("calc:", "");

        var defer = new Q.defer();        
        self.wizerApi.calcBinderCache.getCalcValue(modelname, tlQuestion).then(function(value){
            
            self.addVote(simQuestion,value);
            defer.resolve(value);
        });
        
        return defer.promise;
    }

    
    /**
     * Promise function: Addvote value to a questionName
     */
    Table.prototype.addVote = function (questionName, val) {
        var self = this;
        
        var questionId = self.wizerApi.getQuestionIdByName(questionName);

        var defer = new Q.defer();

        var waiting = self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});

        waiting.then(function (result) {            
            defer.resolve(result);
        });                    
        
        return defer.promise;
    };


    Table.getRegistration = function () {
        return new Table();
    };

    return Table;

});