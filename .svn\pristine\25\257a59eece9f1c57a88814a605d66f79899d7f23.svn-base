.wizlet.wizletCollapsible .card .card-content.card-content {
  background-color: #FFFFFF;
  border-radius: 10px;
}
.wizlet.wizletCollapsible .card .card-content .card-title {
  font-family: clientBolder, clientBold, Arial;
  font-size: 150%;
  line-height: 100%;
  text-align: left;
  padding-bottom: 5px;
  border-bottom: 3px solid #cc4a3d;
  margin-bottom: 15px;
}
.wizlet.wizletCollapsible .card .card-content .card-title.activator, .wizlet.wizletCollapsible .card .card-content .card-title.reveal {
  position: relative;
}
.wizlet.wizletCollapsible .card .card-content .card-title.activator i.material-icons.right, .wizlet.wizletCollapsible .card .card-content .card-title.reveal i.material-icons.right {
  position: absolute;
  top: 0;
  right: -5px;
}
.wizlet.wizletCollapsible .card .collapsible {
  border-radius: 10px;
  margin-bottom: 3rem;
}
.wizlet.wizletCollapsible .card .collapsible li {
  border-radius: 10px;
  margin: 0;
  padding: 0;
  background-color: #FFFFFF;
}
.wizlet.wizletCollapsible .card .collapsible li .collapsible-header {
  border-radius: 10px;
  background: #e1958e;
  background: -webkit-gradient(linear, left top, right bottom, color-stop(0%, #cc4a3d), color-stop(100%, #e1958e));
  background: -webkit-linear-gradient(to right, #cc4a3d 0%, #e1958e 100%);
  background: -o-linear-gradient(to right, #cc4a3d 0%, #e1958e 100%);
  background: ms-linear-gradient(to right, #cc4a3d 0%, #e1958e 100%);
  background: -webkit-gradient(linear, left top, right top, from(#cc4a3d), to(#e1958e));
  background: -webkit-linear-gradient(left, #cc4a3d 0%, #e1958e 100%);
  background: -o-linear-gradient(left, #cc4a3d 0%, #e1958e 100%);
  background: linear-gradient(to right, #cc4a3d 0%, #e1958e 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#cc4a3d, endColorstr=#e1958e);
  color: #ffffff;
}
.wizlet.wizletCollapsible .card .collapsible li .collapsible-body {
  border-radius: 10px;
}