<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutLanding">
  <Include name="Header_Intro"></Include>

  <Component type="Vanilla"><![CDATA[{
    templateInEvent: "html/landingPage.dot",
    css: "styles/landingPage.css",
    fullScreen: true,
    content: {
      title: "!{}",
      subtitle: "!{}",
      image: "!{LandingPage_Img0}",
      firstIsLogo: false,
      images: ["!{LandingPage_Img1}","!{LandingPage_Img2}","!{LandingPage_Img3}", "!{LandingPage_Img4}",
               "!{LandingPage_Img5}","!{LandingPage_Img6}" ]
    }
  }]]></Component>

  <!-- Reset all participant's votes when landing -->
  <Component type="myCode_reset" customJS="true"><![CDATA[{
      clearAll: true,
      resetVotes: false
  }]]></Component> 



  <Voting autoNext="false">
    <Score type="TimeStamp" result="Q_LOGIN_DATE_R2" keepFirst="true"/> 
  </Voting>


</Action>