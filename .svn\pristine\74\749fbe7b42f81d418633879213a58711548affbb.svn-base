<?xml version="1.0" encoding="utf-8" ?>
<Action layout="../../../layout/tabsLayoutModalNav">
  
  <!-- Tabs component -->
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{}",
      swipeable: false,
      tabPanelID: "tabPanelModalNav",
      tabs: [
          "!{GroupDirector_Tab1}",
          "!{GroupDirector_Tab2}",
          "!{GroupDirector_Tab3}",
          "!{GroupDirector_Tab4}",
          "!{GroupDirector_Tab5}",
          "!{GroupDirector_Tab9}"
      ],
      _activeTab: 1,
      activeTab: "Q_FAC_Navigation_Tab",
      activeTabByVal: false,
      scope: ["Q_FAC_Navigation_Tab"]
  }]]></Component>


  <!-- ROUND 1 -->
  <Component type="ActionMenu" customJS="true"><![CDATA[{
      templateInEvent: "html/collection.dot",
      css: "styles/collection.css",
      header: "!{}",
      intro: "!{GroupDirector_Text1}",
      bgColor: "primary _margin-right",
      highlightActiveScreen: true,
      items: [
        {
          gdActionTrack: "GD",
          gdActionSection: "R1_LandingPage",
          targetSection: "R1_LandingPage_FAC",
          label: "!{GD_LandingPage}",
          icon: "screen_share"
        },
        {
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R1_Start",
          targetSection: "SIM_R1_FAC_dashboard",
          label: "!{GD_SIM_R1_Start}",
          icon: "looks_one"
        },        
        {
          gdActionCommand: "EmbedWizlet",
          gdActionCommandXML: "SIM_R1_Finish_BackDirector2",
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          _gdActionSection: "R1_DebriefPage",
          targetSection: "R1_DebriefPage_FAC",
          label: "!{GD_DebriefPage}",
          icon: "insert_chart"
        }
      ],
      scope: [  ]
  }]]></Component>

  <!-- ROUND 2 -->
  <Component type="ActionMenu" customJS="true"><![CDATA[{
      templateInEvent: "html/collection.dot",
      css: "styles/collection.css",
      header: "!{}",
      intro: "!{GroupDirector_Text2}",
      bgColor: "primary _margin-right",
      highlightActiveScreen: true,
      items: [
        {
          gdActionTrack: "GD",
          gdActionSection: "R2_LandingPage",
          targetSection: "R2_LandingPage_FAC",
          label: "!{GD_LandingPage}",
          icon: "screen_share"
        },
        {
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R2_Start",
          targetSection: "SIM_R2_FAC_dashboard",
          label: "!{GD_SIM_R2_Start}",
          icon: "looks_two"
        },        
        {
          gdActionCommand: "EmbedWizlet",
          gdActionCommandXML: "SIM_R2_Finish_BackDirector2",
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          _gdActionSection: "R2_DebriefPage",
          targetSection: "R2_DebriefPage_FAC",
          label: "!{GD_DebriefPage}",
          icon: "insert_chart"
        }
      ],
      scope: [  ]
  }]]></Component>

  <!-- ROUND 3 -->
  <Component type="ActionMenu" customJS="true"><![CDATA[{
      templateInEvent: "html/collection.dot",
      css: "styles/collection.css",
      header: "!{}",
      intro: "!{GroupDirector_Text3}",
      bgColor: "primary _margin-right",
      highlightActiveScreen: true,
      items: [
        {
          gdActionTrack: "GD",
          gdActionSection: "R3_LandingPage",
          targetSection: "R3_LandingPage_FAC",
          label: "!{GD_LandingPage}",
          icon: "screen_share"
        },
        {
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R3_Start",
          targetSection: "SIM_R3_FAC_dashboard",
          label: "!{GD_SIM_R3_Start}",
          icon: "looks_3"
        },        
        {
          gdActionCommand: "EmbedWizlet",
          gdActionCommandXML: "SIM_R3_Finish_BackDirector2",
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          _gdActionSection: "R3_DebriefPage",
          targetSection: "R3_DebriefPage_FAC",
          label: "!{GD_DebriefPage}",
          icon: "insert_chart"
        }
      ],
      scope: [  ]
  }]]></Component>

  <!-- ROUND 4 -->
  <Component type="ActionMenu" customJS="true"><![CDATA[{
      templateInEvent: "html/collection.dot",
      css: "styles/collection.css",
      header: "!{}",
      intro: "!{GroupDirector_Text4}",
      bgColor: "primary _margin-right",
      highlightActiveScreen: true,
      items: [
        {
          gdActionTrack: "GD",
          gdActionSection: "R4_LandingPage",
          targetSection: "R4_LandingPage_FAC",
          label: "!{GD_LandingPage}",
          icon: "screen_share"
        },
        {
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R4_Start",
          targetSection: "SIM_R4_FAC_dashboard",
          label: "!{GD_SIM_R4_Start}",
          icon: "looks_4"
        },
        {
          gdActionCommand: "EmbedWizlet",
          gdActionCommandXML: "SIM_R4_Finish_BackDirector2",
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          _gdActionSection: "R4_DebriefPage",
          targetSection: "R4_DebriefPage_FAC",
          label: "!{GD_DebriefPage}",
          icon: "insert_chart"
        }
      ],
      scope: [  ]
  }]]></Component>

  <!-- ROUND 5 -->
  <Component type="ActionMenu" customJS="true"><![CDATA[{
      templateInEvent: "html/collection.dot",
      css: "styles/collection.css",
      header: "!{}",
      intro: "!{GroupDirector_Text5}",
      bgColor: "primary _margin-right",
      highlightActiveScreen: true,
      items: [
        {
          gdActionTrack: "GD",
          gdActionSection: "R5_LandingPage",
          targetSection: "R5_LandingPage_FAC",
          label: "!{GD_LandingPage}",
          icon: "screen_share"
        },
        {
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "SIM_R5_Start",
          targetSection: "SIM_R5_FAC_dashboard",
          label: "!{GD_SIM_R5_Start}",
          icon: "looks_5"
        },
        {
          gdActionCommand: "EmbedWizlet",
          gdActionCommandXML: "SIM_R5_Finish_BackDirector2",
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          _gdActionSection: "R5_DebriefPage",
          targetSection: "R5_DebriefPage_FAC",
          label: "!{GD_DebriefPage}",
          icon: "insert_chart"
        }
      ],
      scope: [  ]
  }]]></Component>



  <!-- MODULE EXTRA -->
  <Component type="ActionMenu" customJS="true"><![CDATA[{
      templateInEvent: "html/collection.dot",
      css: "styles/collection.css",
      header: "!{}",
      intro: "!{GroupDirector_Text9}",
      bgColor: "primary _margin-right",
      items: [
        {
          gdActionTrack: "GD",
          gdActionSection: "Extra_YesNoQuestion1_Radio",
          targetSection: "Extra_YesNoQuestion1_Piechart_FAC",
          label: "!{GD_Extra_YesNoQuestion1_Radio}",
          icon: "thumbs_up_down"
        } ,
        {
          gdActionTrack: "GD",
          gdActionSection: "Extra_YesNoQuestion2_Radio",
          targetSection: "Extra_YesNoQuestion2_Piechart_FAC",
          label: "!{GD_Extra_YesNoQuestion2_Radio}",
          icon: "thumbs_up_down"
        } ,
        {
          gdActionTrack: "GD",
          gdActionSection: "Extra_YesNoQuestion3_Radio",
          targetSection: "Extra_YesNoQuestion3_Piechart_FAC",
          label: "!{GD_Extra_YesNoQuestion3_Radio}",
          icon: "thumbs_up_down"
        } ,
        {
          gdActionTrack: "GD",
          gdActionSection: "Extra_YesNoQuestion4_Radio",
          targetSection: "Extra_YesNoQuestion4_Piechart_FAC",
          label: "!{GD_Extra_YesNoQuestion4_Radio}",
          icon: "thumbs_up_down"
        } ,
        {
          gdActionTrack: "GD",
          gdActionSection: "Extra_YesNoQuestion5_Radio",
          targetSection: "Extra_YesNoQuestion5_Piechart_FAC",
          label: "!{GD_Extra_YesNoQuestion5_Radio}",
          icon: "thumbs_up_down"
        } ,
        {
          gdActionTrack: "GD",
          gdActionSection: "Extra_OpenQuestion1_Input",
          targetSection: "Extra_OpenQuestion1_Answers_FAC",
          label: "!{GD_Extra_OpenQuestion1_Input}",
          icon: "chat"
        } ,
        {
          gdActionTrack: "GD",
          gdActionSection: "Extra_OpenQuestion2_Input",
          targetSection: "Extra_OpenQuestion2_Answers_FAC",
          label: "!{GD_Extra_OpenQuestion2_Input}",
          icon: "chat"
        } ,
        {
          gdActionTrack: "GD",
          gdActionSection: "Extra_OpenQuestion3_Input",
          targetSection: "Extra_OpenQuestion3_Answers_FAC",
          label: "!{GD_Extra_OpenQuestion3_Input}",
          icon: "chat"
        } ,
        {
          gdActionTrack: "GD",
          gdActionSection: "Extra_OpenQuestion4_Input",
          targetSection: "Extra_OpenQuestion4_Answers_FAC",
          label: "!{GD_Extra_OpenQuestion4_Input}",
          icon: "chat"
        } ,
        {
          gdActionTrack: "GD",
          gdActionSection: "Extra_OpenQuestion5_Input",
          targetSection: "Extra_OpenQuestion5_Answers_FAC",
          label: "!{GD_Extra_OpenQuestion5_Input}",
          icon: "chat"
        }
      ],
      scope: [  ]
  }]]></Component>




</Action>
