﻿/* 
    "HCPieChart" component that takes a template and some data as the input
    and renders the chart using highcharts library.
*/

define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', 'highcharts-styled', 'css!lib/highcharts/code/css/highcharts.css', 'numeral', 'jsCalcLib/numberFormatting'], 
        function ($, Q, WizerApi, WizletBase, doT, Highcharts,  HighchartsCss, numeral, numberFormatting) {

    var HCPieChart = function () {
        this.type = 'HCPieChart';
        this.level = 1;
        this.chartDefaults = {
            "lang": {
                "thousandsSep": ",",
                "numericSymbols": ['k', 'm', 'b', 't']
            },
            "chart": {
                "type": 'pie'
            },
            "credits": {
                "enabled": false
            },
            "legend":{ },
            "title": {
                "text": ''
            },
           
            "tooltip": {
                "headerFormat": '<b>{point.x}</b><br/>',
                "pointFormat": '{series.name}: {point.y}'
            },
            "plotOptions": {
                "column": {
                    "dataLabels": {
                        "enabled": true
                    }
                }
            }
        }
    };

    HCPieChart.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        

        this.templateDefer = new Q.defer();
        var self = this;
        var requirements = [];
        requirements.push(WizletBase.loadTemplate(wizletInfo, 'HCPieChart.dot'));

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });
        
        // requirejs.config({
        //     'paths': {
        //         'pattern-fill': 'lib/highcharts/code/js/modules/pattern-fill'
        //     },
        //     'shim': {
        //         'pattern-fill': { deps: ['highcharts-styled'], exports: 'Highcharts' }
        //     }
        // });
        // require(['pattern-fill'], function(fill){
        //     Highcharts = fill;
        // });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    HCPieChart.prototype.unloadHandler = function () {
        //unload wizletbase
        WizletBase.unloadHandler({ wizlet: this });
        $(document).off("wizer:model:change", this.redrawChart);
    };

    HCPieChart.prototype.render = function (options) {
        var self = this;
		var fetching = new Q.defer();
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
            self.isDelivery = self.wizletInfo.isDelivery || false;
            
            self.renderChart().then(function () {
                fetching.resolve(true);
                // add redraw binder
                if (!self.wizletInfo.id) self.wizletInfo.id = 'pieChart0';
                if (self.wizletInfo.listenModel)
                    $(document).off("wizer:model:change."+self.wizletInfo.id, self.redrawChart).
                                on("wizer:model:change."+self.wizletInfo.id, {self: self}, self.redrawChart);
            });
        })
        .fail(this.wizerApi.showError);
    };

    HCPieChart.prototype.renderChart = function() {
        var self = this;
        var rendering = new Q.defer();

        var chartOptions = self.wizletInfo.chartConfig;
        var chartElem = self.wizletContext.find("[data-piechartholder]");
        chartOptions = $.extend(true, {}, self.chartDefaults, chartOptions);

        if (chartOptions.plotOptions.pie.dataLabels.iconsFormat) {
            var format=chartOptions.plotOptions.pie.dataLabels.iconsFormat;
            chartOptions.plotOptions.pie.dataLabels.formatter = function () {
                
                if (this.point.icon)
                    return '<img src="Wizer/Pages/Events/' + wizerApi.eventName() + '/' + this.point.icon+ '" '+ (this.point.style ? 'style="'+this.point.style : '') + '"><span>' + this.point.y + (format=="percentage"?" %":"") + '</span>'
                else
                    return '<span>' + this.point[format] + (format=="percentage"?" %":"") + '</span>'
            }
        }

        
        if (typeof self.wizletInfo.questions !== "undefined" && self.wizletInfo.questions.length) {
            
            //Show answers distribution for a one question 
            if (self.wizletInfo.isAnswersDistribution) {
                
                if (self.wizletInfo.isCountingIfChecked) {
                    self.calculateCombinedDistribution().then(function(data){
                        self.renderChartData.call(self, data, chartOptions, chartElem);
                        rendering.resolve(true);
                    });

                } else if (self.wizletInfo.isCountingAllCombined) {
                        self.calculateAllCombined().then(function(data){
                            self.renderChartData.call(self, data, chartOptions, chartElem);
                            rendering.resolve(true);
                        });
    
                } else {
                    self.calculateDistribution().then(function(data){
                        self.renderChartData.call(self, data, chartOptions, chartElem);
                        rendering.resolve(true);
                    });

                }


            //Show votes values per each question
            } else {

                self.fetchVotes().then(function(data){
                    self.renderChartData.call(self, data, chartOptions, chartElem);
                    rendering.resolve(true);
                });
            }


        }
        else {
            //if no questions, direct values given in the XML by the series
            chartElem.highcharts(chartOptions);
            rendering.resolve(true);
        }
		return rendering.promise;
    };



    HCPieChart.prototype.calculateDistribution = function() {
        var self = this;
		
        var trackDiffer = new Q.defer();

        if (self.wizletInfo.trackQuestion) {
            //fetch the trackQuestionId and my vote on that trackQuestion. This then becomes the new filterQuestionId and filterText
            var trackQuestionId = self.wizerApi.getQuestionIdByName(self.wizletInfo.trackQuestion);
            var gettingMyVoteOnTrack = self.wizerApi.getMyVotes([trackQuestionId]);
            gettingMyVoteOnTrack.then(function (votes) {
                self.myVoteonTrack = votes.votes[trackQuestionId];
                if (self.myVoteonTrack) {
                    trackDiffer.resolve(true);
                }
                else {
                    trackDiffer.resolve(true);
                    Wizer.Api.showError("No Votes of this user on trackQuestion: " + self.wizletInfo.trackQuestion);
                }
            });

        }
        else {
            trackDiffer.resolve(true);
        }


        return trackDiffer.promise.then(function () {

            var questName = self.wizletInfo.questions[0].binding;
            var qId = self.wizerApi.getQuestionIdByName(questName);
            var waiting = self.wizerApi.getAverageVotes({
                shortNames: questName,
                filterQuestionId: self.wizletInfo.trackQuestion ? trackQuestionId : qId,  
                filterText: self.wizletInfo.trackQuestion ? self.myVoteonTrack[0]  : '',
                showAverage: true,
                showVoteCount: true,
                seed: Math.random().toString().replace(".", ""),
                isDelivery: null
            });

            return waiting.then(function (result) {  
                return self.processAverageVotes(result.questionResults[0]);;
            });
        });

        

    }

    HCPieChart.prototype.calculateAllCombined = function() {
        var self = this;
		
        var trackDiffer = new Q.defer();

        
        if (self.wizletInfo.trackQuestion) {
            //fetch the trackQuestionId and my vote on that trackQuestion. This then becomes the new filterQuestionId and filterText
            var trackQuestionId = self.wizerApi.getQuestionIdByName(self.wizletInfo.trackQuestion);
            var gettingMyVoteOnTrack = self.wizerApi.getMyVotes([trackQuestionId]);
            gettingMyVoteOnTrack.then(function (votes) {
                self.myVoteonTrack = votes.votes[trackQuestionId];
                if (self.myVoteonTrack) {
                    trackDiffer.resolve(true);
                }
                else {
                    trackDiffer.resolve(true);
                    Wizer.Api.showError("No Votes of this user on trackQuestion: " + self.wizletInfo.trackQuestion);
                }
            });

        }
        else {
            trackDiffer.resolve(true);
        }

        

        return trackDiffer.promise.then(function () {

            
            var questIds = [];
            self.wizletInfo.questions.forEach (function (question, idx) {
                questIds.push( self.wizerApi.getQuestionIdByName(question.binding) );
            });
             
            var waiting = self.wizerApi.getVotes({
                questionIds: questIds
            });

            return waiting.then(function (result) {  
                //self.wizletContext.find('.answers span.votes').html(result.totalVoteCount);
                return self.processAverageVotesRecountPerValue(result.votes);;
            });
        });


    }



    HCPieChart.prototype.calculateCombinedDistribution = function() {
        var self = this;
		
        var trackDiffer = new Q.defer();

        if (self.wizletInfo.trackQuestion) {
            //fetch the trackQuestionId and my vote on that trackQuestion. This then becomes the new filterQuestionId and filterText
            var trackQuestionId = self.wizerApi.getQuestionIdByName(self.wizletInfo.trackQuestion);
            var gettingMyVoteOnTrack = self.wizerApi.getMyVotes([trackQuestionId]);
            gettingMyVoteOnTrack.then(function (votes) {
                self.myVoteonTrack = votes.votes[trackQuestionId];
                if (self.myVoteonTrack) {
                    trackDiffer.resolve(true);
                }
                else {
                    trackDiffer.resolve(true);
                    Wizer.Api.showError("No Votes of this user on trackQuestion: " + self.wizletInfo.trackQuestion);
                }
            });

        }
        else {
            trackDiffer.resolve(true);
        }


        return trackDiffer.promise.then(function () {

            var questName = [];
            self.wizletInfo.questions.forEach (function (question, idx) {
                questName.push(question.binding);
            });
            
            var waiting = self.wizerApi.getVotePercentage({
                shortNames: questName,
                filterQuestionId: self.wizletInfo.trackQuestion ? trackQuestionId : null,  
                filterText: self.wizletInfo.trackQuestion ? self.myVoteonTrack[0]  : '',
                seed: Math.random().toString().replace(".", ""),
                isDelivery: null
            });

            return waiting.then(function (result) {  
                self.wizletContext.find('.answers span.votes').html(result.totalVoteCount);
                return self.processAverageVotesRecount(result.votes[0]);;
            });
        });

        

    }


    HCPieChart.prototype.processAverageVotes = function(votes) {
        
        var retVal = {}, series = [], self = this;
        
        
        self.wizletContext.find('.answers span.votes').html(votes.count);
        
        votes.list.forEach (function (vote, idx) {
            //if (vote.value>0) {
                series.push({y: vote.totalVotes});
            //}
        });

                    
        retVal.series = [{
                data: series
            }];
        
        return retVal;
    }



    HCPieChart.prototype.processAverageVotesRecount = function(votes) {
        
        var retVal = {}, series = [], self = this;
        
        var checked=0, unchecked=0;
        votes.list.forEach (function (vote, idx) {
            
            if (vote.totalVotes>0) {
                checked++;
            } else {
                unchecked++;
            }
            
        });

        series.push({
            y: checked
        });
        series.push({
            y: unchecked
        });

        retVal.series = [{
                data: series
            }];
        
        return retVal;
    }


    HCPieChart.prototype.processAverageVotesRecountPerValue = function(votes) {
        
        var retVal = {}, series = [], self = this;
        var values=[], value;
        votes.forEach (function (vote, idx) {
            value = parseInt(vote.responseText);
            if (vote.responseText && value>0)
                if (values[value])
                    values[value] ++;
                else {
                    values[value]=1;
                }
            
        });
        
        values.forEach (function (vote, idx) {
            if (vote)
                series.push({
                    y: vote
                });
            
        });
        
        retVal.series = [{
            data: series
        }];

        return retVal;
    }



    HCPieChart.prototype.fetchVotes = function() {
        var self = this;
        var questions = [];
        self.wizletInfo.questions.forEach(function(q) {
            questions.push(q.binding);
        });
		

        return self.wizerApi.getVotesByQuestionName(questions, self.wizletInfo.trackQuestion, self.wizletInfo.sorting, null, self.isDelivery).then(function(response){
            return self.processVotes(response);
        });

    }

    HCPieChart.prototype.processVotes = function(votes) {
        
        var retVal = {}, series = [], self = this;
        for (var i = 0; i < self.wizletInfo.questions.length; i++) {
            const q = self.wizletInfo.questions[i];
            
			var val;
            
            const p = votes.participants[0];
            if(p.questionMap && p.questionMap[q.binding] && p.questionMap[q.binding].value){
                val = numeral(p.questionMap[q.binding].value).value();
            }
            else{
                val = "";
            }
                        
            series.push({y: val});
        }

        retVal.series = [{
                data: series
            }];
        
        return retVal;
    }

    HCPieChart.prototype.renderChartData = function(data, chartOptions, chartElem) {

        chartOptions.series[0].data = $.extend(true, [], chartOptions.series[0].data, data.series[0].data);
        this.applyNumberFormat(chartOptions);
        this.formatTooltips(chartOptions);
        chartElem.highcharts(chartOptions);
				
    };









    HCPieChart.prototype.formatTooltips = function(chartOptions) {
        var formatters = discover(chartOptions, 'numformat');

        formatters.forEach(function(ob) {
            var format = ob.numformat,
                scaler = ob.scaler,
                formatFunction = (function(){
                    return function() {
                        var val = 0, point = this, out = '<strong>' + point.series.name + '</strong><br />';
                        out += '' + point.x + ': ';
                        if (typeof this.value !== "undefined") {
                            val = this.value;
                        }
                        else if (typeof this.y !== "undefined") {
                            val = this.y;
                        }
                        else if (typeof this.x !== "undefined") {
                            val = this.x;
                        }
                        out += numberFormatting.format(val, format, scaler);
                        return out;
                    }
                }());

            ob.formatter = formatFunction;
        });
    }

    HCPieChart.prototype.applyNumberFormat = function(chartOptions) {
        var formatters = discover(chartOptions, 'formatter');

        formatters.forEach(function(ob) {
            var format = ob.formatter,
                scaler = ob.scaler,
                formatFunction = (function(){
                    return function() {
                        var val = 0;
                        if (typeof this.value !== "undefined") {
                            val = this.value;
                        }
                        else if (typeof this.y !== "undefined") {
                            val = this.y;
                        }
                        else if (typeof this.x !== "undefined") {
                            val = this.x;
                        }
                        return numberFormatting.format(val, format, scaler);
                    }
                }());

            ob.formatter = formatFunction;
        });
    }

    HCPieChart.prototype.redrawChart = function(e) {
        var self = e.data.self;
        var redraw = new Q.defer();
        var chartOptions = self.wizletInfo.chartConfig;
        var chartElem = self.wizletContext.find("[data-piechartholder]");
        chartOptions = $.extend(true, {}, self.chartDefaults, chartOptions);

        
        if (typeof self.wizletInfo.questions !== "undefined" && self.wizletInfo.questions.length) {
            
            //Show answers distribution for a one question 
            if (self.wizletInfo.isAnswersDistribution) {
                
                if (self.wizletInfo.isCountingIfChecked) {
                    self.calculateCombinedDistribution().then(function(data){
                        self.renderChartData.call(self, data, chartOptions, chartElem);
                        redraw.resolve(true);
                    });

                } else if (self.wizletInfo.isCountingAllCombined) {
                        self.calculateAllCombined().then(function(data){
                            self.renderChartData.call(self, data, chartOptions, chartElem);
                            redraw.resolve(true);
                        });
    
                } else {
                    self.calculateDistribution().then(function(data){
                        self.renderChartData.call(self, data, chartOptions, chartElem);
                        redraw.resolve(true);
                    });

                }


            //Show votes values per each question
            } else {

                self.fetchVotes().then(function(data){
                    self.renderChartData.call(self, data, chartOptions, chartElem);
                    redraw.resolve(true);
                });
            }


        }
        else {
            redraw.resolve(true);
        }
        return redraw.promise;
    };

    HCPieChart.getRegistration = function () {
        return new HCPieChart();
    };


    function search(tree, propertyName, result) {
        if ($.isArray(tree)) {
            for (var i = 0; i < tree.length; ++i) {
                search(tree[i], propertyName, result);
            }
        } else if ($.isPlainObject(tree)) {
            for (var pName in tree) {
                if (tree.hasOwnProperty(pName)) {
                    var subTree = tree[pName];
                    if (pName == propertyName) {
                        result.push(tree);
                    } else {
                        search(subTree, propertyName, result);
                    }
                }
            }
        }
    };

    function discover(src, propertyName) { 
        var propertyName = propertyName || 'formatter';
        var formatters = [];
        search(src, propertyName, formatters);
        return formatters;
    };

	
	
    return HCPieChart;

});
