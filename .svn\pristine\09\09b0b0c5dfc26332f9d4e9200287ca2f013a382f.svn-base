.wizlet.wizletHCBarChart .header.with-label {
  position: relative;
  padding-left: 2.5rem;
  margin-bottom: 1rem;
}
.wizlet.wizletHCBarChart .header.with-label .badge.header-label {
  position: absolute;
  left: 0;
  top: 0;
  background-color: #cc4a3d;
  min-width: 2rem;
  min-height: 2rem;
  font-size: 1.25rem;
  font-weight: bold;
  border-radius: 50%;
  line-height: 2rem;
  margin: 0;
  margin-top: -5px;
}
.wizlet.wizletHCBarChart .card .card-content {
  background-color: #FFFFFF;
  padding: 6px 12px;
}
.wizlet.wizletHCBarChart .card .card-content .row {
  margin: 0;
}
.wizlet.wizletHCBarChart .card .card-content .bach-content-stackedColumnChart--container {
  height: auto;
}
.wizlet.wizletHCBarChart .highcharts-container > svg {
  width: 100% !important;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-background {
  fill: transparent;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-title {
  font-size: 1.25rem;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-subtitle {
  font-size: 1rem;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-label,
.wizlet.wizletHCBarChart .highcharts-container .highcharts-axis-labels {
  color: initial;
  fill: initial;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-label > text, .wizlet.wizletHCBarChart .highcharts-container .highcharts-label > span,
.wizlet.wizletHCBarChart .highcharts-container .highcharts-axis-labels > text,
.wizlet.wizletHCBarChart .highcharts-container .highcharts-axis-labels > span {
  font-size: 1rem;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-label > text:last-child:not([transform]), .wizlet.wizletHCBarChart .highcharts-container .highcharts-label > span:last-child:not([transform]),
.wizlet.wizletHCBarChart .highcharts-container .highcharts-axis-labels > text:last-child:not([transform]),
.wizlet.wizletHCBarChart .highcharts-container .highcharts-axis-labels > span:last-child:not([transform]) {
  -webkit-transform: translateX(-4px);
      -ms-transform: translateX(-4px);
       -o-transform: translateX(-4px);
          transform: translateX(-4px);
  -webkit-transform: translateY(2px);
      -ms-transform: translateY(2px);
       -o-transform: translateY(2px);
          transform: translateY(2px);
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-data-label span {
  color: #ffffff;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-data-label text {
  fill: #ffffff;
  text-shadow: 1px 1px #24466b;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-axis.highcharts-xaxis .highcharts-axis-title tspan {
  font-weight: bold;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-color-0 {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-color-1 {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-color-2 {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-color-3 {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-color-4 {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-color-5 {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-color-6 {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-color-7 {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-color-8 {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-color-9 {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-color-10 {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-color-11 {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(0) {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(1) {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(2) {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(3) {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(4) {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(5) {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(6) {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(7) {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(8) {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(9) {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(10) {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(11) {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-tooltip .highcharts-tooltip-box {
  opacity: 1;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-tooltip table tr, .wizlet.wizletHCBarChart .highcharts-container .highcharts-tooltip table td {
  padding: 0;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-series-group .highcharts-spline-series {
  fill: #cc4a3d;
  stroke: #cc4a3d;
}
.wizlet.wizletHCBarChart .highcharts-container .highcharts-spline-series .highcharts-label > text, .wizlet.wizletHCBarChart .highcharts-container .highcharts-spline-series .highcharts-label > span {
  font-size: 0.8rem;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(1) {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(2) {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(3) {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(4) {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(5) {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(6) {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(7) {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(8) {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(9) {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(10) {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(11) {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container:not(.stacked) .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(12) {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(1),
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(1) {
  fill: #2AD4C3;
  stroke: #2AD4C3;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(2),
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(2) {
  fill: #2D9ED6;
  stroke: #2D9ED6;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(3),
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(3) {
  fill: #2E79E2;
  stroke: #2E79E2;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(4),
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(4) {
  fill: #2E57ED;
  stroke: #2E57ED;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(5),
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(5) {
  fill: #2F35F8;
  stroke: #2F35F8;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(6),
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(6) {
  fill: #1823F4;
  stroke: #1823F4;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(7),
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(7) {
  fill: #2AD4C3;
  stroke: #2AD4C3;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(8),
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(8) {
  fill: #2D9ED6;
  stroke: #2D9ED6;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(9),
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(9) {
  fill: #2E79E2;
  stroke: #2E79E2;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(10),
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(10) {
  fill: #2E57ED;
  stroke: #2E57ED;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(11),
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(11) {
  fill: #2F35F8;
  stroke: #2F35F8;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-bar-series:not(.highcharts-data-labels) rect:nth-child(12),
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.chartaux .highcharts-container .highcharts-column-series:not(.highcharts-data-labels) rect:nth-child(12) {
  fill: #1823F4;
  stroke: #1823F4;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.farclose .highcharts-container .highcharts-color-0,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.farclose .highcharts-container .highcharts-color-0 > .highcharts-point {
  fill: #fb054b;
  stroke: #fb054b;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.farclose .highcharts-container .highcharts-color-1,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.farclose .highcharts-container .highcharts-color-1 > .highcharts-point {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.farclose .highcharts-container .highcharts-color-2,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.farclose .highcharts-container .highcharts-color-2 > .highcharts-point {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.noyes .highcharts-container .highcharts-color-0,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.noyes .highcharts-container .highcharts-color-0 > .highcharts-point {
  fill: #fb054b;
  stroke: #fb054b;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.noyes .highcharts-container .highcharts-color-1,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.noyes .highcharts-container .highcharts-color-1 > .highcharts-point {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.yesno .highcharts-container .highcharts-color-0,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.yesno .highcharts-container .highcharts-color-0 > .highcharts-point {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.yesno .highcharts-container .highcharts-color-1,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.yesno .highcharts-container .highcharts-color-1 > .highcharts-point {
  fill: #fb054b;
  stroke: #fb054b;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.categories .highcharts-container .highcharts-color-0,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.categories .highcharts-container .highcharts-color-0 > .highcharts-point {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.categories .highcharts-container .highcharts-color-1,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.categories .highcharts-container .highcharts-color-1 > .highcharts-point {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.categories .highcharts-container .highcharts-color-2,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.categories .highcharts-container .highcharts-color-2 > .highcharts-point {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.categories .highcharts-container .highcharts-color-3,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.categories .highcharts-container .highcharts-color-3 > .highcharts-point {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.categories .highcharts-container .highcharts-color-4,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.categories .highcharts-container .highcharts-color-4 > .highcharts-point {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.categories .highcharts-container .highcharts-color-5,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.categories .highcharts-container .highcharts-color-5 > .highcharts-point {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.categories .highcharts-container .highcharts-color-6,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.categories .highcharts-container .highcharts-color-6 > .highcharts-point {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.aux .highcharts-container .highcharts-color-0,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.aux .highcharts-container .highcharts-color-0 > .highcharts-point {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.aux .highcharts-container .highcharts-color-1,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.aux .highcharts-container .highcharts-color-1 > .highcharts-point {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.aux .highcharts-container .highcharts-color-2,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.aux .highcharts-container .highcharts-color-2 > .highcharts-point {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.aux .highcharts-container .highcharts-color-3,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.aux .highcharts-container .highcharts-color-3 > .highcharts-point {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.aux .highcharts-container .highcharts-color-4,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.aux .highcharts-container .highcharts-color-4 > .highcharts-point {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.aux .highcharts-container .highcharts-color-5,
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.stacked.aux .highcharts-container .highcharts-color-5 > .highcharts-point {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.line-dashed .highcharts-spline-series .highcharts-graph {
  stroke-dasharray: 1em;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.noclickablelegend .highcharts-legend {
  pointer-events: none;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.hide-legend.legend1 .highcharts-legend .highcharts-legend-item:nth-child(1) {
  display: none;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.hide-legend.legend1 .highcharts-legend .highcharts-legend-item:nth-child(2) {
  -webkit-transform: translateX(50px);
      -ms-transform: translateX(50px);
       -o-transform: translateX(50px);
          transform: translateX(50px);
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.color.categoria1 .highcharts-color-0 {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.color.categoria2 .highcharts-color-0 {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.color.categoria3 .highcharts-color-0 {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.color.categoria4 .highcharts-color-0 {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCBarChart .bach-content-stackedColumnChart--container.coloured .highcharts-background {
  fill: rgba(36, 70, 107, 0.2);
}
.wizlet.wizletHCBarChart .answers.card-panel {
  padding: 8px 12px;
}
.wizlet.wizletHCBarChart .answers.card-panel .btn-text {
  display: inline-block;
  vertical-align: sub;
}