﻿define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerApi, WizletBase, doT) {

    var CheckBoxes = function () {
        this.type = 'CheckBoxes';
        this.level = 1;
        this.choices = 0;
        this.nCorrects = 0;
    };
  
    CheckBoxes.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
  
        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        
  
        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });
  
        return WizletBase.loadHandler({ wizlet: this, render: this.render });
  
    };
  
    CheckBoxes.prototype.unloadHandler = function () {
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        $(document).off("wizer:model:change", this.optionSelected);
        WizletBase.unloadHandler({ wizlet: this });
    };
  
    CheckBoxes.prototype.render = function (options) {
        var self = this;
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
                
            //Init Materialize Modal
            if (options.wizletInfo.help && options.wizletInfo.help.modalID)
                options.context.find('.modal').modal();
  
  
            if ((options.wizletInfo.submitOnChange && !options.wizletInfo.preloadSaved))
                self.init(options.context.find('input[type="checkbox"]'));
  
            
            //Check if already played
            if (options.wizletInfo.preloadSaved)
            if (!options.wizletInfo.submitOnChange) {
                self.getVote(options.wizletInfo.saved).then(function(value) { 
                        
                    //if ( options.wizletInfo.id && (localStorage.getItem( options.wizletInfo.id ) == 1)) {
                    if (value) {  
                        var questionIds = [];
                        var opts = options.wizletInfo.options;
                        var bind;
                        $.each(opts, function (idx, option) {
                            bind = option.bind || option.binding;
                            questionIds.push( self.wizerApi.getQuestionIdByName(bind) );
                        });
                        self.wizerApi.getMyVotes(questionIds).then(function(response) {
                            self.refreshOptions(self, options.context, options.wizletInfo, opts, response.votes);
                            
                        });   
        
                        // options.wizletInfo.minChoices = 0;
                        // options.wizletInfo.maxChoices = 0;
                    } 
  
                });
            } else {
                var questionIds = [];
                var opts = options.wizletInfo.options;
                var bind;
                $.each(opts, function (idx, option) {
                    bind = option.bind || option.binding;
                    questionIds.push( self.wizerApi.getQuestionIdByName(bind) );
                });
                self.wizerApi.getMyVotes(questionIds).then(function(response) {
                    self.refreshOptions(self, options.context, options.wizletInfo, opts, response.votes);
                    
                });   
            }
  
            if (options.wizletInfo.isSolution)
                self.showCorrectOptions(options.context);
  
  
            if (options.wizletInfo.moreInfo)
                options.context.find('ul.collapsible').collapsible({
                    onOpenStart : function(current_item) {
                        $(current_item).find('i.expand').html('expand_less');
                    },
                    
                    onCloseStart : function(current_item) {
                        $(current_item).find('i.expand').html('expand_more');                        
                    }
                });
                
  
  
            //options.context.find('input[type="checkbox"]').on('change', function(e) {
                
            options.context.find('input[type="checkbox"]').closest('li>div').on('click', function(e) {
                
                e.preventDefault();
                var input = $(this).find('input[type="checkbox"]');    
  
                var binding = $(input).data('bind') || $(input).data('binding');
  
                if (! $(input).is(':checked')) {
                                
                    input.prop('checked',true);
  
                    if (self.choices < options.wizletInfo.maxChoices) {
                        $(this).addClass('chosen');
                        self.choices ++;
                        if (self.choices === options.wizletInfo.maxChoices) {
                            options.context.find('input[type="checkbox"]').filter(':not(:checked)').prop('disabled',true).
                                                                                                    closest('li').addClass('disabled');
                            if (options.wizletInfo.maxText) {
                                $('#toast-container .toast').hide()
                                M.toast({
                                    html: options.wizletInfo.maxText,
                                    classes: 'rounded'
                                });
                            }
                        }
  
  
                        if (options.wizletInfo.submitOnChange) {                    
                            self.addVote(binding, 1);      
  
                            if (!options.wizletInfo.saveWhenSubmit) {
                                if (options.wizletInfo.savedWithCounter) {
                                    self.addVote(options.wizletInfo.saved, self.choices);
                                } else {
                                    self.addVote(options.wizletInfo.saved, 1);
                                }
                            }
                            
                            if (self.wizletInfo.saveInModel)
                                $(input).attr('data-value', 1).trigger('updateModelInput');  
                        }
  
                    }
                } else {
  
                    input.prop('checked',false);
  
                    $(this).removeClass('chosen');
                    self.choices --;
  
                    if (options.wizletInfo.submitOnChange) {                    
                        self.addVote(binding, 0);   
  
                        if (!options.wizletInfo.saveWhenSubmit) {
                            if (options.wizletInfo.savedWithCounter) {
                                self.addVote(options.wizletInfo.saved, self.choices);
                            } else {
                                if (self.choices == 0) self.addVote(options.wizletInfo.saved, 0);
                            }
                        }
                            
                        if (self.wizletInfo.saveInModel)
                            $(input).attr('data-value', 0).trigger('updateModelInput');       
                    }
  
                    if (self.choices === options.wizletInfo.maxChoices-1) {
                        options.context.find('input[type="checkbox"]').filter(':not(:checked)').prop('disabled',false).
                                                                                                closest('li').removeClass('disabled');
                    }
                    if (self.choices < options.wizletInfo.minChoices) {
                        //if (! options.wizletInfo.submitOnChange)
                            options.context.find("#checkBtn").prop('hidden',true);
                    }
                }
                
                
                if (self.choices >= options.wizletInfo.minChoices) {
                    //if (! options.wizletInfo.submitOnChange)
                        options.context.find("#checkBtn").removeAttr('hidden');
                }
  
                
            });
  
            
            options.context.find("#checkBtn").on("click", function (e) {
  
                //if (options.wizletInfo.id) localStorage.setItem(options.wizletInfo.id, 1);  
                if (!options.wizletInfo.savedWithLabels) {
                    if (options.wizletInfo.savedWithCounter) {
                        self.addVote(options.wizletInfo.saved, self.choices).then((value) => {
  
                            if (options.wizletInfo.noBlockAnswer) {
                                $(this).removeClass('pulse');
                            } else {
                                $(this).closest('.row').prop('hidden', true);
                                $(this).prop('hidden', true);
                            }
                            
                            var allCorrect = true, someCorrect = false, savedWithLabels='';
                            $.each(options.context.find('[type="checkbox"]'), function (idx, option) {
                                
                                var binding = $(this).data('bind') || $(this).data('binding');
            
                                if (!options.wizletInfo.noBlockAnswer) $(option).closest('li').addClass('disabled');
            
                                if ($(option).is(':checked')) {
            
                                    if (binding) {
            
                                        if (options.wizletInfo.savedWithLabels)
                                            savedWithLabels += $(this).data('label') + '-';
            
                                        //save chosen option
                                        self.addVote(binding, 1);
                                        
                                        if (self.wizletInfo.saveInModel)
                                            $(this).attr('data-value', 1).trigger('updateModelInput');   
            
            
                                        //only if allCorrect
                                        allCorrect = allCorrect && $(this).data('correct');
                                        
                                        someCorrect = someCorrect || $(this).data('correct');
                                        
                                        if ($(this).data('correct')) self.nCorrects++;
                                    }
            
                                } else {
                                    
                                    if (!options.wizletInfo.noBlockAnswer) $(option).prop('disabled',true);
            
                                    //save not-chosen option
                                    self.addVote(binding, 0);
                                        
                                    if (self.wizletInfo.saveInModel)
                                        $(this).attr('data-value', 0).trigger('updateModelInput');  
            
                                    //only if allCorrect
                                    allCorrect = allCorrect && !$(this).data('correct');
            
                                }
                            });
            
                            
                            if (options.wizletInfo.savedWithLabels)
                                self.addVote(options.wizletInfo.saved, savedWithLabels.slice(0,-1));
                       
            
                            if (options.wizletInfo.checkBtn && options.wizletInfo.checkBtn.toast) {
                                $('#toast-container .toast').hide()
                                if (options.wizletInfo.score) {
                                    self.toast (allCorrect,someCorrect,options.wizletInfo.checkBtn.toast,options.wizletInfo.score.points);
                                } else {
                                    M.toast({
                                        html: options.wizletInfo.checkBtn.toast,
                                        classes: 'rounded'
                                    });
                                }
            
                                
                            }
                            if (options.wizletInfo.nChecksForCorrect) {
                                allCorrect = (self.nCorrects == options.wizletInfo.nChecksForCorrect);
                            }
                            if (options.wizletInfo.nChecksForSome) {
                                someCorrect = (self.nCorrects == options.wizletInfo.nChecksForSome);
                            }
            
                            
                            //save score depending on wheteher if it's correct and update total
                            var points;
                            if (options.wizletInfo.score) {
                                var score = options.wizletInfo.score;
                                if (allCorrect) {
                                    points = score.points.ifCorrect;
                                } else {
                                    points = score.points.ifPartial ? 
                                                (someCorrect ? score.points.ifPartial : score.points.ifIncorrect) : 
                                                score.points.ifIncorrect;
                                }
                                self.updateTotal(score.question, points, score.questions, score.total, score.total2, score.totals, score.superTotal);
                            }
            
                            
            
                            options.context.find("i.check-icon").addClass('scale-in').removeClass('scaled-out');
            
                                    
                            //show the hidden component (navigation button)
                            if (options.wizletInfo.checkBtn) {
                                if (allCorrect && options.wizletInfo.checkBtn.idToShowIfCorrect)
                                    $('#'+options.wizletInfo.checkBtn.idToShowIfCorrect).removeAttr('hidden')
                                                                                    .find('a').removeAttr('disabled');
                        
                                if (!allCorrect && someCorrect && options.wizletInfo.checkBtn.idToShowIfSomecorrect)
                                    $('#'+options.wizletInfo.checkBtn.idToShowIfSomecorrect).removeAttr('hidden')
                                                                                .find('a').removeAttr('disabled');  
                            
                                if (!allCorrect && options.wizletInfo.checkBtn.idToShowIfIncorrect)
                                    $('#'+options.wizletInfo.checkBtn.idToShowIfIncorrect).removeAttr('hidden')
                                                                                    .find('a').removeAttr('disabled');        
                                
                                if (options.wizletInfo.checkBtn.idToShow) {
                                    if (allCorrect && options.wizletInfo.checkBtn.newIcon) $('#'+options.wizletInfo.checkBtn.idToShow+' i.material-icons').html(options.wizletInfo.checkBtn.newIcon);
                                    
                                    if (!options.wizletInfo.checkBtn.showIfChecked || options.context.find('input[type="checkbox"][data-bind="'+options.wizletInfo.checkBtn.showIfChecked+'"]').prop('checked'))
                                    $('#'+options.wizletInfo.checkBtn.idToShow).removeAttr('hidden')
                                                                            .find('a:not(.follower)').removeAttr('disabled');
                                
                                    if (options.wizletInfo.checkBtn.idToShow2) {
                                        $('#'+options.wizletInfo.checkBtn.idToShow2).removeAttr('hidden')
                                                                                .find('a:not(.follower)').removeAttr('disabled');
                                    }
                                }
            
                                //MOVES USER TO THE NEXT SCREEN
                                if (options.wizletInfo.checkBtn.idToClick) {
                                    $('#'+options.wizletInfo.checkBtn.idToClick+' a').first().click();
                                }
                                //automatically scroll to the top of the page after confirm
                                if (options.wizletInfo.checkBtn.scrollToTop) {
                                    $('HTML, BODY').animate({ scrollTop: 0 }, 1000);
                                }
                                //automatically scroll to the bottom of the page after confirm
                                if (options.wizletInfo.checkBtn.scrollToDown) {
                                    $('HTML, BODY').animate({ scrollTop: $('body').height() }, 1000);
                                }
                                
            
                            }
            
            
            
                            if (options.wizletInfo.saveInModel && options.wizletInfo.saveWhenSubmit && options.wizletInfo.checkBtn && options.wizletInfo.checkBtn.model)
                                self.wizerApi.calcBinderCache.setCalcValue(options.wizletInfo.checkBtn.model, options.wizletInfo.checkBtn.input, options.wizletInfo.checkBtn.inputval).then(function () {
                                    console.log(options.wizletInfo.checkBtn.input, "updated to "+options.wizletInfo.checkBtn.inputval)
                                });
                        });
  
  
  
                        
                    } else {
                        self.addVote(options.wizletInfo.saved, 1);
                    }
                }
  
               
                
            });
  
  
  
            
  
            //The follower listens the model to load the leader's checked options and enable the navigation button
            if (options.wizletInfo.isFollower) {
  
                self.iAmFollower(options.wizletInfo.isFollower).then(function (follower) {
                    if (follower) {
                        
                        //check if the leader had already answered
                        var teamId = self.wizerApi.getQuestionIdByName(options.wizletInfo.trackTeam);
                                
                        var questionIds = [];
                        var opts = options.wizletInfo.options;
                        var bind;
                        $.each(opts, function (idx, option) {
                            bind = option.bind || option.binding;
                            questionIds.push( self.wizerApi.getQuestionIdByName(bind) );
                        });
  
                        self.wizerApi.getForemanVotes(teamId,questionIds).then(function (response) {     
                                                        
                            if ( response.votes[questionIds[0]].length > 0 ) {
                                self.optionSelected (
                                    {data: {
                                        self: self, 
                                        context: options.context, 
                                        info: options.wizletInfo,
                                        opts: opts,
                                        votes: response.votes
                                    }})
                            } else {
                                //If not already answered, keep listening the model to change
                                $(document).on("wizer:model:change", { 
                                                    self: self, 
                                                    context: options.context, 
                                                    info: options.wizletInfo,
                                                    opts: null,
                                                    votes: []
                                                }, self.optionSelected);
                            }
                        });  
                    }
                });
  
            };
  
  
            return true;
        })
        .fail(this.wizerApi.showError);
    };
  
  
  
    CheckBoxes.prototype.iAmFollower = function (questionName) {
        var self = this;
  
        var questionId = self.wizerApi.getQuestionIdByName(questionName);
  
        var defer = Q.defer();
  
        self.wizerApi.getMyVotes([questionId]).then(function (result) {     
            defer.resolve( result.votes[questionId][0] == 1 );
        });                    
        
        return defer.promise;
    };
  
  
  
    CheckBoxes.prototype.optionSelected = function (event) {
        var self = event.data.self;
        var context = event.data.context;
        var info = event.data.info;
        var opts = event.data.opts;
        var votes = event.data.votes;
        
  
        if (opts) {
            self.refreshOptions(self, context, info, opts, votes);
            $(document).off("wizer:model:change", self.optionSelected);  
        } else {
            //check the leader's answer to refresh
            var teamId = self.wizerApi.getQuestionIdByName(info.trackTeam);
            
            var questionIds = [];
            var bind;
            $.each(info.options, function (idx, option) {
                bind = option.bind || option.binding;
                questionIds.push( self.wizerApi.getQuestionIdByName(bind) );
            });
  
            self.wizerApi.getForemanVotes(teamId,questionIds).then(function (response) {  
                var leaderVal =  response.votes;
                if (leaderVal[questionIds[0]].length> 0 ) {
                    self.refreshOptions(self, context, info, info.options, leaderVal); 
                    $(document).off("wizer:model:change", self.optionSelected);  
                }
            });  
        }
  
  
    };
    
    CheckBoxes.prototype.refreshOptions = function (self, context, info, opts, votes) {
  
        var idx = 0;
        var allCorrect = true, someCorrect = false;
        var check;
        var bind;
  
        $.each(votes, function(k,vote) {
  
            if (opts[idx].bind) {
                check = context.find('input[type="checkbox"][data-bind="'+opts[idx].bind+'"]');
            } else {
                check = context.find('input[type="checkbox"][data-binding="'+opts[idx].binding+'"]');
            }
            
            if (vote[0] === "1") {
                $(check).closest('li').addClass('chosen');
                $(check).prop('checked',true);
                allCorrect = allCorrect && $(check).data('correct');
                someCorrect = someCorrect || $(check).data('correct');
                self.choices ++;
            } else {
                allCorrect = allCorrect && !$(check).data('correct');
            }
            idx ++;
        });
  
        if (info.checkBtn && info.checkBtn.toast) {
            $('#toast-container .toast').hide()
            if (info.score) {
                self.toast (allCorrect,someCorrect,info.checkBtn.toast,info.score.points);
            } else {
                M.toast({
                    html: info.checkBtn.toast,
                    classes: 'rounded'
                });
            }
        }
        
        
        //show the hidden component (navigation button)
        if (self.choices >= info.minChoices && info.checkBtn) {
  
            if (!info.saveWhenSubmit || (info.DB[info.saved] && (info.DB[info.saved]>0))) {
  
                if (allCorrect && info.checkBtn.idToShowIfCorrect)
                    $('#'+info.checkBtn.idToShowIfCorrect).removeAttr('hidden')
                                                            .find('a').removeAttr('disabled');
                
                if (!allCorrect && someCorrect && info.checkBtn.idToShowIfSomecorrect)
                    $('#'+info.checkBtn.idToShowIfSomecorrect).removeAttr('hidden')
                                                                .find('a').removeAttr('disabled');  
                
                if (!allCorrect && !someCorrect && info.checkBtn.idToShowIfIncorrect)
                    $('#'+info.checkBtn.idToShowIfIncorrect).removeAttr('hidden')
                                                            .find('a').removeAttr('disabled');  
                
                if (info.checkBtn.idToShow) {
                    if (allCorrect && info.checkBtn.newIcon) $('#'+info.checkBtn.idToShow+' i.material-icons').html(info.checkBtn.newIcon);
                    
                    //if (!info.checkBtn.showIfChecked || context.find('input[type="checkbox"][data-bind="'+info.checkBtn.showIfChecked+'"]').prop('checked'))
                            $('#'+info.checkBtn.idToShow).removeAttr('hidden')
                                .find('a:not(.follower)').removeAttr('disabled');
                    
                    if (info.checkBtn.idToShow2) {
                            $('#'+info.checkBtn.idToShow2).removeAttr('hidden')
                                .find('a:not(.follower)').removeAttr('disabled');
                    }
                }
  
                if (info.checkBtn.idToClick) {
                    $('#'+info.checkBtn.idToClick+' a').first().click();
                }
  
            } else {
                if (info.saveWhenSubmit) context.find("#checkBtn").removeAttr('hidden');
            }
  
        }
        
        if (self.choices >= info.maxChoices) {
  
            if (!info.noBlockAnswer) {
                
                if (!info.saveWhenSubmit || (info.DB[info.saved] && (info.DB[info.saved]>0))) {
  
                    context.find('input[type="checkbox"]').filter(':not(:checked)').prop('disabled',true);
                    context.find('input[type="checkbox"]').closest('li').addClass('disabled');
                }
            }
            context.find("i.check-icon").addClass('scale-in').removeClass('scaled-out');
        } else {
            if (!info.noBlockAnswer) {   
  
                if (!info.saveWhenSubmit || (info.DB[info.saved] && (info.DB[info.saved]>0))) {
                    
                    if (info.saveWhenSubmit) {
                        context.find('input[type="checkbox"]').prop('disabled',true);
                        context.find('input[type="checkbox"]').closest('li').addClass('disabled');
                    } else {
                        context.find('input[type="checkbox"]').filter(':checked').prop('disabled',true);
                        context.find('input[type="checkbox"]').filter(':checked').closest('li').addClass('disabled');
                    }
  
                }
            }
        }
  
        
    };
    
    CheckBoxes.prototype.showCorrectOptions = function (context) {
  
        $.each(context.find('input[type="checkbox"]'), function (idx, option) {
            $(option).closest('li').addClass('disabled');
            // if ($(option).data('correct')) {
            //     $(option).closest('li').addClass('chosen');
            //     $(option).prop('checked',true);     
            // }
        });
  
        context.find("i.check-icon").addClass('scale-in').removeClass('scaled-out');
       
    };
  
    
  
    CheckBoxes.prototype.init = function (inputs) {
        var self = this; 
  
        var binds = _.unique(
                        inputs.filter('[data-bind]').map(function (idx,ele) {
                            return $(ele).data('bind');
                        }).get()
                    );
        self.initVotes(binds,0);
    };
  
    
    CheckBoxes.prototype.toast = function (allCorrect,someCorrect,toast,points) {
        var self = this; 
        
        var text, score;
        if (allCorrect) {
            text = toast.ifCorrect;
            score = points.ifCorrect;
        } else {
            if (toast.ifPartial && someCorrect) {
                text = toast.ifPartial;
                score = points.ifPartial;
            } else {
                text = toast.ifIncorrect;
                score = points.ifIncorrect;
            }
        }
        if (toast.points) {
            text += '<br><br>' + ((score>0) ? '+' : '') + score + ' ' + toast.points;
        }
  
        M.toast({
            html: text,
            classes: 'rounded'
        });
  
    };
    
  
    CheckBoxes.prototype.updateTotal = function (questionName, val, questions, qTotal, qTotal2, questionsTot, qTotalTol) {
        var self = this; 
  
        var promise1 = self.addVote(questionName, val);
        promise1.then(function () {
                
            var questionIds = [];
            //the total score is the sum of all the individual scores
            $.each(questions, function (idx, question) {
                questionIds.push( self.wizerApi.getQuestionIdByName(question) );
            });
            
            var totalPoints = 0;
            self.wizerApi.getMyVotes(questionIds).then(function(response) {
                $.each(response.votes, function(k,v) {
                    if (v[0]) totalPoints += parseInt(v[0]);
                });
                
                if (qTotal2) self.addVote(qTotal2, totalPoints);
  
                var promise2 = self.addVote(qTotal, totalPoints);
                promise2.then(function () {
                    //if there's a super-total metric, sum the partial total
                    if (qTotalTol) {
  
                        var questionTotIds = [];
                        //the total-total score is the sum of all the individual total-scores
                        $.each(questionsTot, function (idx, question) {
                            questionTotIds.push( self.wizerApi.getQuestionIdByName(question) );
                        });
                        
                        var totalTotPoints = 0;
                        self.wizerApi.getMyVotes(questionTotIds).then(function(response) {
                            $.each(response.votes, function(k,v) {
                                if (v[0]) totalTotPoints += parseInt(v[0]);
                            });
                            console.log("Total total:" + totalTotPoints);
                            
                            self.addVote(qTotalTol, totalTotPoints);
                        });
                    }
                });
  
  
            });
        });
            
    };
  
    
    /**
     * Promise function: Get vote value from a questionName
     */
    CheckBoxes.prototype.getVote = function (questionName) {
        var self = this;
  
        var questionId = self.wizerApi.getQuestionIdByName(questionName);
  
        var defer = new Q.defer();
  
        var waiting =  self.wizerApi.getMyVotes([questionId]);
  
        waiting.then(function (result) {            
            defer.resolve(result.votes[questionId][0]);
        });          
            
        
        return defer.promise;
    };
  
    CheckBoxes.prototype.removeVotes = function (questionName) {
        var self = this; 
  
        var myVotes = [];
        if (Array.isArray(questionName)) {
            questionName.forEach(function(q) { myVotes.push( { questionId: self.wizerApi.getQuestionIdByName(q) } ); });
        } else {
            myVotes.push({ questionId: self.wizerApi.getQuestionIdByName(questionName) });
        }
        self.wizerApi.removeVotes({ votes: myVotes });
    };
  
    
    CheckBoxes.prototype.initVotes = function (questionName, val) {
        var self = this; 
  
        var myVotes = [];
        if (Array.isArray(questionName)) {
            if (Array.isArray(val)) {
                questionName.forEach(function(q,i) { myVotes.push( { questionId: self.wizerApi.getQuestionIdByName(q), responseText: val[i] } ); });
            } else {
                questionName.forEach(function(q) { myVotes.push( { questionId: self.wizerApi.getQuestionIdByName(q), responseText: val } ); });
            }
        } else {
            myVotes.push({ questionId: self.wizerApi.getQuestionIdByName(questionName), responseText: val });
        }
        self.wizerApi.addVotes({ votes: myVotes });
    };
  
    
    CheckBoxes.prototype.addVote = function (questionName, val) {
        var self = this;
  
        var questionId = self.wizerApi.getQuestionIdByName(questionName);
  
        var defer = Q.defer();
  
        var waiting = self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});
  
        waiting.then(function (result) {            
            defer.resolve(result);
        });                    
        
        return defer.promise;
    };
  
    CheckBoxes.prototype.addVotes = function (questionNames, values) {
        var self = this; 
  
        var myVotes = [];
        questionNames.forEach(function(q,pos) { 
            myVotes.push( { 
                questionId: self.wizerApi.getQuestionIdByName(q), 
                responseText: values[pos] } ); 
        });
        self.wizerApi.addVotes({ votes: myVotes });
    };
    
    CheckBoxes.getRegistration = function () {
        return new CheckBoxes();
    };
  
    return CheckBoxes;
  
  });