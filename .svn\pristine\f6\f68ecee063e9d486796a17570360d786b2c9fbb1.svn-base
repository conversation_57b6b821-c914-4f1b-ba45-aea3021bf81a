.wizlet.wizletHCPieChart .header.with-label {
  position: relative;
  padding-left: 2.5rem;
  margin-bottom: 1rem;
}
.wizlet.wizletHCPieChart .header.with-label .badge.header-label {
  position: absolute;
  left: 0;
  top: 0;
  background-color: #cc4a3d;
  min-width: 2rem;
  min-height: 2rem;
  font-size: 1.25rem;
  font-weight: bold;
  border-radius: 50%;
  line-height: 2rem;
  margin: 0;
  margin-top: -5px;
}
.wizlet.wizletHCPieChart .card .card-content {
  background-color: #FFFFFF;
  padding: 6px 12px;
}
.wizlet.wizletHCPieChart .card .card-content .row {
  margin: 0;
}
.wizlet.wizletHCPieChart .highcharts-container > svg {
  width: 100% !important;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-background {
  fill: transparent;
}
@media only screen and (min-width : 601px) {
  .wizlet.wizletHCPieChart .highcharts-container .highcharts-data-label {
    font-size: 150%;
  }
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-data-label span {
  color: #ffffff;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-data-label tspan {
  fill: #ffffff;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-data-labels .highcharts-label span {
  text-align: center;
  top: -2px !important;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-data-labels .highcharts-label span img {
  display: block;
  margin-bottom: -8px;
  padding: 5px;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-color-0 {
  fill: #0AB9F0;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-color-1 {
  fill: #2E739E;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-color-2 {
  fill: #E8CD41;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-color-3 {
  fill: #16B2AB;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-color-4 {
  fill: #727FAE;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-color-5 {
  fill: #72BF44;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-color-6 {
  fill: #0AB9F0;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-color-7 {
  fill: #2E739E;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-color-8 {
  fill: #E8CD41;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-color-9 {
  fill: #16B2AB;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-color-10 {
  fill: #727FAE;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-color-11 {
  fill: #72BF44;
}
.wizlet.wizletHCPieChart .highcharts-container .highcharts-tooltip table tr, .wizlet.wizletHCPieChart .highcharts-container .highcharts-tooltip table td {
  padding: 0;
}
.wizlet.wizletHCPieChart .bach-content-pieChart--container.semaphore .highcharts-color-0 {
  fill: #fb054b;
}
.wizlet.wizletHCPieChart .bach-content-pieChart--container.semaphore .highcharts-color-1 {
  fill: #E8CD41;
}
.wizlet.wizletHCPieChart .bach-content-pieChart--container.semaphore .highcharts-color-2 {
  fill: #72BF44;
}
.wizlet.wizletHCPieChart .bach-content-pieChart--container.yesno .highcharts-color-0 {
  fill: #72BF44;
}
.wizlet.wizletHCPieChart .bach-content-pieChart--container.yesno .highcharts-color-1 {
  fill: #fb054b;
}
.wizlet.wizletHCPieChart .bach-content-pieChart--container.noyes .highcharts-color-0 {
  fill: #fb054b;
}
.wizlet.wizletHCPieChart .bach-content-pieChart--container.noyes .highcharts-color-1 {
  fill: #72BF44;
}
@media only screen and (min-width : 601px) {
  .wizlet.wizletHCPieChart .bach-content-pieChart--container.labels.medium .highcharts-data-label {
    font-size: 125%;
  }
}
@media only screen and (min-width : 601px) {
  .wizlet.wizletHCPieChart .bach-content-pieChart--container.labels.small .highcharts-data-label {
    font-size: 100%;
  }
}
.wizlet.wizletHCPieChart .bach-content-pieChart--container.noclickablelegend .highcharts-legend {
  pointer-events: none;
}
.wizlet.wizletHCPieChart .answers.card-panel {
  padding: 8px 12px;
}
.wizlet.wizletHCPieChart .answers.card-panel .btn-text {
  display: inline-block;
  vertical-align: sub;
}