@charset "UTF-8";
.wizlet.wizletCarousel .carousel {
  margin: 20px 0;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletCarousel .carousel {
    height: 150px;
  }
}
@media only screen and (max-width : 992px) {
  .wizlet.wizletCarousel .carousel {
    height: 250px;
  }
}
@media only screen and (min-width : 601px) {
  .wizlet.wizletCarousel .carousel {
    height: 350px;
  }
}
@media only screen and (min-width : 993px) {
  .wizlet.wizletCarousel .carousel {
    height: 450px;
  }
}
@media only screen and (min-width : 1201px) {
  .wizlet.wizletCarousel .carousel {
    height: 550px;
  }
}
.wizlet.wizletCarousel .carousel .carousel-item {
  width: 50%;
  height: 100%;
}
.wizlet.wizletCarousel .carousel .carousel-item .card {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  background-color: #fd85c5;
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-image {
  min-width: 30%;
  max-height: 75%;
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-image img {
  height: 300px;
  -o-object-fit: contain;
     object-fit: contain;
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-action a {
  font-family: clientBold, Arial;
  cursor: pointer;
  color: #cc4a3d !important;
  font-size: 20px;
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-action a:hover {
  color: #cc4a3d !important;
  text-decoration: underline;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletCarousel .carousel .carousel-item .card .card-action a {
    font-size: 16px;
  }
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-content,
.wizlet.wizletCarousel .carousel .carousel-item .card .card-reveal {
  width: 90%;
  height: auto;
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-content.card-content,
.wizlet.wizletCarousel .carousel .carousel-item .card .card-reveal.card-content {
  background-color: #FFFFFF;
  border-radius: 10px;
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-content .card-title,
.wizlet.wizletCarousel .carousel .carousel-item .card .card-reveal .card-title {
  font-family: clientBolder, clientBold, Arial;
  font-size: 100%;
  line-height: 100%;
  text-align: left;
  padding-bottom: 5px;
  border-bottom: 3px solid #cc4a3d;
  margin-bottom: 15px;
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-content .card-title.activator, .wizlet.wizletCarousel .carousel .carousel-item .card .card-content .card-title.reveal,
.wizlet.wizletCarousel .carousel .carousel-item .card .card-reveal .card-title.activator,
.wizlet.wizletCarousel .carousel .carousel-item .card .card-reveal .card-title.reveal {
  position: relative;
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-content .card-title.activator i.material-icons.right, .wizlet.wizletCarousel .carousel .carousel-item .card .card-content .card-title.reveal i.material-icons.right,
.wizlet.wizletCarousel .carousel .carousel-item .card .card-reveal .card-title.activator i.material-icons.right,
.wizlet.wizletCarousel .carousel .carousel-item .card .card-reveal .card-title.reveal i.material-icons.right {
  position: absolute;
  top: 0;
  right: -5px;
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-content .card-title.size.big,
.wizlet.wizletCarousel .carousel .carousel-item .card .card-reveal .card-title.size.big {
  font-size: 120%;
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-content .card-title.size.small,
.wizlet.wizletCarousel .carousel .carousel-item .card .card-reveal .card-title.size.small {
  font-size: 90%;
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-reveal {
  padding-top: 0px;
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-reveal .reveal-text {
  font-size: 90%;
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-content {
  padding: 5px 12px;
  overflow: auto;
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-content .card-title {
  border-bottom: none;
  line-height: 2rem;
  word-break: break-word;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletCarousel .carousel .carousel-item .card .card-content .card-title {
    font-size: 20px;
  }
}
.wizlet.wizletCarousel .carousel .carousel-item .card .card-action {
  padding: 8px 12px;
  cursor: pointer;
}
.wizlet.wizletCarousel .carousel .carousel-item .card.sticky-action .card-reveal {
  opacity: 0.95;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
  filter: alpha(opacity=95);
  zoom: 1;
}
.wizlet.wizletCarousel .carousel .carousel-item .card.sticky-action .card-reveal p, .wizlet.wizletCarousel .carousel .carousel-item .card.sticky-action .card-reveal ul {
  font-size: inherit;
}
.wizlet.wizletCarousel .carousel .carousel-item .card.sticky-action.with-overflow {
  overflow-y: auto;
}
.wizlet.wizletCarousel .carousel .carousel-item .card.sticky-action.with-overflow .card-reveal {
  height: 150px;
}
.wizlet.wizletCarousel .carousel .carousel-item .card.sticky-action.with-overflow .card-reveal .reveal-text {
  position: absolute;
  padding-bottom: 10px;
}
.wizlet.wizletCarousel .carousel .carousel-item .card.disabled {
  pointer-events: none;
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.wizlet.wizletCarousel .carousel .carousel-item .card.no-reveal .card-content .card-title {
  text-align: center;
}
.wizlet.wizletCarousel .carousel .carousel-item .card.no-reveal .card-image, .wizlet.wizletCarousel .carousel .carousel-item .card.no-reveal .card-content {
  cursor: pointer;
}
.wizlet.wizletCarousel .carousel .indicators {
  bottom: -28px;
}
.wizlet.wizletCarousel .carousel .indicators .indicator-item {
  background-color: #24466b;
}
.wizlet.wizletCarousel .carousel .indicators .indicator-item.active {
  background-color: #cc4a3d;
}
.wizlet.wizletCarousel .modal.open {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 90vw;
  height: auto;
  background-color: rgba(0, 0, 0, 0);
}
.wizlet.wizletCarousel .modal.open .modal-content {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  text-align: center;
}
.wizlet.wizletCarousel .modal.open .modal-content img {
  width: 100%;
  height: -webkit-fill-available;
  max-width: 90vw;
  -o-object-fit: contain;
     object-fit: contain;
}
.wizlet.wizletCarousel .modal.open.fullModal {
  overflow: hidden;
  padding: 0;
}
.wizlet.wizletCarousel .modal.open.fullModal .modal-content {
  background-color: #FFFFFF;
  padding: 5px;
  height: 90vh;
}
.wizlet.wizletCarousel .modal.open.fullModal .modal-footer {
  position: absolute;
  top: 0;
  right: 0;
  background: none;
}
.wizlet.wizletCarousel .modal.open.fullModal .modal-footer .modal-close {
  margin: 0;
  padding: 0;
}
.wizlet.wizletCarousel .modal.open.fullModal .modal-footer .modal-close i {
  margin: 0 5px;
}