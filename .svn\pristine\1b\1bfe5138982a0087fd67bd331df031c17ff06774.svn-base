<?xml version="1.0" encoding="utf-8" ?>
  
<Action>

  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "slideInUp",
      content: {
        _img_embed: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          position: "right", style: "",
          src_vert: "!{}"
        },
        _img: { 
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_CaseStudy_Img}",  alt: "!{SIM_CaseStudy_Title}" ,
          src_vert: "!{}"
        },
        position: "up",
        title: "!{SIM_CaseStudy3_Title}",
        body: "!{SIM_CaseStudy3_Text} !{SIM_CaseStudy_Next}"
      }      
    }]]>
  </Component>
  
  
  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "zoomIn animate__delay-3s",
      id: "navButton",
      _isHidden: true, _showDelay: "5000",
      hasModals: false,
      buttons: [
        {
          type: "next",
          pulse: true,
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD",
          gdActionSection: "",
          targetSection: "",
          label: "!{Navigation_start}",
          tooltip: "!{Navigation_start_tt}",
          icon: "forward"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>



</Action>