.wizlet.wizletButtonChoice .header.with-label { position: relative; padding-left: 3rem; }

.wizlet.wizletButtonChoice .header.with-label .badge.header-label { position: absolute; left: 0; top: 0; background-color: #041E42; min-width: 2.5rem; min-height: 2.5rem; font-size: 1.75rem; font-weight: bold; border-radius: 50%; line-height: 2.35rem; margin: 0; margin-top: 2px; }

.wizlet.wizletButtonChoice .card .card-content .header.subtitle { margin-top: 0; margin-bottom: 25px; }

.wizlet.wizletButtonChoice .card .card-content .choices-container { position: relative; margin: 0; }

.wizlet.wizletButtonChoice .card .card-content .choices-container.chosen a.choice { pointer-events: none; }

.wizlet.wizletButtonChoice .card .card-content .choices-container.chosen a.choice:not(.chosen) { color: #ffffff !important; background-color: #ACA39A !important; }

.wizlet.wizletButtonChoice .card .card-content .choices-container a.choice { width: 100%; }

.wizlet.wizletButtonChoice .card .card-content .choices-container a.choice i.left { margin-right: 5px; }

.wizlet.wizletButtonChoice .card .card-content .choices-container a.choice i.right { margin-left: 5px; }

.wizlet.wizletButtonChoice .card .card-content .choices-container a.choice i, .wizlet.wizletButtonChoice .card .card-content .choices-container a.choice .text { font-size: 2rem; }

@media only screen and (max-width: 600px) { .wizlet.wizletButtonChoice .card .card-content .choices-container a.choice i, .wizlet.wizletButtonChoice .card .card-content .choices-container a.choice .text { font-size: 1.5rem; } }

.wizlet.wizletButtonChoice .card .card-content .choices-container a.choice:hover, .wizlet.wizletButtonChoice .card .card-content .choices-container a.choice.chosen { -webkit-transform: scale(1.1); transform: scale(1.1); color: #ffffff !important; background-color: #FF8200 !important; }

.wizlet.wizletButtonChoice .card .card-content .choices-container a.choice:hover i, .wizlet.wizletButtonChoice .card .card-content .choices-container a.choice:hover .text, .wizlet.wizletButtonChoice .card .card-content .choices-container a.choice.chosen i, .wizlet.wizletButtonChoice .card .card-content .choices-container a.choice.chosen .text { font-size: 2.25rem; }

@media only screen and (max-width: 600px) { .wizlet.wizletButtonChoice .card .card-content .choices-container a.choice:hover i, .wizlet.wizletButtonChoice .card .card-content .choices-container a.choice:hover .text, .wizlet.wizletButtonChoice .card .card-content .choices-container a.choice.chosen i, .wizlet.wizletButtonChoice .card .card-content .choices-container a.choice.chosen .text { font-size: 1.75rem; } }

.wizlet.wizletButtonChoice .card .card-content .choices-container a.choice.isMyth { background-color: #003B5C; }

.wizlet.wizletButtonChoice .card .card-content .choices-container a.choice.isReality { background-color: #00859B; }

.wizlet.wizletButtonChoice .card .card-content .choices-container i.check-icon { position: absolute; color: #ffffff !important; top: -2px; z-index: 1; }

.wizlet.wizletButtonChoice .card .card-content .choices-container i.check-icon.left { left: 0; }

.wizlet.wizletButtonChoice .card .card-content .choices-container i.check-icon.right { right: 0; }

.wizlet.wizletButtonChoice .card .card-content .choices-container i.check-icon.scaled-out { -webkit-transform: scale(0); transform: scale(0); }

@media only screen and (max-width: 600px) { .wizlet.wizletButtonChoice .card .card-content .choices-container i.check-icon { font-size: 3rem; } }
