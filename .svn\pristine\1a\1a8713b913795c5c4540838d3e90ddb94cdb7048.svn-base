﻿<div class="{{?it.content}}container{{?}}{{?it.animate}} animate__animated animate__{{=it.animate}}{{?}}{{?it.borders}} borders {{=it.borders}}{{?}}{{?it.isHiddenWhenSmall}} hide-on-med-and-down{{?}}{{?it.isHiddenWhenBig}} hide-on-large-only{{?}}" 
{{?it.id}}id="{{=it.id}}"{{?}} 

{{?it.isHidden}}
    {{?it.condition}}
        {{?!it.DB[it.condition] || 
            (it.condition_Val && (it.DB[it.condition] == it.condition_Val)) ||
            (it.condition_Diff && (it.DB[it.condition] != it.condition_Diff)) ||
            (it.condition_Greater && (it.DB[it.condition] > it.condition_Greater)) ||
            (it.condition_Less && (it.DB[it.condition] < it.condition_Less))}}
            hidden
        {{?}}
    {{??}}
        hidden
    {{?}}
{{?}}
>  


    {{?it.header}}
    <h4 class="header {{?it.header_color}}client-colors-text text-{{=it.header_color}}{{?}}">{{=it.header}}</h4>{{?}} {{?it.subheader}}
    <h5 class="header {{?it.header_color}}client-colors-text text-{{=it.header_color}}{{?}}">{{=it.subheader}}</h5>{{?}} {{?it.instructions}}
    <h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}} {{?it.content}}
    <div class="card hoverable {{?it.size}}{{=it.size}}{{?}} {{?it.orientation}}{{=it.orientation}}{{?}}{{?it.content && it.content.position}} {{=it.content.position}}{{?}} {{?!it.header}}no-header{{?}} {{?it.transparentBox}}transparent{{?}}">

        {{?it.content.position && it.content.position=="up"}}
        <div class="card-content {{?it.content.nopadding}}no-padding{{?}} {{?it.content.centered}}centered{{?}}{{?it.content.borders}} borders {{=it.content.borders}}{{?}}">
            {{?it.content.title}}<span class="card-title {{?it.content.titleClass}}{{=it.content.titleClass}}{{?}}">{{=it.content.title}}</span>{{?}} {{?it.embed}}
            <div class="embed {{=it.embed}}">
                <lottie-player class="" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.lottie.src}}" background="{{?it.lottie.background}}{{=it.lottie.background}}{{??}}transparent{{?}}" speed="{{?it.lottie.speed}}{{=it.lottie.speed}}{{??}}1{{?}}" style="{{?it.lottie.style}}{{=it.lottie.style}}{{??}}width: 100%; height: auto;{{?}}"
                    {{?it.lottie.loop}}loop{{?}} {{?it.lottie.autoplay}}autoplay{{?}}>
                </lottie-player>
            </div>
            {{?}} {{?it.content.body}}

            <div class="body flow-text">{{=it.content.body}}</div>{{?}}

        </div>
        {{?}} {{? !it.embed}}
        <div class="card-image {{?it.orientation=='horizontal'}}hide-on-small-and-down{{??}}full-width{{?}} {{?it.lottie.borders}}borders {{=it.lottie.borders}}{{?}} {{?it.lottie.nopadding}}no-padding{{?}}{{?it.lottie.small}} small{{?}}">

            <lottie-player class="" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.lottie.src}}" background="{{?it.lottie.background}}{{=it.lottie.background}}{{??}}transparent{{?}}" speed="{{?it.lottie.speed}}{{=it.lottie.speed}}{{??}}1{{?}}" style="{{?it.lottie.style}}{{=it.lottie.style}}{{??}}width: 100%; height: auto;{{?}}"
                {{?it.lottie.loop}}loop{{?}} {{?it.lottie.autoplay}}autoplay{{?}}>
            </lottie-player>

        </div>
        {{?}} {{?it.content.position && it.content.position=="down"}}
        <div class="card-content {{?it.content.nopadding}}no-padding{{?}} {{?it.content.centered}}centered{{?}}">
            {{?it.content.title}}<span class="card-title {{?it.content.titleClass}}{{=it.content.titleClass}}{{?}}">{{=it.content.title}}</span>{{?}} {{?it.content.body}}
            <span class="flow-text">{{=it.content.body}}</span>{{?}}
        </div>
        {{?}} {{??}}

        <lottie-player class="" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.lottie.src}}" background="{{?it.lottie.background}}{{=it.lottie.background}}{{??}}transparent{{?}}" speed="{{?it.lottie.speed}}{{=it.lottie.speed}}{{??}}1{{?}}" style="{{?it.lottie.style}}{{=it.lottie.style}}{{??}}width: 300px; height: 300px;{{?}}"
            {{?it.lottie.loop}}loop{{?}} {{?it.lottie.autoplay}}autoplay{{?}}>
        </lottie-player>

        {{?}}


    </div>