<?xml version="1.0" encoding="utf-8" ?>
<Action mainAreaLayout="../../../layout/mainLayoutLanding">
  <Include name="Header_Intro"></Include>

  <Component type="Vanilla"><![CDATA[{
    templateInEvent: "html/landingPage.dot",
    css: "styles/landingPage.css",
    fullScreen: true,
    facilitator: false,
    content: {
      withBubbles: false,
      title: "!{Landing_Title}",
      subtitle: "!{Landing_Subtitle}",
      image: "!{LandingPage_Img0}",
      firstIsLogo: false,
      images: ["!{LandingPage_Img1}","!{LandingPage_Img2}","!{LandingPage_Img3}", "!{LandingPage_Img4}",
               "!{LandingPage_Img5}","!{LandingPage_Img6}" ]
    }
  }]]></Component>

  <!-- Reset all participant's votes when landing -->
  <Component type="myCode_reset" customJS="true"><![CDATA[{
      clearAll: true,
      resetVotes: false
  }]]></Component> 



  <Voting autoNext="false">
    <Score type="Vote" result="Score_SIM_Init_KPI1" response="!{KPI_Metric1_Init}"/> 
    <Score type="Vote" result="Score_SIM_Init_KPI2" response="!{KPI_Metric2_Init}"/> 
    <Score type="Vote" result="Score_SIM_Init_KPI3" response="!{KPI_Metric3_Init}"/> 
  </Voting>

  <Voting autoNext="false">
    <Score type="TimeStamp" result="Q_LOGIN_DATE_R1" keepFirst="false"/> 
  </Voting>


</Action>