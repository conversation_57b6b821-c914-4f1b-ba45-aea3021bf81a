@charset "UTF-8";
.mainAreaContainer main.landingPage {
  height: calc(100vh - 99px);
}
.mainAreaContainer main.landingPage.facilitator {
  height: calc(100vh - 2 * 118px);
}
.mainAreaContainer main.landingPage.facilitator2 {
  height: calc(100vh - 2 * 84px);
}
.mainAreaContainer main.landingPage .title-box {
  position: absolute;
  margin: 5rem;
  padding-left: 1rem;
  z-index: 1;
  text-align: left;
  background-color: transparent;
  border-left: 2rem solid #cc4a3d;
  border-radius: 1rem;
}
.mainAreaContainer main.landingPage .title-box .title {
  margin-top: 1rem;
  color: #ffffff;
  -webkit-filter: drop-shadow(0.05em 0.05em 0em #24466b);
          filter: drop-shadow(0.05em 0.05em 0em #24466b);
}
.mainAreaContainer main.landingPage .title-box .subtitle {
  margin-bottom: 1rem;
  color: #ffffff;
  -webkit-filter: drop-shadow(0.05em 0.05em 0em #cc4a3d);
          filter: drop-shadow(0.05em 0.05em 0em #cc4a3d);
}
.mainAreaContainer main.landingPage .title-box.with-bubbles:before, .mainAreaContainer main.landingPage .title-box.with-bubbles:after {
  content: "";
  height: 4.5rem;
  width: 4.5rem;
  border-radius: 50%;
  background-color: #cc4a3d;
  position: absolute;
  bottom: 3rem;
  right: -4rem;
  -o-animation-name: wobble;
  -webkit-animation-name: wobble;
  animation-name: wobble;
  -o-animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -o-animation-timing-function: ease;
  -webkit-animation-timing-function: ease;
  animation-timing-function: ease;
  -o-animation-duration: 10s;
  -webkit-animation-duration: 10s;
  animation-duration: 10s;
  -o-transform-origin: center bottom;
  -ms-transform-origin: center bottom;
  -webkit-transform-origin: center bottom;
  transform-origin: center bottom;
}
.mainAreaContainer main.landingPage .title-box.with-bubbles:after {
  height: 2rem;
  width: 2rem;
  bottom: 7rem;
  right: -5rem;
}
.mainAreaContainer main.landingPage.facilitator .title {
  top: 40%;
  z-index: -1;
}

.mainAreaContainer.landingpageend main.landingPage.facilitator {
  height: calc(100vh - 2 * 118px);
}
.mainAreaContainer.landingpageend main.landingPage.facilitator2 {
  height: calc(100vh - 2 * 84px);
}

.mainAreaContainer.landingpage,
.mainAreaContainer.landingpageend {
  /** Landing Page Animations **/
  /* moveIn */
}
.mainAreaContainer.landingpage main.landingPage .image,
.mainAreaContainer.landingpageend main.landingPage .image {
  height: calc(100vh - 99px);
}
.mainAreaContainer.landingpage main.landingPage .image > img,
.mainAreaContainer.landingpageend main.landingPage .image > img {
  width: 100%;
  height: calc(100vh - 99px);
  -o-object-fit: cover;
     object-fit: cover;
  position: absolute;
  left: 0;
}
.mainAreaContainer.landingpage main.landingPage .image.carousel,
.mainAreaContainer.landingpageend main.landingPage .image.carousel {
  position: relative;
  margin: 0 auto;
}
.mainAreaContainer.landingpage main.landingPage .image.carousel > img,
.mainAreaContainer.landingpageend main.landingPage .image.carousel > img {
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  position: absolute;
  left: 0;
  opacity: 0;
  -webkit-transition: opacity 1s ease-in-out;
  -o-transition: opacity 1s ease-in-out;
  transition: opacity 1s ease-in-out;
  -o-animation-name: carouselFadeInOut;
  -webkit-animation-name: carouselFadeInOut;
  animation-name: carouselFadeInOut;
  -o-animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -o-animation-timing-function: ease-in-out;
  -webkit-animation-timing-function: ease-in-out;
  animation-timing-function: ease-in-out;
  -o-animation-duration: 20s;
  -webkit-animation-duration: 20s;
  animation-duration: 20s;
}
.mainAreaContainer.landingpage main.landingPage .image.carousel > img.firstIsLogo,
.mainAreaContainer.landingpageend main.landingPage .image.carousel > img.firstIsLogo {
  -o-object-fit: scale-down;
     object-fit: scale-down;
}
.mainAreaContainer.landingpage main.landingPage .image.carousel > img:nth-of-type(1),
.mainAreaContainer.landingpageend main.landingPage .image.carousel > img:nth-of-type(1) {
  -o-animation-delay: 16s;
  -webkit-animation-delay: 16s;
  animation-delay: 16s;
}
.mainAreaContainer.landingpage main.landingPage .image.carousel > img:nth-of-type(2),
.mainAreaContainer.landingpageend main.landingPage .image.carousel > img:nth-of-type(2) {
  -o-animation-delay: 12s;
  -webkit-animation-delay: 12s;
  animation-delay: 12s;
}
.mainAreaContainer.landingpage main.landingPage .image.carousel > img:nth-of-type(3),
.mainAreaContainer.landingpageend main.landingPage .image.carousel > img:nth-of-type(3) {
  -o-animation-delay: 8s;
  -webkit-animation-delay: 8s;
  animation-delay: 8s;
}
.mainAreaContainer.landingpage main.landingPage .image.carousel > img:nth-of-type(4),
.mainAreaContainer.landingpageend main.landingPage .image.carousel > img:nth-of-type(4) {
  -o-animation-delay: 4s;
  -webkit-animation-delay: 4s;
  animation-delay: 4s;
}
.mainAreaContainer.landingpage main.landingPage .image.carousel > img:nth-of-type(5),
.mainAreaContainer.landingpageend main.landingPage .image.carousel > img:nth-of-type(5) {
  -o-animation-delay: 0s;
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
}
.mainAreaContainer.landingpage main.landingPage.facilitator .image,
.mainAreaContainer.landingpageend main.landingPage.facilitator .image {
  height: calc(100vh - 2 * 118px);
  /*img {
    height: calc(100vh - 84px);
    object-fit: cover;

  }*/
}
.mainAreaContainer.landingpage main.landingPage.facilitator2 .image,
.mainAreaContainer.landingpageend main.landingPage.facilitator2 .image {
  height: calc(100vh - 2 * 84px);
}
.mainAreaContainer.landingpage #splash-ball, .mainAreaContainer.landingpage #splash-overlay,
.mainAreaContainer.landingpageend #splash-ball,
.mainAreaContainer.landingpageend #splash-overlay {
  left: 50%;
  overflow: hidden;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
       -o-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  border-radius: 50%;
  -webkit-background-clip: padding-box;
  -moz-background-clip: padding;
  background-clip: padding-box;
}
.mainAreaContainer.landingpage #splash-overlay,
.mainAreaContainer.landingpageend #splash-overlay {
  height: 0px;
  padding: 0px;
  border: 130em solid #ededed;
  position: fixed;
  top: 100%;
  display: block;
  -webkit-box-sizing: initial;
          box-sizing: initial;
  -o-animation-name: puff, borderRadius;
  -webkit-animation-name: puff, borderRadius;
  animation-name: puff, borderRadius;
  -o-animation-duration: 0.5s, 0.2s;
  -webkit-animation-duration: 0.5s, 0.2s;
  animation-duration: 0.5s, 0.2s;
  -o-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19), linear;
  -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19), linear;
  animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19), linear;
  -o-animation-delay: 1.8s, 2.3s;
  -webkit-animation-delay: 1.8s, 2.3s;
  animation-delay: 1.8s, 2.3s;
  -o-animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}
.mainAreaContainer.landingpage #splash-ball,
.mainAreaContainer.landingpageend #splash-ball {
  background: #cc4a3d;
  width: 56px;
  height: 56px;
  position: absolute;
  z-index: 1;
  top: 50%;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
  filter: alpha(opacity=0);
  zoom: 1;
  -o-animation-name: init, moveDown, moveUp, materia, hide;
  -webkit-animation-name: init, moveDown, moveUp, materia, hide;
  animation-name: init, moveDown, moveUp, materia, hide;
  -o-animation-duration: 0.5s, 1s, 1s, 0.5s, 2s;
  -webkit-animation-duration: 0.5s, 1s, 1s, 0.5s, 2s;
  animation-duration: 0.5s, 1s, 1s, 0.5s, 2s;
  -o-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19), cubic-bezier(0.6, -0.28, 0.735, 0.045), cubic-bezier(0.175, 0.885, 0.32, 1.275), cubic-bezier(0.86, 0, 0.07, 1), ease;
  -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19), cubic-bezier(0.6, -0.28, 0.735, 0.045), cubic-bezier(0.175, 0.885, 0.32, 1.275), cubic-bezier(0.86, 0, 0.07, 1), ease;
  animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19), cubic-bezier(0.6, -0.28, 0.735, 0.045), cubic-bezier(0.175, 0.885, 0.32, 1.275), cubic-bezier(0.86, 0, 0.07, 1), ease;
  -o-animation-delay: 0.2s, 0.8s, 1.8s, 2.7s, 2.9s;
  -webkit-animation-delay: 0.2s, 0.8s, 1.8s, 2.7s, 2.9s;
  animation-delay: 0.2s, 0.8s, 1.8s, 2.7s, 2.9s;
  -o-animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}
.mainAreaContainer.landingpage header, .mainAreaContainer.landingpage footer,
.mainAreaContainer.landingpageend header,
.mainAreaContainer.landingpageend footer {
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
  filter: alpha(opacity=0);
  zoom: 1;
  -o-animation-name: moveIn;
  -webkit-animation-name: moveIn;
  animation-name: moveIn;
  -o-animation-duration: 2s;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -o-animation-timing-function: ease;
  -webkit-animation-timing-function: ease;
  animation-timing-function: ease;
  -o-animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}
.mainAreaContainer.landingpage header,
.mainAreaContainer.landingpageend header {
  -o-animation-delay: 3.1s;
  -webkit-animation-delay: 3.1s;
  animation-delay: 3.1s;
}
.mainAreaContainer.landingpage footer,
.mainAreaContainer.landingpageend footer {
  -o-animation-delay: 2s;
  -webkit-animation-delay: 2s;
  animation-delay: 2s;
}
.mainAreaContainer.landingpage #start-button,
.mainAreaContainer.landingpageend #start-button {
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
  filter: alpha(opacity=0);
  zoom: 1;
  -o-animation-name: moveIn, shakeInfinite;
  -webkit-animation-name: moveIn, shakeInfinite;
  animation-name: moveIn, shakeInfinite;
  -o-animation-duration: 2s, 10s;
  -webkit-animation-duration: 2s, 10s;
  animation-duration: 2s, 10s;
  -o-animation-iteration-count: 1, infinite;
  -webkit-animation-iteration-count: 1, infinite;
  animation-iteration-count: 1, infinite;
  -o-animation-timing-function: ease;
  -webkit-animation-timing-function: ease;
  animation-timing-function: ease;
  -o-animation-delay: 3.1s, 10s;
  -webkit-animation-delay: 3.1s, 10s;
  animation-delay: 3.1s, 10s;
  -o-animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}
.mainAreaContainer.landingpage .video-holder,
.mainAreaContainer.landingpageend .video-holder {
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
  filter: alpha(opacity=0);
  zoom: 1;
  -o-animation-name: moveIn;
  -webkit-animation-name: moveIn;
  animation-name: moveIn;
  -o-animation-duration: 2s;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -o-animation-iteration-count: 1;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -o-animation-timing-function: ease;
  -webkit-animation-timing-function: ease;
  animation-timing-function: ease;
  -o-animation-delay: 3.1s;
  -webkit-animation-delay: 3.1s;
  animation-delay: 3.1s;
  -o-animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}
@-webkit-keyframes init {
  0% {
    width: 0px;
    height: 0px;
  }
  100% {
    width: 56px;
    height: 56px;
    margin-top: 0px;
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
}
@-o-keyframes init {
  0% {
    width: 0px;
    height: 0px;
  }
  100% {
    width: 56px;
    height: 56px;
    margin-top: 0px;
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
}
@keyframes init {
  0% {
    width: 0px;
    height: 0px;
  }
  100% {
    width: 56px;
    height: 56px;
    margin-top: 0px;
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
}
@-webkit-keyframes puff {
  0% {
    top: 100%;
    height: 0px;
    padding: 0px;
  }
  100% {
    top: 50%;
    height: 100%;
    padding: 0px 100%;
  }
}
@-o-keyframes puff {
  0% {
    top: 100%;
    height: 0px;
    padding: 0px;
  }
  100% {
    top: 50%;
    height: 100%;
    padding: 0px 100%;
  }
}
@keyframes puff {
  0% {
    top: 100%;
    height: 0px;
    padding: 0px;
  }
  100% {
    top: 50%;
    height: 100%;
    padding: 0px 100%;
  }
}
@-webkit-keyframes borderRadius {
  0% {
    border-radius: 50%;
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
  }
  100% {
    border-radius: 0px;
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    border: none;
  }
}
@-o-keyframes borderRadius {
  0% {
    border-radius: 50%;
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
  }
  100% {
    border-radius: 0px;
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    border: none;
  }
}
@keyframes borderRadius {
  0% {
    border-radius: 50%;
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
  }
  100% {
    border-radius: 0px;
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    border: none;
  }
}
@-webkit-keyframes moveDown {
  0% {
    top: 50%;
  }
  50% {
    top: 40%;
  }
  100% {
    top: 95%;
  }
}
@-o-keyframes moveDown {
  0% {
    top: 50%;
  }
  50% {
    top: 40%;
  }
  100% {
    top: 95%;
  }
}
@keyframes moveDown {
  0% {
    top: 50%;
  }
  50% {
    top: 40%;
  }
  100% {
    top: 95%;
  }
}
@-webkit-keyframes moveUp {
  0% {
    top: 100%;
  }
  50% {
    top: 40%;
  }
  100% {
    top: 50%;
  }
}
@-o-keyframes moveUp {
  0% {
    top: 100%;
  }
  50% {
    top: 40%;
  }
  100% {
    top: 50%;
  }
}
@keyframes moveUp {
  0% {
    top: 100%;
  }
  50% {
    top: 40%;
  }
  100% {
    top: 50%;
  }
}
@-webkit-keyframes materia {
  0% {
    background: #cc4a3d;
  }
  50% {
    background: #cc4a3d;
    top: calc(44px * 0.4);
  }
  100% {
    background: #FFFFFF;
    width: 100%;
    height: 44px;
    top: calc(44px * 0.4);
    border-radius: 0px;
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    display: none;
  }
}
@-o-keyframes materia {
  0% {
    background: #cc4a3d;
  }
  50% {
    background: #cc4a3d;
    top: calc(44px * 0.4);
  }
  100% {
    background: #FFFFFF;
    width: 100%;
    height: 44px;
    top: calc(44px * 0.4);
    border-radius: 0px;
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    display: none;
  }
}
@keyframes materia {
  0% {
    background: #cc4a3d;
  }
  50% {
    background: #cc4a3d;
    top: calc(44px * 0.4);
  }
  100% {
    background: #FFFFFF;
    width: 100%;
    height: 44px;
    top: calc(44px * 0.4);
    border-radius: 0px;
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    display: none;
  }
}
@-webkit-keyframes moveIn {
  0% {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=0);
    zoom: 1;
  }
  100% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
}
@-o-keyframes moveIn {
  0% {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=0);
    zoom: 1;
  }
  100% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
}
@keyframes moveIn {
  0% {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=0);
    zoom: 1;
  }
  100% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
}
@-webkit-keyframes hide {
  0% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
  100% {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=0);
    zoom: 1;
    display: none;
  }
}
@-o-keyframes hide {
  0% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
  100% {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=0);
    zoom: 1;
    display: none;
  }
}
@keyframes hide {
  0% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
  100% {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=0);
    zoom: 1;
    display: none;
  }
}
@-webkit-keyframes carouselFadeInOut {
  0% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
  17% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
  25% {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=0);
    zoom: 1;
  }
  92% {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=0);
    zoom: 1;
  }
  100% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
}
@-o-keyframes carouselFadeInOut {
  0% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
  17% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
  25% {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=0);
    zoom: 1;
  }
  92% {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=0);
    zoom: 1;
  }
  100% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
}
@keyframes carouselFadeInOut {
  0% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
  17% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
  25% {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=0);
    zoom: 1;
  }
  92% {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=0);
    zoom: 1;
  }
  100% {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=” $value * 100 “)";
    filter: alpha(opacity=100);
    zoom: 1;
  }
}
@-webkit-keyframes wobble {
  33% {
    -o-transform: rotate(20deg);
    -ms-transform: rotate(20deg);
    -webkit-transform: rotate(20deg);
    transform: rotate(20deg);
  }
  66% {
    -o-transform: rotate(-20deg);
    -ms-transform: rotate(-20deg);
    -webkit-transform: rotate(-20deg);
    transform: rotate(-20deg);
  }
}
@-o-keyframes wobble {
  33% {
    -o-transform: rotate(20deg);
    -ms-transform: rotate(20deg);
    -webkit-transform: rotate(20deg);
    transform: rotate(20deg);
  }
  66% {
    -o-transform: rotate(-20deg);
    -ms-transform: rotate(-20deg);
    -webkit-transform: rotate(-20deg);
    transform: rotate(-20deg);
  }
}
@keyframes wobble {
  33% {
    -o-transform: rotate(20deg);
    -ms-transform: rotate(20deg);
    -webkit-transform: rotate(20deg);
    transform: rotate(20deg);
  }
  66% {
    -o-transform: rotate(-20deg);
    -ms-transform: rotate(-20deg);
    -webkit-transform: rotate(-20deg);
    transform: rotate(-20deg);
  }
}