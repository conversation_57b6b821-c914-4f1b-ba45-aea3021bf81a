.wizlet.wizletGauges nav.gauges {
  z-index: 998;
  background: none;
  -webkit-box-shadow: none;
          box-shadow: none;
  height: 70px;
}
.wizlet.wizletGauges nav.gauges .row {
  margin: 0;
}
.wizlet.wizletGauges nav.gauges .highcharts-background,
.wizlet.wizletGauges nav.gauges .highcharts-plot-background,
.wizlet.wizletGauges nav.gauges .highcharts-plot-border,
.wizlet.wizletGauges nav.gauges .highcharts-legend,
.wizlet.wizletGauges nav.gauges .highcharts-axis-labels,
.wizlet.wizletGauges nav.gauges .highcharts-grid,
.wizlet.wizletGauges nav.gauges .highcharts-axis.highcharts-yaxis .highcharts-axis-line {
  display: none;
}
.wizlet.wizletGauges nav.gauges .highcharts-pane-group,
.wizlet.wizletGauges nav.gauges .highcharts-pane-group .highcharts-pane {
  fill: white;
  fill-opacity: 1;
  opacity: 1;
}
.wizlet.wizletGauges nav.gauges .statusBar {
  -webkit-box-shadow: 0 2px 2px 0 rgba(204, 74, 61, 0.14), 0 3px 1px -2px rgba(204, 74, 61, 0.12), 0 1px 5px 0 rgba(204, 74, 61, 0.2);
          box-shadow: 0 2px 2px 0 rgba(204, 74, 61, 0.14), 0 3px 1px -2px rgba(204, 74, 61, 0.12), 0 1px 5px 0 rgba(204, 74, 61, 0.2);
  height: 80px;
  margin-top: 1rem;
  border-radius: 3.5rem;
  color: #fff;
  position: relative;
  background: #172d45;
  background: -webkit-gradient(linear, left top, right bottom, color-stop(0%, #315f91), color-stop("500%", #172d45));
  background: -webkit-linear-gradient("90deg", #315f91 0%, #172d45 "500%");
  background: -o-linear-gradient("90deg", #315f91 0%, #172d45 "500%");
  background: ms-linear-gradient("90deg", #315f91 0%, #172d45 "500%");
  background: -webkit-gradient(linear, left top, right top, from(#315f91), to(#172d45));
  background: -webkit-linear-gradient(left, #315f91 0%, #172d45 "500%");
  background: -o-linear-gradient(left, #315f91 0%, #172d45 "500%");
  background: linear-gradient("90deg", #315f91 0%, #172d45 "500%");
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#315f91, endColorstr=#172d45);
}
.wizlet.wizletGauges nav.gauges .statusBar:before {
  content: "";
  display: block;
  position: absolute;
  border-radius: 3.5rem;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/statusBar/BG.svg");
  -webkit-background-size: cover;
          background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge {
  position: relative;
  text-align: center;
  padding: 0;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .score-container {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .score-container div.totalmax {
  margin-left: 0.5rem;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .score-container div.totalmax > span {
  font-size: 80%;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge span {
  font-size: 1rem;
  line-height: 1.2rem;
  color: #fff !important;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge span.gauge-label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: -10px;
  display: block;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge span.gauge-label:after {
  content: none;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge span.gauge-label.total {
  font-weight: bold;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .gauge-value {
  position: absolute;
  top: 0;
  height: 25px;
  left: 50%;
  -webkit-transform: translateX(-50%) translateY(50%);
      -ms-transform: translateX(-50%) translateY(50%);
       -o-transform: translateX(-50%) translateY(50%);
          transform: translateX(-50%) translateY(50%);
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .gauge-value span {
  font-size: 1rem;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .gauge-value.percentage span:after {
  content: " %";
  display: inline-block;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .highcharts-container {
  width: 80px !important;
  height: 60px !important;
  margin: auto;
  margin-top: 0px;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .highcharts-container > svg > desc, .wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .highcharts-container > svg > defs {
  display: none;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .highcharts-container .highcharts-series > path {
  fill: #24466b;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .highcharts-container .highcharts-axis-labels.highcharts-yaxis-labels text {
  display: none;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .gaugeContainer.container0 .highcharts-series > path {
  fill: #F44336;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .gaugeContainer.container1 .highcharts-series > path {
  fill: #E53935;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .gaugeContainer.container2 .highcharts-series > path {
  fill: #D32F2F;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .gaugeContainer.container3 .highcharts-series > path {
  fill: #C62828;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .gauge .gaugeContainer.container4 .highcharts-series > path {
  fill: #B71C1C;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .yourMetrics {
  padding: 0;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .yourMetrics .gauge {
  width: 100%;
  padding: 0;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .yourMetrics .gauge span {
  vertical-align: -webkit-baseline-middle;
  font-size: 1.2rem;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .yourMetrics .gauge span:after {
  display: none;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .yourMetrics .gauge span.gauge-label {
  margin-top: 0;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .otherMetrics {
  padding: 0;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .otherMetrics .textContainer {
  display: inline-block;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .otherMetrics .textContainer ~ .gauge-value {
  margin-top: 5px;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .otherMetrics .textContainer ~ .gauge-value span {
  white-space: nowrap;
  font-size: 1.5rem;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .otherMetrics .textContainer ~ .gauge-label {
  margin-top: 6px;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .more-info {
  position: absolute;
  right: 1rem;
  top: 1rem;
  cursor: pointer;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .more-info:hover {
  -webkit-transform: scale(1.2);
      -ms-transform: scale(1.2);
       -o-transform: scale(1.2);
          transform: scale(1.2);
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .more-info i {
  font-size: 2rem;
}
.wizlet.wizletGauges nav.gauges .statusBar .top_gauges .more-info i.bounce {
  -o-animation-name: bounceInfinite;
  -webkit-animation-name: bounceInfinite;
  animation-name: bounceInfinite;
  -o-animation-duration: 5s;
  -webkit-animation-duration: 5s;
  animation-duration: 5s;
  -o-animation-delay: 2s;
  -webkit-animation-delay: 2s;
  animation-delay: 2s;
  -o-animation-iteration-count: 1;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -o-animation-timing-function: ease;
  -webkit-animation-timing-function: ease;
  animation-timing-function: ease;
  -o-animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}
.wizlet.wizletGauges nav.gauges.on-the-side {
  position: absolute;
  width: 100px;
}
.wizlet.wizletGauges nav.gauges.on-the-side .statusBar {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  margin-top: 0px;
  background: black;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #24466b), color-stop(100%, black));
  background: -webkit-linear-gradient(top, #24466b 0%, black 100%);
  background: -o-linear-gradient(top, #24466b 0%, black 100%);
  background: ms-linear-gradient(to bottom, #24466b 0%, black 100%);
  background: -webkit-gradient(linear, left top, left bottom, from(#24466b), to(black));
  background: linear-gradient(to bottom, #24466b 0%, black 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#24466b, endColorstr=black);
}
.wizlet.wizletGauges nav.gauges.on-the-side .statusBar:before {
  content: none;
}
.wizlet.wizletGauges nav.gauges.on-the-side.left {
  left: 0;
}
.wizlet.wizletGauges nav.gauges.on-the-side.left .statusBar {
  border-bottom-left-radius: 0;
  padding-right: 20px;
}
.wizlet.wizletGauges nav.gauges.on-the-side.right {
  right: 0;
}
.wizlet.wizletGauges nav.gauges.on-the-side.right .statusBar {
  border-bottom-right-radius: 0;
  padding-left: 20px;
}
.wizlet.wizletGauges .sticky.row {
  height: 80px;
}
.wizlet.wizletGauges .sticky.row nav.gauges {
  position: fixed;
  width: 90%;
  left: 5%;
}
.wizlet.wizletGauges .sticky.row nav.gauges .statusBar {
  margin-top: 0;
}
.wizlet.wizletGauges .sticky.row nav.gauges .statusBar, .wizlet.wizletGauges .sticky.row nav.gauges .statusBar:before {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}