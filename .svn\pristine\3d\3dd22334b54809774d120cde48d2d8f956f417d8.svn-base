.wizlet.wizletMultipleActionsLoader > .row.fixed {
  float: none !important;
}
.wizlet.wizletMultipleActionsLoader > .row.fixed.left {
  text-align: left;
}
.wizlet.wizletMultipleActionsLoader > .row.fixed.center {
  text-align: center;
}
.wizlet.wizletMultipleActionsLoader > .row.fixed.right {
  text-align: right;
}
.wizlet.wizletMultipleActionsLoader .next-btn-container {
  -webkit-transition: visibility 0.5s, opacity 1s linear;
  -o-transition: visibility 0.5s, opacity 1s linear;
  transition: visibility 0.5s, opacity 1s linear;
}
.wizlet.wizletMultipleActionsLoader .next-btn-container.hidden {
  visibility: hidden;
  opacity: 0;
}
.wizlet.wizletMultipleActionsLoader .next-btn-container .instructions {
  color: #cc4a3d;
}
.wizlet.wizletMultipleActionsLoader .next-btn-container .next-btn {
  font-size: inherit;
  margin-top: 10px;
}