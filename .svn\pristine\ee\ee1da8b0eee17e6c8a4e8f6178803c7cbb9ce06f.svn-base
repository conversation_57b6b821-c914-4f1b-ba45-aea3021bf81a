.wizlet.wizletRollingRoulette .card .card-content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
      -ms-flex-direction: column;
          flex-direction: column;
  background-image: url("../images/casino/diceBoard.jpg");
  -webkit-background-size: cover;
          background-size: cover;
}
.wizlet.wizletRollingRoulette .card .card-content .row {
  margin: 0;
}
.wizlet.wizletRollingRoulette .card .card-content.borders {
  border-width: 10px;
  border-color: #cc4a3d;
  border-radius: 10px;
  float: none !important;
}
.wizlet.wizletRollingRoulette .card .card-content.borders.top {
  border-top-style: outset;
}
.wizlet.wizletRollingRoulette .card .card-content.borders.right {
  border-right-style: outset;
}
.wizlet.wizletRollingRoulette .card .card-content.borders.left {
  border-left-style: outset;
}
.wizlet.wizletRollingRoulette .card .card-content.borders.bottom {
  border-bottom-style: outset;
}
.wizlet.wizletRollingRoulette #roulette.spinner {
  float: left;
  position: relative;
  background-color: #fff;
  height: 25em;
  width: 25em;
  border-radius: 25em;
  border: solid 2em rgb(41, 25, 6);
  -webkit-box-shadow: 0 0 1em rgb(42, 40, 40);
          box-shadow: 0 0 1em rgb(42, 40, 40);
  font-size: 1.75vh;
  margin-bottom: 3rem;
}
.wizlet.wizletRollingRoulette #roulette .ball {
  position: absolute;
  position: absolute;
  z-index: 80;
  width: 2em;
  height: 18.5em;
  left: 11.5em;
  top: 3.2em;
  -webkit-transform: rotate(360deg);
      -ms-transform: rotate(360deg);
       -o-transform: rotate(360deg);
          transform: rotate(360deg);
}
.wizlet.wizletRollingRoulette #roulette .ball span {
  display: block;
  background-color: rgb(255, 255, 255);
  border-radius: 50%;
  width: 1.5em;
  height: 1.5em;
  margin: auto;
  -webkit-box-shadow: inset -0.5em -0.5em 1em rgb(183, 183, 183), 0 0 2em #000;
          box-shadow: inset -0.5em -0.5em 1em rgb(183, 183, 183), 0 0 2em #000;
}
.wizlet.wizletRollingRoulette #roulette .platebg {
  position: absolute;
  z-index: 50;
  background: rgb(51, 29, 1);
  width: 19em;
  height: 19em;
  border-radius: 19em;
  left: 3em;
  top: 3em;
  -webkit-box-shadow: inset 0 0 1em rgb(0, 0, 0);
          box-shadow: inset 0 0 1em rgb(0, 0, 0);
}
.wizlet.wizletRollingRoulette #roulette .platetop {
  position: absolute;
  background: none repeat scroll 0% 0% rgb(237, 217, 167);
  width: 15em;
  height: 15em;
  z-index: 60;
  border-radius: 15em;
  left: 5em;
  top: 5em;
  -webkit-box-shadow: 0px 0px 0.6em rgba(0, 0, 0, 0.3), -1.5em -1.5em 4em rgb(90, 71, 17) inset;
          box-shadow: 0px 0px 0.6em rgba(0, 0, 0, 0.3), -1.5em -1.5em 4em rgb(90, 71, 17) inset;
}
.wizlet.wizletRollingRoulette #roulette .topnodebox {
  position: absolute;
  left: 9.9em;
  top: 9.9em;
  z-index: 150;
}
.wizlet.wizletRollingRoulette #roulette .topnodebox .silvernode {
  width: 2em;
  height: 2em;
  position: absolute;
  z-index: 110;
  border-radius: 2em;
  left: 1.5em;
  top: 1.5em;
  border: solid 0.1em rgb(189, 189, 189);
  -webkit-box-shadow: 0 0 1em rgba(0, 0, 0, 0.5);
          box-shadow: 0 0 1em rgba(0, 0, 0, 0.5);
  background: #e2e2e2;
  background: -webkit-gradient(linear, left bottom, right top, color-stop(0%, #e2e2e2), color-stop(50%, #dbdbdb), color-stop(51%, #d1d1d1), color-stop(100%, #fefefe));
  background: -webkit-linear-gradient(45deg, #e2e2e2 0%, #dbdbdb 50%, #d1d1d1 51%, #fefefe 100%);
  background: -o-linear-gradient(45deg, #e2e2e2 0%, #dbdbdb 50%, #d1d1d1 51%, #fefefe 100%);
  background: ms-linear-gradient(45deg, #e2e2e2 0%, #dbdbdb 50%, #d1d1d1 51%, #fefefe 100%);
  background: linear-gradient(45deg, #e2e2e2 0%, #dbdbdb 50%, #d1d1d1 51%, #fefefe 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=$color1, endColorstr=$color4,GradientType=1 );
}
.wizlet.wizletRollingRoulette #roulette .topnodebox .topnode {
  position: relative;
  width: 5em;
  height: 5em;
  z-index: 100;
  border-radius: 10em;
  border: 0.1em solid #A8A8A8;
  -webkit-box-shadow: 0px 0px 2em rgba(0, 0, 0, 0.5);
          box-shadow: 0px 0px 2em rgba(0, 0, 0, 0.5);
}
.wizlet.wizletRollingRoulette #roulette .topnodebox span {
  position: absolute;
  z-index: 90;
  border-radius: 0.5em;
  border: solid 0.1em rgb(168, 168, 168);
  -webkit-box-shadow: 0 0 2em rgba(0, 0, 0, 0.5);
          box-shadow: 0 0 2em rgba(0, 0, 0, 0.5);
}
.wizlet.wizletRollingRoulette #roulette .topnodebox span.top {
  height: 5em;
  width: 1em;
  left: 2em;
  top: -4em;
}
.wizlet.wizletRollingRoulette #roulette .topnodebox span.right {
  height: 1em;
  width: 5em;
  top: 2em;
  left: 4em;
}
.wizlet.wizletRollingRoulette #roulette .topnodebox span.down {
  height: 5em;
  width: 1em;
  left: 2em;
  top: 4em;
}
.wizlet.wizletRollingRoulette #roulette .topnodebox span.left {
  height: 1em;
  width: 5em;
  top: 2em;
  left: -4em;
}
.wizlet.wizletRollingRoulette #roulette .topnodebox .silverbg {
  background: #e2e2e2;
  background: -webkit-gradient(linear, left bottom, right top, color-stop(0%, #e2e2e2), color-stop(50%, #dbdbdb), color-stop(51%, #d1d1d1), color-stop(100%, #fefefe));
  background: -webkit-linear-gradient(-45deg, #e2e2e2 0%, #dbdbdb 50%, #d1d1d1 51%, #fefefe 100%);
  background: -o-linear-gradient(-45deg, #e2e2e2 0%, #dbdbdb 50%, #d1d1d1 51%, #fefefe 100%);
  background: ms-linear-gradient(-45deg, #e2e2e2 0%, #dbdbdb 50%, #d1d1d1 51%, #fefefe 100%);
  background: -webkit-linear-gradient(135deg, #e2e2e2 0%, #dbdbdb 50%, #d1d1d1 51%, #fefefe 100%);
  background: -o-linear-gradient(135deg, #e2e2e2 0%, #dbdbdb 50%, #d1d1d1 51%, #fefefe 100%);
  background: linear-gradient(-45deg, #e2e2e2 0%, #dbdbdb 50%, #d1d1d1 51%, #fefefe 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=$color1, endColorstr=$color4,GradientType=1 );
}
.wizlet.wizletRollingRoulette #roulette .pieContainer {
  width: 20em;
  height: 20em;
  position: relative;
  -webkit-transform: rotate(0deg);
      -ms-transform: rotate(0deg);
       -o-transform: rotate(0deg);
          transform: rotate(0deg);
  font-size: 1.25em;
}
.wizlet.wizletRollingRoulette #roulette .pieContainer .pieBackground {
  background-color: rgb(240, 240, 240);
  position: absolute;
  width: 20em;
  height: 20em;
  border-radius: 20em;
  -webkit-box-shadow: -1px 1px 3px #000;
          box-shadow: -1px 1px 3px #000;
}
.wizlet.wizletRollingRoulette #roulette .pieContainer .hold {
  position: absolute;
  clip: rect(0, 20em, 20em, 10em);
  width: 20em;
  height: 20em;
  border-radius: 20em;
  z-index: 20;
}
.wizlet.wizletRollingRoulette #roulette .pieContainer .hold .num {
  position: absolute;
  top: 0.4em;
  color: rgb(255, 255, 255);
  z-index: 40;
  left: 10.28em;
  text-align: center;
  font-family: "Times New Roman", Times, serif;
  font-weight: 700;
}
.wizlet.wizletRollingRoulette #roulette .pieContainer .hold .pie {
  -webkit-box-shadow: inset 0 0 1em #000;
          box-shadow: inset 0 0 1em #000;
  border: solid 0.1em #FFF;
  position: absolute;
  width: 20em;
  height: 20em;
  border-radius: 20em;
  clip: rect(0px, 10em, 10em, 0px);
  z-index: 30;
}
.wizlet.wizletRollingRoulette #roulette .pieContainer .hold .pie.redbg {
  background: #a90329;
  background: -webkit-gradient(linear, left bottom, right top, color-stop(0%, #a90329), color-stop(44%, #8f0222), color-stop(100%, #6d0019));
  background: -webkit-linear-gradient(-45deg, #a90329 0%, #8f0222 44%, #6d0019 100%);
  background: -o-linear-gradient(-45deg, #a90329 0%, #8f0222 44%, #6d0019 100%);
  background: ms-linear-gradient(-45deg, #a90329 0%, #8f0222 44%, #6d0019 100%);
  background: -webkit-linear-gradient(135deg, #a90329 0%, #8f0222 44%, #6d0019 100%);
  background: -o-linear-gradient(135deg, #a90329 0%, #8f0222 44%, #6d0019 100%);
  background: linear-gradient(-45deg, #a90329 0%, #8f0222 44%, #6d0019 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=$color1, endColorstr=$color3,GradientType=1 );
}
.wizlet.wizletRollingRoulette #roulette .pieContainer .hold .pie.greybg {
  background: #7d7e7d;
  background: -webkit-gradient(radial, center center, 0px, center center, 100%, color-stop(0%, #7d7e7d), color-stop(70%, #000000), color-stop(100%, #0e0e0e));
  background: -webkit-radial-gradient(center, ellipse farthest-corner, #7d7e7d 0%, #000000 70%, #0e0e0e 100%);
  background: -o-radial-gradient(center, ellipse farthest-corner, #7d7e7d 0%, #000000 70%, #0e0e0e 100%);
  background: -webkit-radial-gradient(center, ellipse, #7d7e7d 0%, #000000 70%, #0e0e0e 100%);
  background: -o-radial-gradient(center, ellipse, #7d7e7d 0%, #000000 70%, #0e0e0e 100%);
  background: radial-gradient(ellipse at center, #7d7e7d 0%, #000000 70%, #0e0e0e 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=$color1, endColorstr=$color3,GradientType=1 );
}
.wizlet.wizletRollingRoulette #roulette .pieContainer .hold .pie.grey2bg {
  background: #000000;
  background: -webkit-gradient(linear, left top, right bottom, color-stop(0%, #45484d), color-stop(100%, #000000));
  background: -webkit-linear-gradient(-45deg, #45484d 0%, #000000 100%);
  background: -o-linear-gradient(-45deg, #45484d 0%, #000000 100%);
  background: ms-linear-gradient(-45deg, #45484d 0%, #000000 100%);
  background: -webkit-linear-gradient(135deg, #45484d 0%, #000000 100%);
  background: -o-linear-gradient(135deg, #45484d 0%, #000000 100%);
  background: linear-gradient(-45deg, #45484d 0%, #000000 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr=#45484d, endColorstr=#000000);
}
.wizlet.wizletRollingRoulette #roulette .pieContainer .hold .pie.greenbg {
  background: #bfd255;
  background: -webkit-gradient(linear, left bottom, right top, color-stop(0%, #bfd255), color-stop(50%, #8eb92a), color-stop(51%, #72aa00), color-stop(100%, #9ecb2d));
  background: -webkit-linear-gradient(-45deg, #bfd255 0%, #8eb92a 50%, #72aa00 51%, #9ecb2d 100%);
  background: -o-linear-gradient(-45deg, #bfd255 0%, #8eb92a 50%, #72aa00 51%, #9ecb2d 100%);
  background: ms-linear-gradient(-45deg, #bfd255 0%, #8eb92a 50%, #72aa00 51%, #9ecb2d 100%);
  background: -webkit-linear-gradient(135deg, #bfd255 0%, #8eb92a 50%, #72aa00 51%, #9ecb2d 100%);
  background: -o-linear-gradient(135deg, #bfd255 0%, #8eb92a 50%, #72aa00 51%, #9ecb2d 100%);
  background: linear-gradient(-45deg, #bfd255 0%, #8eb92a 50%, #72aa00 51%, #9ecb2d 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=$color1, endColorstr=$color4,GradientType=1 );
}
.wizlet.wizletRollingRoulette #scoreBox {
  width: 80%;
  margin: auto;
  padding: 12px;
}