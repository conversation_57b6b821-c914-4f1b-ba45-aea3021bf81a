@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
//@import "../materialize-src/sass/components/variables";
@import "mixins";

.wizlet.wizletCardReveal {

    .container {
        padding: 0;
    } 

    .row.no-padding {
        margin: 10px -10px;
        >.col {
            padding: 0 5px;
            >.card {
                margin: 5px 0;
            }
        }
    }

    .row.is-flex {
        @include flexbox();
        @include flex-wrap();
    
        & > .col {
            @include flexbox();
            @include flex-direction(column);
            margin: auto auto;
        }
    }

    
    .container > .header-container {
        position: relative;

        .header.help {
            padding-right: 36px;
        }
        i.help {
            position: absolute;
            right: 0;
            bottom: 0;
            transform: translate(-50%, 0%);

            cursor: pointer;
            &:hover {
                transform: scale(1.1) translate(-50%, 0%);
            }
        }
    }


    .container > .intro {
        padding-bottom: 1rem;
        margin-top: 1rem;
        background-color: color("client-colors", "white") ;
        p {
            font-size: inherit;
        }
    }
    
    .collapsible {
        background-color: color("client-colors", "white") ;

        .collapsible-body {
            padding: 1rem;
            text-align: left;
            
            p {
                font-size: inherit;
            }
        }
    }

    .card.maincard {

        .badge.header-label {
            z-index: 2;
            position: absolute;
            left: 0;
            top: 0;
            background-color: color("client-colors", "secondary");
            min-height: 2rem;
            font-size: 1.5rem;
            line-height: 2rem;
            margin: 0;
        }

        .card-image {
            min-width: 30%;
            max-height: 75%;
            @include no-select-highlight;
        }

        .card-action a {
            font-family: clientBold, Arial;
            cursor: pointer;
            color: color("client-colors", "button") !important; 
            &:hover {
                color: color("client-colors", "aux1") !important; 
                text-decoration: underline;
            }
            font-size: 20px; 
            @media #{$small-and-down} { 
                font-size: 16px; 
            }
        }

        .card-content, 
        .card-reveal {
            @include card-title(100%);
            .card-title.size {
                &.big {font-size: 120%; }
                &.small {font-size: 90%; }
            }
        }
        .card-reveal {
            padding-top: 48px;
        }


        .card-content {
            padding: 5px 12px;            
            // height: 70px;
            overflow: auto;
            .card-title {         
                border-bottom: none;    
                line-height: 2rem;
                word-break: break-word;                
                @media #{$small-and-down} { 
                    font-size: 20px;  
                }
            }            
        }

        .card-action {
            padding: 8px 12px;
            cursor: pointer;
        }

        &.sticky-action {
            .card-reveal {
                @include opacity(0.95);
                p, ul {
                    font-size: inherit;
                }
            }
        }

        
        &.disabled {
            pointer-events: none;
            filter: grayscale(100%);
        }

        
        &.no-reveal {
            .card-content{
                .card-title {
                    text-align: center;
                }
            }
            .card-image, .card-content {
                cursor: pointer;
            }
            .card-image.onHover:hover {
                filter: saturate(200%);
                //transform: scale(1.1);
            }
        }
        
    }


}