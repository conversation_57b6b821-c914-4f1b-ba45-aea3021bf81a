﻿
define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min'], function($, Q, WizerApi, WizletBase, doT) {

    var DropDownButton = function() {
        this.type = 'DropDownButton';
        this.level = 1;
    };

    DropDownButton.prototype.loadHandler = function(unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        if (wizletInfo.templateInEvent) {
            requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        }
        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function(doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });
    };

    DropDownButton.prototype.unloadHandler = function() {
        $('.dropdown-content').remove();
        $('.material-tooltip').remove();
        WizletBase.unloadHandler({ wizlet: this });
    };

    DropDownButton.prototype.render = function(options) {
        var self = this;
        
        return self.templateDefer.promise.then(function(template) {
                var fragment = template(options.wizletInfo);
                options.context.html(fragment);

                if (options.wizletInfo.inactive) {

                } else

                self.dropdown = options.context.find('.dropdown-trigger');

                //after WIZER MODEL is loaded, we can move the component to the mainAire to keep the renders working
                $(document).one('wizer:action:init', function(e, currentAction) {

                    self.dropdown.dropdown({
                    
                        _container: document.body,
                        container: $('.mainAreaContainer'),
    
                        hover: options.wizletInfo.openOnHover ? options.wizletInfo.openOnHover : false,
    
                        constrainWidth: options.wizletInfo.constrainWidth,

                        belowOrigin: true,
                        
                        //onCloseEnd : function(current_item) {
                            //console.log(current_item);
                        //}
                        //reposition Box below the button when available space on screen
                        onOpenEnd: function(current_item) {
                            var $button = $(current_item);
                            var $box = $('#'+$button.data('target'));
                            if ($button.offset().top > $box.offset().top)
                                if ($box.offset().top < ($(window).scrollTop()+$box.height()))
                                    $box.css({ 'top': $box.offset().top + $box.height() - $button.outerHeight() });
                        },
                        _onOpenStart: function(current_item) {
                                // the elements the dropdown should be on top of.
                                $('.card').css('z-index',-1)
                                $('.chip').css('z-index', -1)
                                $('.postImage').css('z-index', -1)
                                $('.parallax-container').css('z-index', -1)
                                $('.collection').css('z-index', -1)
                                $('.btn').css('z-index', -1)
                                $('.header').css('z-index', -1)
                        },
                        _onCloseStart : function(current_item) {
                            // return the elements to there first state so the user can interact with them
                            $('.card').css('z-index', 1)
                            $('.card-title').css('z-index', 1)
                            $('.postImage').css('z-index', 1)
                            $('.parallax-container').css('z-index', 1)
                            $('.collection').css('z-index', 1)
                            $('.btn').css('z-index', 1)
                            $('.header').css('z-index', 1)
                        }
                    });

                });

                $('#'+self.wizletInfo.id+'UL.dropdown-content > li').on("click", function(event) {
                    
                    //set data value
                    var value = $(this).data('value');
                    self.dropdown.attr('data-value', value);
                    self.dropdown.find('span.placeholder').html( $(this).find('span.label').html() )

                    var bind = $(this).data('bind') || $(this).data('binding');
                    self.addVote( bind,  value);

                    //update data model
                    if (self.wizletInfo.saveInModel) {
                        var input = $(this).find('.model-render');
                        input.attr('data-value', value);
                        if (self.wizletInfo.saveInModelLater) {
                            input.addClass('saveInModelLater');
                        } else {
                            input.trigger('updateModelInput');
                        }
                    }

                });


                if (self.wizletInfo.loadStoredValues) {
                    
                    
                    var questionId = self.wizerApi.getQuestionIdByName(self.wizletInfo.bind ? self.wizletInfo.bind : self.wizletInfo.binding);

                    self.wizerApi.getMyVotes([questionId]).then(function (response) {
                        
                        var vote = response.votes[questionId];

                        if (vote & vote[0]) {
                            var value = vote[0];
                            self.dropdown.attr('data-value', value);
                            self.dropdown.find('span.placeholder').html( 
                                $('#'+self.wizletInfo.id+'UL.dropdown-content > li[data-value="'+value+'"]').find('span.label').html()
                            );

                        }

                        if (self.wizletInfo.saveInModel) {
                    
                            if (self.wizletInfo.saveInModelLater) {                                
                                $('#'+self.wizletInfo.id+'UL.dropdown-content > li[data-value="'+value+'"]').find('.model-render').addClass('saveInModelLater');
                            } else {
                                //after WIZER MODEL is loaded, we can update renders
                                $(document).one('wizer:action:init', function(e, currentAction) {                                    
                                    $('#'+self.wizletInfo.id+'UL.dropdown-content > li[data-value="'+value+'"]').find('.model-render').trigger('updateModelInput');                                        
                                });
        
                            }
                        }



                    });
                }



                return true;
            })
            .fail(this.wizerApi.showError)
    };



    
    /**
     * Promise function: Addvote value to a questionName
     */
    DropDownButton.prototype.addVote = function (questionName, val) {
        var self = this;

        var questionId = self.wizerApi.getQuestionIdByName(questionName);

        var defer = new Q.defer();

        var waiting = self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});

        waiting.then(function (result) {            
            defer.resolve(result);
        });                    
        
        return defer.promise;
    };


    DropDownButton.getRegistration = function() {
        return new DropDownButton();
    };

    return DropDownButton;

});