.wizlet.wizletDropDownButton .card .card-content.card-content {
  background-color: #FFFFFF;
  border-radius: 10px;
}
.wizlet.wizletDropDownButton .card .card-content .card-title {
  font-family: clientBolder, clientBold, Arial;
  font-size: 150%;
  line-height: 100%;
  text-align: left;
  padding-bottom: 5px;
  border-bottom: 3px solid #cc4a3d;
  margin-bottom: 15px;
}
.wizlet.wizletDropDownButton .card .card-content .card-title.activator, .wizlet.wizletDropDownButton .card .card-content .card-title.reveal {
  position: relative;
}
.wizlet.wizletDropDownButton .card .card-content .card-title.activator i.material-icons.right, .wizlet.wizletDropDownButton .card .card-content .card-title.reveal i.material-icons.right {
  position: absolute;
  top: 0;
  right: -5px;
}
.wizlet.wizletDropDownButton .card .card-content .dropdown .option > .header {
  padding-right: 0;
}
.wizlet.wizletDropDownButton .card .card-content .dropdown .button .dropdown-trigger.btn {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  height: 100%;
  width: 100%;
}
.wizlet.wizletDropDownButton .card .card-content .dropdown .button .dropdown-trigger.btn > span, .wizlet.wizletDropDownButton .card .card-content .dropdown .button .dropdown-trigger.btn > i {
  padding: 0;
}
.wizlet.wizletDropDownButton .card .card-content .dropdown .button .dropdown-trigger.btn.disabled {
  opacity: 1;
  pointer-events: none;
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.wizlet.wizletDropDownButton .card .card-content .dropdown .button ul.dropdown-content {
  -webkit-transform-origin: 0px 0px !important;
      -ms-transform-origin: 0px 0px !important;
       -o-transform-origin: 0px 0px !important;
          transform-origin: 0px 0px !important;
}
.wizlet.wizletDropDownButton .card .card-content .dropdown .button ul.dropdown-content > li .model-render {
  display: none;
}

.mainAreaContainer ul.dropdown-content {
  -webkit-transform-origin: 0px 0px !important;
      -ms-transform-origin: 0px 0px !important;
       -o-transform-origin: 0px 0px !important;
          transform-origin: 0px 0px !important;
}
.mainAreaContainer ul.dropdown-content > li .model-render {
  display: none;
}