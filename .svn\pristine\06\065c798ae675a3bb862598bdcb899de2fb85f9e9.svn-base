
<header class="navbar-fixed{{?it.ibiderEmbed}} ibiderEmbed{{?}}">

    <nav class="client-colors navheader" role="navigation">
        <div class="nav-wrapper">
            {{?it.logo && it.logo.src}}
            <a id="logo-container" class="brand-logo center">
                <img src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.logo.src}}" alt="{{=it.logo.alt}}"/>
            </a>
            {{?}}
            
            <!-- No Mobile TOPMEMU -->
            <ul class="left hide-on-small-and-down">
                
                {{?it.user.my_avatar && it.DB[it.user.my_avatar]}}
                <li>
                    <img class="circle avatar" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}/images/avatar/{{=it.DB[it.user.my_avatar]}}">
                </li>
                {{?}}
                
                <li>
                    <span class="participantName client-colors-text text-navcolor name">
                        {{? it.DB[it.myName] }}
                            {{= it.DB[it.myName] }}
                        {{??}}
                            {{=Wizer.ParticipantName}}{{?it.DB[it.myTeam]}} - {{=it.DB[it.myTeam]}}{{?}}
                        {{?}}
                    </span>
                </li>
            </ul>

            <!-- No Mobile TOPMEMU -->
            <ul class="right hide-on-small-and-down">

                {{?it.links}}
                {{~it.links :link}}
                    
                    {{?link.section}}
                        {{?link.sectionID}}
                        <li>
                            <a class="dropdown-trigger" data-target="{{=link.sectionID}}">
                                {{?link.sectionIcon}}
                                    <i class="medium material-icons left no-mar-right">{{=link.sectionIcon}}</i>
                                    <i class="material-icons right no-mar-left">arrow_drop_down</i>
                                {{??}}
                                    {{=link.sectionTitle}}
                                    <i class="material-icons right">arrow_drop_down</i>
                                {{?}}
                            </a>
                        </li>
                        {{?}}
                    {{??}}

                        {{? !link.divider}}
                        <li class="tooltipped theader" data-position="bottom" data-tooltip="{{=link.title}}">
                            <a class="{{?link.modalID || link.popup}}modal-trigger{{?}}{{?link.disabled}} disabled{{?}}"
                                {{?link.onclick}} onclick="{{=link.onclick}}"{{?}}
                                {{?link.popup}}href="#{{=link.popupID}}" data-modal="{{=link.popup}}"{{?}}
                                {{?link.targetSection}}data-action="{{=link.targetSection}}"{{?}}
                                {{?link.gdActionEmbed}}data-gdembed="{{=link.gdActionEmbed}}" data-index="{{=idx}}"{{?}}
                                {{?link.gdActionTrack}}data-gdtrack="{{=link.gdActionTrack}}"{{?}}
                                {{?link.modalID}}href="#{{=link.modalID}}"{{?}}>
                                {{?link.icon}}
                                    {{?link.showTitle}}
                                        {{=link.title}}<i class="medium material-icons right">{{=link.icon}}</i>
                                    {{??}}
                                        <i class="medium material-icons">{{=link.icon}}</i>
                                    {{?}}
                                {{??}}                                  
                                    {{=link.title}}
                                {{?}}
                            </a>
                        </li>
                        {{?}}
                    {{?}}
                    {{?link.gdActionEmbed}}
                    <div class="embeddingWrapper" id="embedding-{{=idx}}" data-embed></div>
                    {{?}} 

                {{~}}
                {{?}}
            </ul>


            <!-- Mobile SIDENAV -->
            <ul id="nav-mobile" class="sidenav">    
                
                <li>
                    <div class="user-view">
                        {{?it.user.background}}
                        <div class="background">
                            <img src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.user.background}}">
                        </div>
                        {{?}}
                        {{?it.user.my_avatar && it.DB[it.user.my_avatar]}}
                            <img class="circle" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}/images/avatar/{{=it.DB[it.user.my_avatar]}}">
                        {{??}}
                            <img class="" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=it.user.avatar}}">
                        {{?}}
                        <span class="participantName client-colors-text text-font2 name">
                            {{? it.DB[it.myName] }}
                                {{= it.DB[it.myName] }}
                            {{??}}
                                {{=Wizer.ParticipantName}}{{?it.DB[it.myTeam]}} - {{=it.DB[it.myTeam]}}{{?}}
                            {{?}}
                        </span>
                    </div>
                </li>

                {{?it.links}}
                {{~it.links :link}}

                    {{?link.sectionTitle}}
                        <li><a class="subheader">{{=link.sectionTitle}}</a></li>                    
                    {{?}}

                    {{? !link.divider}}
                    <li>
                        <a class="{{?link.modalID || link.popup}}modal-trigger{{?}}{{?link.disabled}} disabled{{?}}"
                            {{?link.onclick}} onclick="{{=link.onclick}}"{{?}}
                            {{?link.onclickFunction}}data-function="{{=link.onclickFunction}}"{{?}} 
                            {{?link.onclickQuestion}}data-question="{{=link.onclickQuestion}}"{{?}} 
                            {{?link.popup}}href="#{{=link.popupID}}" data-modal="{{=link.popup}}"{{?}}
                            {{?link.targetSection}}data-action="{{=link.targetSection}}"{{?}}
                            {{?link.gdActionEmbed}}data-gdembed="{{=link.gdActionEmbed}}" data-index="{{=idx}}"{{?}}
                            {{?link.gdActionTrack}}data-gdtrack="{{=link.gdActionTrack}}"{{?}}
                            {{?link.modalID}}href="#{{=link.modalID}}"{{?}}>
                            {{?link.icon}}
                                <i class="medium material-icons">{{=link.icon}}</i>
                            {{?}}
                            
                            {{?link.image}}
                                <img class="" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=link.image}}">
                            {{?}}
                            
                            {{=link.title}}
                        </a>
                    </li>              
                    {{?}}
                    
                    {{?link.sectionLast || link.divider}}
                        <li><div class="divider"></div></li>
                    {{?}}
                    {{?link.gdActionEmbed}}
                    <div class="embeddingWrapper" id="embedding-{{=idx}}" data-embed></div>
                    {{?}} 
                {{~}}
                {{?}}                
            </ul>
            <a data-target="nav-mobile" class="sidenav-trigger"><i class="material-icons">menu</i></a>

            
        </div>
    </nav>


    {{?it.sidePanel}}
    <ul id="sidenav-panel" class="sidenav panel sidenav-fixed animated fadeInLeft">
        {{~it.sidePanel.items :item:idx}}
        <li>
            <a id="sidenav-panel{{=idx+1}}"
                class="{{?item.active}}on{{??}}off{{?}} {{?item.popup && item.popupID}}modal-trigger{{?}}"
                {{?item.popup && item.popupID}}href="#{{=item.popupID}}" data-modal="{{=item.popup}}"{{?}}>
                {{?item.imgON}}
                    <img class="on" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=item.imgON}}" alt="item{{=idx}}"/>
                    <img class="off" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=item.img}}" alt="item{{=idx}}"/>
                {{??}}
                    {{?item.img}}<img class="" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=item.img}}" alt="item{{=idx}}"/>{{?}}
                {{?}}
                {{?item.lab}}<figcaption class="flow-text">{{=item.lab}}</figcaption>{{?}}
            </a>
        </li>
        {{~}}
    </ul>
    {{?}}
            

</header>


<!-- ************************************** -->
<!-- *********  DROPDOWN SECTION  ********* -->
<!-- ************************************** -->
{{?it.links}}
{{~it.links :link}}
    {{?link.sectionID}}
    <ul id="{{=link.sectionID}}" class="dropdown-content">
    {{?}}
        {{?link.section}}                
            <li>
                <a class="{{?link.modalID || link.popup}}modal-trigger{{?}}{{?link.disabled}} disabled{{?}}"
                    {{?link.onclick}} onclick="{{=link.onclick}}"{{?}}
                    {{?link.onclickFunction}}data-function="{{=link.onclickFunction}}"{{?}} 
                    {{?link.onclickQuestion}}data-question="{{=link.onclickQuestion}}"{{?}} 
                    {{?link.popup}}href="#{{=link.popupID}}" data-modal="{{=link.popup}}"{{?}}
                    {{?link.targetSection}}data-action="{{=link.targetSection}}"{{?}}
                    {{?link.gdActionEmbed}}data-gdembed="{{=link.gdActionEmbed}}" data-index="{{=idx}}"{{?}}
                    {{?link.gdActionTrack}}data-gdtrack="{{=link.gdActionTrack}}"{{?}}
                    {{?link.modalID}}href="#{{=link.modalID}}"{{?}}>
                    {{?link.icon}}
                        <i class="medium material-icons">{{=link.icon}}</i>
                    {{?}}
                    
                    {{?link.image}}
                    <img class="left" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=link.image}}">
                    {{?}}
                    {{=link.title}}
                </a>
            </li>
        {{?}}
        {{?link.divider}}
            <li class="divider"></li>
        {{?}}
    {{?link.sectionLast}}
    </ul>  
    {{?}}
    {{?link.gdActionEmbed}}
    <div class="embeddingWrapper" id="embedding-{{=idx}}" data-embed></div>
    {{?}} 
{{~}}
{{?}}       






<!-- ************************************** -->
<!-- *****  MODALS: Teams, More info  ***** -->
<!-- ************************************** -->
{{?it.sectionsID}}
    {{~it.sectionsID :modalID}}
    <div id="{{=modalID}}" class="modal large">
        <div class="modal-content"> </div>
        <div class="modal-footer">
            <a class="btn modal-close client-colors button2">
                <i class="small material-icons right">close</i>{{=it.close}}
            </a>
        </div>
    </div>
    {{~}}
{{?}}




<!-- *********************************** -->
<!-- *********  HELP-MODAL  ********* -->
<!-- *********************************** -->
{{?it.help}}
<div id="{{=it.help.modalID}}" class="modal large">
    <div class="modal-content">
        <span class="flow-text">{{=it.help.text}}</span>
    </div>
    <div class="modal-footer">
        <a class="btn modal-close waves-effect waves-green client-colors button2">
            <i class="small material-icons right">close</i>{{=it.help.close}}
        </a>
    </div>
</div>
{{?}}


<!-- ********************************** -->
<!-- *********  LOGOUT-MODAL  ********* -->
<!-- ********************************** -->
{{?it.logout}}
<div id="{{=it.logout.modalID}}" class="modal">
    <div class="modal-content">
        <h4>{{=it.logout.header}}</h4>
        <span class="flow-text">{{=it.logout.text}}</span>
    </div>
    <div class="modal-footer">
        <a class="btn modal-close client-colors button2">
            <i class="small material-icons right">close</i>{{=it.logout.close}}
        </a>
        <a  {{?it.logout.onclick}}onclick="{{=it.logout.onclick}}"{{?}} 
            {{?it.logout.onclickFunction}}data-function="{{=it.logout.onclickFunction}}"{{?}} 
            {{?it.logout.onclickQuestion}}data-question="{{=it.logout.onclickQuestion}}"{{?}} 
            class="btn client-colors red">
            <i class="small material-icons right">exit_to_app</i>{{=it.logout.logout}}
        </a>
    </div>
</div>
{{?}}      


