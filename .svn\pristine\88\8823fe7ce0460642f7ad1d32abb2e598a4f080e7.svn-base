.wizlet.wizletMatchable .row.table.header {
  margin: 0;
}
.wizlet.wizletMatchable ul .option-image, .wizlet.wizletMatchable ul ul.right-list, .wizlet.wizletMatchable ul ul.left-list {
  padding: 0;
}
.wizlet.wizletMatchable ul .option-image img, .wizlet.wizletMatchable ul ul.right-list img, .wizlet.wizletMatchable ul ul.left-list img {
  height: 100%;
  width: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}
@media only screen and (max-width : 992px) {
  .wizlet.wizletMatchable ul.left-list {
    padding: 0;
  }
}
.wizlet.wizletMatchable ul.left-list .col {
  height: 100%;
}
.wizlet.wizletMatchable ul.left-list li {
  padding: 5px 0;
  margin-right: 5px;
}
.wizlet.wizletMatchable ul.left-list li .row {
  height: 100%;
  margin: 0;
  padding: 5px;
}
.wizlet.wizletMatchable ul.left-list li .with-help {
  position: relative;
  padding-left: 3px;
  padding-right: 28px;
}
.wizlet.wizletMatchable ul.left-list li .with-help a.help-icon {
  color: #ffffff;
  position: absolute;
  right: 2px;
  top: -1px;
}
.wizlet.wizletMatchable ul.left-list li .with-help a.help-icon.bounce {
  -o-animation-name: bounceInfinite;
  -webkit-animation-name: bounceInfinite;
  animation-name: bounceInfinite;
  -o-animation-duration: 10s;
  -webkit-animation-duration: 10s;
  animation-duration: 10s;
  -o-animation-delay: 5s;
  -webkit-animation-delay: 5s;
  animation-delay: 5s;
  -o-animation-iteration-count: 1;
  -webkit-animation-iteration-count: 1;
  animation-iteration-count: 1;
  -o-animation-timing-function: ease;
  -webkit-animation-timing-function: ease;
  animation-timing-function: ease;
  -o-animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}
@media only screen and (max-width : 992px) {
  .wizlet.wizletMatchable ul.right-list {
    padding: 0;
  }
}
.wizlet.wizletMatchable ul.right-list li {
  padding: 5px 0;
  margin-left: 5px;
}
.wizlet.wizletMatchable ul.right-list li .matchable {
  padding-right: 0;
}
.wizlet.wizletMatchable ul.right-list li ul {
  padding-left: 15px;
}
.wizlet.wizletMatchable ul.right-list li ul > li {
  padding: 0;
}
.wizlet.wizletMatchable ul.header li {
  font-size: 100%;
  text-align: center;
  padding: 5px 10px;
}
.wizlet.wizletMatchable ul li.ui-state-highlight {
  background-color: #ededed !important;
}
.wizlet.wizletMatchable ul .icon {
  position: relative;
  padding: 0;
}
.wizlet.wizletMatchable ul .icon i.check-icon {
  position: absolute;
  z-index: 1;
  -webkit-transform: translate(50%, -50%);
      -ms-transform: translate(50%, -50%);
       -o-transform: translate(50%, -50%);
          transform: translate(50%, -50%);
}
.wizlet.wizletMatchable ul .icon i.check-icon.left {
  left: 0;
}
.wizlet.wizletMatchable ul .icon i.check-icon.right {
  right: 15px;
}
.wizlet.wizletMatchable ul.solution {
  pointer-events: none;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletMatchable .card-panel {
    padding: 5px 5px;
  }
}
.wizlet.wizletMatchable .card-panel.matchable {
  cursor: -webkit-grab;
}
.wizlet.wizletMatchable .card-panel.matchable:active {
  cursor: -webkit-grabbing;
}
.wizlet.wizletMatchable .card-panel.matchable.disabled {
  pointer-events: none;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletMatchable .card-panel .text {
    font-size: 80%;
  }
}
.wizlet.wizletMatchable .card-panel .title {
  word-break: break-word;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletMatchable .card-panel .title {
    font-size: 80%;
  }
}
.wizlet.wizletMatchable .center li.left-item > div.col {
  padding-bottom: 10px;
}
@media only screen and (min-width : 601px) {
  .wizlet.wizletMatchable .big li.left-item > div.col {
    font-size: 125%;
  }
  .wizlet.wizletMatchable .big li.right-item .text {
    font-size: 100%;
  }
}
.wizlet.wizletMatchable .row.submit {
  padding-top: 5px;
  margin-right: 0;
  text-align: right;
}
.wizlet.wizletMatchable .row.submit #checkBtn[hidden] {
  display: inline-block !important;
  visibility: hidden;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletMatchable .row.submit .btn {
    font-size: 0;
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
  .wizlet.wizletMatchable .row.submit .btn i {
    width: inherit;
    display: inline-block;
    text-align: center;
    color: #fff;
    font-size: 1.6rem;
    line-height: 40px;
  }
}

.material-tooltip.timage {
  padding: 5px 20px 20px;
  border-radius: 10px;
  background: rgba(204, 74, 61, 0.9);
  overflow: visible;
  font-size: inherit;
  text-align: initial;
}
@media only screen and (min-width : 601px) {
  .material-tooltip.timage {
    max-width: 45% !important;
  }
}
.material-tooltip.timage.large {
  max-width: 96% !important;
}
@media only screen and (max-width : 600px) {
  .material-tooltip.timage {
    font-size: 1rem;
  }
}
.material-tooltip.timage .tooltip-content,
.material-tooltip.timage .tooltip-content li {
  font-size: 95%;
  line-height: 100%;
}
@media only screen and (min-width : 601px) {
  .material-tooltip.timage .tooltip-content {
    font-size: 100%;
  }
}
@media only screen and (min-width : 993px) {
  .material-tooltip.timage .tooltip-content {
    font-size: 110%;
  }
}
@media only screen and (min-width : 1201px) {
  .material-tooltip.timage .tooltip-content {
    font-size: 120%;
  }
}
.material-tooltip.timage:after {
  position: absolute;
  content: " ";
  margin-left: -8px;
  width: 0;
  height: 0;
  border-style: solid;
}
.material-tooltip.timage.bottom:after {
  top: 0%;
  left: 50%;
  border-width: 0 13px 15px 13px;
  border-color: transparent transparent rgba(204, 74, 61, 0.9) transparent;
  margin-top: -14px;
}
.material-tooltip.timage.top:after {
  top: 100%;
  left: 50%;
  border-width: 15px 13px 0 13px;
  border-color: rgba(204, 74, 61, 0.9) transparent transparent transparent;
  margin-top: -1px;
}
.material-tooltip.timage.right:after {
  top: 25%;
  left: 0%;
  border-width: 13px 15px 13px 0;
  border-color: transparent rgba(204, 74, 61, 0.9) transparent transparent;
  margin-left: -14px;
}
.material-tooltip.timage.left:after {
  top: 25%;
  left: 100%;
  border-width: 13px 0 13px 15px;
  border-color: transparent transparent transparent rgba(204, 74, 61, 0.9);
  margin-left: -1px;
}