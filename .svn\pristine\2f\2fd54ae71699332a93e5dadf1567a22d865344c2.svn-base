@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";
// @import "../materialize-src/sass/components/variables";
@import "mixins";

.wizlet.wizletHCBarChart {

    @include header-badge;

    .card {
        .card-content {

            background-color: color("client-colors", "white");
            
            padding: 6px 12px;
            .row {
                margin: 0;
            }

            .bach-content-stackedColumnChart--container {
                height: auto;
            }
        }
    }


    .highcharts-container {
        
        &>svg {
            width: 100% !important;
        }

        .highcharts-background {
            fill: transparent;
        }

        .highcharts-title {
            font-size: 1.25rem;
        }
        .highcharts-subtitle {
            font-size: 1rem;
        }

        .highcharts-label,
        .highcharts-axis-labels {
            color: initial;
            fill: initial;
            >text, >span {
                font-size: 1rem;
                &:last-child:not([transform]) {
                    transform: translateX(-4px);
                    transform: translateY(2px);
                }
            }
        }
        .highcharts-data-label {
            span {
                color: color("client-colors", "font2");
            }
            text {
                fill: color("client-colors", "font2");
                text-shadow: 1px 1px color("client-colors", "font");
            }
        }

        .highcharts-axis {
            &.highcharts-xaxis {
                .highcharts-axis-title {
                    tspan {
                        font-weight: bold;
                    }
                }
            }
        }

        @for $i from 1 through 12 {
            .highcharts-color-#{$i - 1} {
                fill: color("client-colors", "chart#{$i}");
                stroke: color("client-colors", "chart#{$i}");
            }
        } 
        
        .highcharts-column-series:not(.highcharts-data-labels) {
            @for $i from 1 through 12 {
                rect:nth-child(#{$i - 1}) {
                    fill: color("client-colors", "chart#{$i}");
                    stroke: color("client-colors", "chart#{$i}");
                }
            } 
        }

        // .highcharts-bar-series:not(.highcharts-data-labels) {
        //     @for $i from 1 through 12 {
        //         rect:nth-child(#{$i - 1}) {
        //             fill: color("client-colors", "chart#{$i}");
        //             stroke: color("client-colors", "chart#{$i}");
        //         }
        //     } 
        // }

        .highcharts-tooltip {
            .highcharts-tooltip-box {
                opacity: 1;
            }
            table {
                tr, td {
                    padding: 0;
                }
            }
        }
            
        .highcharts-series-group .highcharts-spline-series {                
            fill: color("client-colors", "border");
            stroke: color("client-colors", "border");
        }
        .highcharts-spline-series {                          
            .highcharts-label {
                >text, >span {
                    font-size: 0.8rem;
                }
            }
        }
    }


    .bach-content-stackedColumnChart--container {

        &:not(.stacked) .highcharts-container {
            .highcharts-bar-series:not(.highcharts-data-labels) {
                @for $i from 1 through 12 {
                    rect:nth-child(#{$i}) {
                        fill: color("client-colors", "chart#{$i}");
                        stroke: color("client-colors", "chart#{$i}");
                    }
                } 
                //More series for the initiatives
                // @for $i from 13 through 25 {
                //     rect:nth-child(#{$i}) {
                //         fill: color("client-colors", "chart#{$i - 12}");
                //         stroke: color("client-colors", "chart#{$i - 12}");
                //     }
                // } 
                // @for $i from 26 through 38 {
                //     rect:nth-child(#{$i}) {
                //         fill: color("client-colors", "chart#{$i - 25}");
                //         stroke: color("client-colors", "chart#{$i - 25}");
                //     }
                // }
            }
        }

        &.chartaux .highcharts-container {
            .highcharts-bar-series:not(.highcharts-data-labels),
            .highcharts-column-series:not(.highcharts-data-labels) {
                @for $i from 1 through 12 {
                    rect:nth-child(#{$i}) {
                        fill: color("client-colors", "chartaux#{$i}");
                        stroke: color("client-colors", "chartaux#{$i}");
                    }
                } 
            }
        }

        &.stacked.farclose .highcharts-container {
            .highcharts-color-0,
            .highcharts-color-0 > .highcharts-point {
                fill: color("client-colors", "red");
                stroke: color("client-colors", "red");
            }
            .highcharts-color-1,
            .highcharts-color-1 > .highcharts-point {
                fill: color("client-colors", "yellow");
                stroke: color("client-colors", "yellow");
            }
            .highcharts-color-2,
            .highcharts-color-2 > .highcharts-point {
                fill: color("client-colors", "green");
                stroke: color("client-colors", "green");
            }
        }
        &.stacked.noyes .highcharts-container {
            .highcharts-color-0,
            .highcharts-color-0 > .highcharts-point {
                fill: color("client-colors", "red");
                stroke: color("client-colors", "red");
            }
            .highcharts-color-1,
            .highcharts-color-1 > .highcharts-point {
                fill: color("client-colors", "green");
                stroke: color("client-colors", "green");
            }
        }
        &.stacked.yesno .highcharts-container {
            .highcharts-color-0,
            .highcharts-color-0 > .highcharts-point {
                fill: color("client-colors", "green");
                stroke: color("client-colors", "green");
            }
            .highcharts-color-1,
            .highcharts-color-1 > .highcharts-point {
                fill: color("client-colors", "red");
                stroke: color("client-colors", "red");
            }
        }
        &.stacked.categories .highcharts-container {

            @for $i from 0 through 6 {                
                .highcharts-color-#{$i},
                .highcharts-color-#{$i} > .highcharts-point {
                    fill: color("client-colors", "categoria#{$i + 1}");
                    stroke: color("client-colors", "categoria#{$i + 1}");
                }
            }  

        }

        &.stacked.aux .highcharts-container {
            @for $i from 0 through 6 {                
                .highcharts-color-#{$i},
                .highcharts-color-#{$i} > .highcharts-point {
                    fill: color("client-colors", "aux#{$i + 1}");
                    stroke: color("client-colors", "aux#{$i + 1}");
                }
            }  

        }

        
        &.line-dashed .highcharts-spline-series { 
            .highcharts-graph{
                stroke-dasharray: 1em;
            }
        }

        &.noclickablelegend {
            .highcharts-legend {
                pointer-events: none;
            }
        }

        &.hide-legend {

            &.legend1 {
                .highcharts-legend {
                    .highcharts-legend-item:nth-child(1) {
                        display: none;
                    }
                    .highcharts-legend-item:nth-child(2) {
                        transform: translateX(50px);
                    }
                }
            }
        }

        &.color {
            @for $i from 1 through 4 {
                &.categoria#{$i} {
                    .highcharts-color-0 {
                        fill: color("client-colors", "categoria#{$i}");
                        stroke: color("client-colors", "categoria#{$i}");
                    }                
                }
            }  
        }

        &.coloured {
            .highcharts-background {
                fill: rgba(color("client-colors","primary"),0.2)
            }

        }

    }

    .answers.card-panel {
        padding: 8px 12px;

        .btn-text {
            //display: inline-block;
            @include vertical-align-middle;
        }
    }
}