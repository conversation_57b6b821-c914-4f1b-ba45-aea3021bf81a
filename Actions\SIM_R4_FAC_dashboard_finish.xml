<?xml version="1.0" encoding="utf-8" ?>
<Action>


  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      subheader: "!{}",
      instructions: "!{}",
      valign: false,
      animate: "fadeIn",
      content: {
        title: "!{GD_SIM_R4_Finish}",
        body: "!{GD_SIM_R4_Finish_Modal}"
      }      
    }]]>
  </Component>


  
  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "",
      id: "navButton",
      isHidden: false, showDelay: "",
      hasModals: true,
      buttons: [
        {
          type: "target",
          pulse: false,
          gdActionCommand: "EmbedWizlet",
          gdActionCommandXML: "SIM_R4_Finish_BackDirector2",
          gdActionEmbed: "",
          gdActionTrack: "GD",
          _gdActionSection: "R4_DebriefPage",
          targetSection: "R4_DebriefPage_FAC",
          label: "!{GD_SIM_R4_Finish}",
          icon: "cancel"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>



</Action>