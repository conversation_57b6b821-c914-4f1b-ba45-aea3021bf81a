.wizlet.wizletHCWebChart .header.with-label {
  position: relative;
  padding-left: 2.5rem;
  margin-bottom: 1rem;
}
.wizlet.wizletHCWebChart .header.with-label .badge.header-label {
  position: absolute;
  left: 0;
  top: 0;
  background-color: #cc4a3d;
  min-width: 2rem;
  min-height: 2rem;
  font-size: 1.25rem;
  font-weight: bold;
  border-radius: 50%;
  line-height: 2rem;
  margin: 0;
  margin-top: -5px;
}
.wizlet.wizletHCWebChart .card .card-content {
  padding: 6px 12px;
}
.wizlet.wizletHCWebChart .card .card-content .row {
  margin: 0;
}
.wizlet.wizletHCWebChart .highcharts-container > svg {
  width: 100% !important;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-label,
.wizlet.wizletHCWebChart .highcharts-container .highcharts-axis-labels {
  color: initial;
  fill: initial;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-xaxis .highcharts-axis-line {
  stroke-width: 0;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-0 {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-1 {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-2 {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-3 {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-4 {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-5 {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-6 {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-7 {
  fill: #2E739E;
  stroke: #2E739E;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-8 {
  fill: #E8CD41;
  stroke: #E8CD41;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-9 {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-10 {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-color-11 {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-tooltip table tr, .wizlet.wizletHCWebChart .highcharts-container .highcharts-tooltip table td {
  padding: 0;
}
.wizlet.wizletHCWebChart .highcharts-container .highcharts-area {
  fill-opacity: 0.5;
}
.wizlet.wizletHCWebChart .answers.card-panel {
  padding: 8px 12px;
}
.wizlet.wizletHCWebChart .answers.card-panel .btn-text {
  display: inline-block;
  vertical-align: sub;
}