@charset "UTF-8";

@import "../materialize-src/sass/components/color-variables";



.wizlet.wizletWordCloud {
    
    margin: 1rem 0 0 0;

    .cl-header--main {
        background: none;
        width: 80%;
        margin: 0 auto;
        padding: 0;
        min-height: 0;

        h1 {
            text-align: left;
            font-size: 1.5rem;
            line-height: 110%;
            // margin: 1rem 0 0.6rem 0;
            font-weight: normal;
            font-family: clientRegular;
            color: color("client-colors","font");
        }
    }

    .cl-container {

        .cl-instructions {
            text-align: left;
        }
        .cl-content {
            background-color: color("client-colors","white");
            padding: 1rem;
            margin-bottom: 2rem;
        }
    }
    
    
}