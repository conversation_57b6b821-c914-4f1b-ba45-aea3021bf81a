.sidenav-overlay {
  background-color: rgba(0, 0, 0, 0.75);
}

.wizlet.wizletHeader > .divider {
  display: none;
}
.wizlet.wizletHeader .embeddingWrapper {
  display: none;
}
.wizlet.wizletHeader header.navbar-fixed {
  z-index: 999;
}
.wizlet.wizletHeader header.navbar-fixed.ibiderEmbed {
  height: 22px;
}
.wizlet.wizletHeader header.navbar-fixed.ibiderEmbed nav.navheader {
  height: 22px;
  -webkit-box-shadow: 0 0 5px #888;
  box-shadow: 0 0 5px #888;
}
.wizlet.wizletHeader header.navbar-fixed.ibiderEmbed nav.navheader .nav-wrapper #logo-container, .wizlet.wizletHeader header.navbar-fixed.ibiderEmbed nav.navheader .nav-wrapper .participantName {
  display: none !important;
}
.wizlet.wizletHeader header.navbar-fixed.ibiderEmbed nav.navheader .nav-wrapper ul.right.hide-on-small-and-down {
  display: block !important;
}
.wizlet.wizletHeader header.navbar-fixed.ibiderEmbed nav.navheader .nav-wrapper a.sidenav-trigger,
.wizlet.wizletHeader header.navbar-fixed.ibiderEmbed nav.navheader .nav-wrapper ul.left.hide-on-small-and-down,
.wizlet.wizletHeader header.navbar-fixed.ibiderEmbed nav.navheader .nav-wrapper #nav-mobile.sidenav {
  display: none !important;
}
.wizlet.wizletHeader header nav {
  -webkit-box-shadow: 0 2px 2px 0 rgba(204, 74, 61, 0.14), 0 3px 1px -2px rgba(204, 74, 61, 0.12), 0 1px 5px 0 rgba(204, 74, 61, 0.2);
          box-shadow: 0 2px 2px 0 rgba(204, 74, 61, 0.14), 0 3px 1px -2px rgba(204, 74, 61, 0.12), 0 1px 5px 0 rgba(204, 74, 61, 0.2);
}
.wizlet.wizletHeader header nav a {
  color: #24466b;
}
.wizlet.wizletHeader header nav #logo-container img {
  width: auto;
  height: 44px;
  padding: 5px 0;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletHeader header nav #logo-container img {
    padding-top: 5px;
  }
}
.wizlet.wizletHeader header nav ul img.avatar {
  height: 44px;
  -webkit-filter: drop-shadow(2px 4px 6px black);
          filter: drop-shadow(2px 4px 6px black);
  padding: 5px 0 5px 10px;
}
.wizlet.wizletHeader header nav ul .name {
  font-size: inherit;
  padding: 0 10px;
}
.wizlet.wizletHeader header nav ul li {
  margin: 0;
}
.wizlet.wizletHeader header nav ul li a {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.wizlet.wizletHeader header nav ul li a:hover {
  background-color: inherit;
}
.wizlet.wizletHeader header nav ul li a > i.right {
  margin-left: 5px;
}
.wizlet.wizletHeader header nav ul li .dropdown-content {
  overflow: hidden;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
.wizlet.wizletHeader header nav ul li .dropdown-content li a {
  width: 100%;
  color: #24466b;
}
.wizlet.wizletHeader header nav ul li .dropdown-content li a img {
  width: 1.5rem;
  margin-right: 5px;
}
.wizlet.wizletHeader header nav ul li .dropdown-content li a.disabled, .wizlet.wizletHeader header nav ul li .dropdown-content li a[disabled] {
  pointer-events: none;
  background-color: #DFDFDF;
  opacity: 0.5;
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.wizlet.wizletHeader header nav ul li .dropdown-content li:not(:last-child) {
  border-bottom: 1px solid #24466b;
}
.wizlet.wizletHeader header nav ul li .dropdown-content a {
  padding: 20px 16px;
}
.wizlet.wizletHeader header nav ul.left, .wizlet.wizletHeader header nav ul.right {
  height: 100%;
}
.wizlet.wizletHeader header nav ul.left > li, .wizlet.wizletHeader header nav ul.right > li {
  height: 100%;
}
.wizlet.wizletHeader header nav .sidenav {
  max-width: 50vw;
}
.wizlet.wizletHeader header nav .sidenav li a {
  width: 100%;
  color: #24466b;
}
.wizlet.wizletHeader header nav .sidenav li a img {
  width: 1.5rem;
  margin-right: 5px;
}
.wizlet.wizletHeader header nav .sidenav li a.disabled, .wizlet.wizletHeader header nav .sidenav li a[disabled] {
  pointer-events: none;
  background-color: #DFDFDF;
  opacity: 0.5;
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.wizlet.wizletHeader header nav .sidenav .user-view {
  padding: 15px;
  background-color: #FFFFFF;
}
.wizlet.wizletHeader header nav .sidenav .user-view img {
  max-width: 150px;
}
.wizlet.wizletHeader header nav .sidenav .user-view .background img {
  width: 100%;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletHeader header .sidenav.panel {
    display: none;
  }
}
@media only screen and (min-width : 601px) {
  .wizlet.wizletHeader header .sidenav.panel {
    width: 10% !important;
    z-index: -1;
    padding: 88px 1% 44px 1%;
  }
  .wizlet.wizletHeader header .sidenav.panel.animated {
    -webkit-animation-delay: 1s;
         -o-animation-delay: 1s;
            animation-delay: 1s;
  }
  .wizlet.wizletHeader header .sidenav.panel li {
    padding: 20px 0;
  }
  .wizlet.wizletHeader header .sidenav.panel li a {
    padding: 0;
    text-align: center;
  }
  .wizlet.wizletHeader header .sidenav.panel li a img {
    height: 48px;
    max-width: 100%;
    -o-object-fit: contain;
       object-fit: contain;
  }
  .wizlet.wizletHeader header .sidenav.panel li a figcaption {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 15px;
    margin-top: -15px;
    font-size: 12px;
    font-weight: bolder;
    color: #24466b;
  }
  .wizlet.wizletHeader header .sidenav.panel li a:hover {
    -webkit-transform: scale(1.1);
        -ms-transform: scale(1.1);
         -o-transform: scale(1.1);
            transform: scale(1.1);
  }
  .wizlet.wizletHeader header .sidenav.panel li a.on {
    -webkit-filter: drop-shadow(2px 4px 4px #6d747e);
            filter: drop-shadow(2px 4px 4px #6d747e);
  }
  .wizlet.wizletHeader header .sidenav.panel li a.on img.off {
    display: none;
  }
  .wizlet.wizletHeader header .sidenav.panel li a.off {
    opacity: 0.75;
  }
  .wizlet.wizletHeader header .sidenav.panel li a.off img.on {
    display: none;
  }
}