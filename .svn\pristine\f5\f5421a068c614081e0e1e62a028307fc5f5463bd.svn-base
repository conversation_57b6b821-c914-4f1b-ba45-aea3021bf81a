.wizlet.wizletRadioButtons .header-container {
  position: relative;
}
.wizlet.wizletRadioButtons .header-container .header.help {
  padding-right: 36px;
}
.wizlet.wizletRadioButtons .header-container i.help {
  position: absolute;
  right: 0;
  bottom: 0;
  -webkit-transform: translate(-50%, 0%);
      -ms-transform: translate(-50%, 0%);
       -o-transform: translate(-50%, 0%);
          transform: translate(-50%, 0%);
  cursor: pointer;
}
.wizlet.wizletRadioButtons .header-container i.help:hover {
  -webkit-transform: scale(1.1) translate(-50%, 0%);
      -ms-transform: scale(1.1) translate(-50%, 0%);
       -o-transform: scale(1.1) translate(-50%, 0%);
          transform: scale(1.1) translate(-50%, 0%);
}
.wizlet.wizletRadioButtons .container.borders {
  border-width: 10px;
  border-color: #cc4a3d;
  border-radius: 10px;
  float: none !important;
}
.wizlet.wizletRadioButtons .container.borders.top {
  border-top-style: double;
}
.wizlet.wizletRadioButtons .container.borders.right {
  border-right-style: double;
}
.wizlet.wizletRadioButtons .container.borders.left {
  border-left-style: double;
}
.wizlet.wizletRadioButtons .container.borders.bottom {
  border-bottom-style: double;
  margin-bottom: 50px;
}
.wizlet.wizletRadioButtons .header.with-label {
  position: relative;
  padding-left: 2.5rem;
  margin-bottom: 1rem;
}
.wizlet.wizletRadioButtons .header.with-label .badge.header-label {
  position: absolute;
  left: 0;
  top: 0;
  background-color: #cc4a3d;
  min-width: 2rem;
  min-height: 2rem;
  font-size: 1.25rem;
  font-weight: bold;
  border-radius: 50%;
  line-height: 2rem;
  margin: 0;
  margin-top: -5px;
}
.wizlet.wizletRadioButtons .row.same-heigh-columns .side-image {
  padding: 0;
  text-align: center;
  margin-top: 10px;
}
.wizlet.wizletRadioButtons .row.same-heigh-columns .side-image span.title {
  min-height: 5%;
  color: #24466b;
  font-family: clientBold, Arial;
  font-size: 100%;
}
.wizlet.wizletRadioButtons .row.same-heigh-columns .side-image img {
  height: 90%;
  width: 100%;
}
.wizlet.wizletRadioButtons .row.same-heigh-columns .card-panel {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 12px;
  padding-bottom: 12px;
}
.wizlet.wizletRadioButtons .card .card-content {
  background-color: #FFFFFF;
}
.wizlet.wizletRadioButtons .card .card-content .row {
  margin: 0;
}
.wizlet.wizletRadioButtons .card .card-content .header {
  color: #24466b;
  font-size: 125%;
  margin-top: 0;
}
.wizlet.wizletRadioButtons .card .card-content.card-content {
  background-color: #FFFFFF;
  border-radius: 10px;
}
.wizlet.wizletRadioButtons .card .card-content .card-title {
  font-family: clientBolder, clientBold, Arial;
  font-size: 150%;
  line-height: 100%;
  text-align: left;
  padding-bottom: 5px;
  border-bottom: 3px solid #cc4a3d;
  margin-bottom: 15px;
}
.wizlet.wizletRadioButtons .card .card-content .card-title.activator, .wizlet.wizletRadioButtons .card .card-content .card-title.reveal {
  position: relative;
}
.wizlet.wizletRadioButtons .card .card-content .card-title.activator i.material-icons.right, .wizlet.wizletRadioButtons .card .card-content .card-title.reveal i.material-icons.right {
  position: absolute;
  top: 0;
  right: -5px;
}
.wizlet.wizletRadioButtons .card .card-content .embed {
  clear: both;
  width: 40%;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletRadioButtons .card .card-content .embed {
    width: 100%;
  }
}
.wizlet.wizletRadioButtons .card .card-content .embed.left {
  float: left;
  margin-right: 12px;
}
.wizlet.wizletRadioButtons .card .card-content .embed.right {
  float: right;
  margin-left: 12px;
}
.wizlet.wizletRadioButtons .card .card-content .embed.xtiny {
  width: 10%;
}
.wizlet.wizletRadioButtons .card .card-content .embed.tiny {
  width: 20%;
}
.wizlet.wizletRadioButtons .card .card-content .embed.small {
  width: 30%;
}
.wizlet.wizletRadioButtons .card .card-content .embed.large {
  width: 50%;
}
.wizlet.wizletRadioButtons .card .card-content .embed.extralarge {
  width: 70%;
}
.wizlet.wizletRadioButtons form > ul > li {
  padding: 10px 5px;
  margin: 0;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}
.wizlet.wizletRadioButtons form > ul > li:not(:last-child) {
  border-bottom: 1px solid #cc4a3d;
}
.wizlet.wizletRadioButtons form > ul > li label {
  color: #24466b;
}
.wizlet.wizletRadioButtons form > ul > li label .question-label {
  color: #ffffff;
  position: absolute;
  left: 0;
  top: 0;
  -webkit-transform: translateX(-75%) translateY(-25%);
      -ms-transform: translateX(-75%) translateY(-25%);
       -o-transform: translateX(-75%) translateY(-25%);
          transform: translateX(-75%) translateY(-25%);
  padding: 0 2px;
  min-width: 2rem;
  background-color: #cc4a3d;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletRadioButtons form > ul > li label .question-label {
    -webkit-transform: translateX(-110%) translateY(-25%);
        -ms-transform: translateX(-110%) translateY(-25%);
         -o-transform: translateX(-110%) translateY(-25%);
            transform: translateX(-110%) translateY(-25%);
    min-width: 1.3rem;
  }
}
.wizlet.wizletRadioButtons form > ul > li.chosen .hoverable {
  -webkit-box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
          box-shadow: 0 8px 17px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}
.wizlet.wizletRadioButtons form > ul > li.chosen label .question-label {
  background-color: #cc4a3d;
}
.wizlet.wizletRadioButtons form > ul > li.turnedoff {
  opacity: 0.8;
}
.wizlet.wizletRadioButtons form > ul > li.turnedoff label, .wizlet.wizletRadioButtons form > ul > li.turnedoff label span.title {
  color: #CFD1D0 !important;
}
.wizlet.wizletRadioButtons form > ul > li.turnedoff .question-label {
  background-color: #cc4a3d;
}
.wizlet.wizletRadioButtons form > ul > li.disabled {
  pointer-events: none;
}
.wizlet.wizletRadioButtons form > ul > li.disabled ul.collapsible {
  pointer-events: initial;
}
.wizlet.wizletRadioButtons form > ul > li.disabled:not(.chosen) {
  opacity: 0.8;
}
.wizlet.wizletRadioButtons form > ul > li.disabled:not(.chosen) label, .wizlet.wizletRadioButtons form > ul > li.disabled:not(.chosen) label span.title {
  color: #CFD1D0;
}
.wizlet.wizletRadioButtons form > ul > li.disabled:not(.chosen) label span.title::before {
  border-color: #CFD1D0;
}
.wizlet.wizletRadioButtons form > ul > li.disabled:not(.chosen)[data-correct=true] {
  -webkit-filter: drop-shadow(2px 4px 6px black);
          filter: drop-shadow(2px 4px 6px black);
  opacity: 0.9;
}
.wizlet.wizletRadioButtons form > ul > li.disabled:not(.chosen)[data-correct=true] label {
  color: #6d747e;
}
.wizlet.wizletRadioButtons form > ul > li.disabled:not(.chosen) .question-label {
  background-color: #cc4a3d;
}
.wizlet.wizletRadioButtons form > ul > li:not(.disabled) {
  cursor: pointer;
}
.wizlet.wizletRadioButtons form > ul > li:not(.disabled):hover label {
  color: #cc4a3d;
}
.wizlet.wizletRadioButtons form > ul > li:not(.disabled):hover [type=radio] + span:before {
  border-color: #cc4a3d;
}
.wizlet.wizletRadioButtons form > ul > li.no-separator {
  border-bottom: none;
}
.wizlet.wizletRadioButtons form > ul > li.reduced {
  padding-top: 2px;
  padding-bottom: 0px;
}
.wizlet.wizletRadioButtons form > ul > li.reduced .card-panel {
  padding: 15px;
}
.wizlet.wizletRadioButtons form > ul > li.reduced .card-panel span.question-label {
  -webkit-transform: translateX(-85%) translateY(-25%);
      -ms-transform: translateX(-85%) translateY(-25%);
       -o-transform: translateX(-85%) translateY(-25%);
          transform: translateX(-85%) translateY(-25%);
  min-width: 1.5rem;
}
.wizlet.wizletRadioButtons form > ul > li.reduced .card-panel span.title {
  font-weight: normal;
}
.wizlet.wizletRadioButtons form > ul > li [type=radio] + span {
  font-weight: bold;
  font-size: 1.3rem;
  height: auto;
  padding-left: 50px;
}
.wizlet.wizletRadioButtons form > ul > li [type=radio] + span:before {
  border: 5px solid #cc4a3d;
  height: 32px;
  width: 32px;
  top: 50%;
  -webkit-transform: translateY(-65%);
      -ms-transform: translateY(-65%);
       -o-transform: translateY(-65%);
          transform: translateY(-65%);
}
.wizlet.wizletRadioButtons form > ul > li [type=radio] + span:after {
  height: 36px;
  width: 36px;
  margin-top: -7px;
  margin-left: 2px;
  top: 50%;
  -webkit-transform: scale(0.5) translateY(-65%);
      -ms-transform: scale(0.5) translateY(-65%);
       -o-transform: scale(0.5) translateY(-65%);
          transform: scale(0.5) translateY(-65%);
  border: none;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletRadioButtons form > ul > li [type=radio] + span {
    padding-left: 40px;
  }
  .wizlet.wizletRadioButtons form > ul > li [type=radio] + span:before {
    height: 24px;
    width: 24px;
  }
  .wizlet.wizletRadioButtons form > ul > li [type=radio] + span:after {
    height: 20px;
    width: 20px;
    margin-top: -3px;
    margin-left: 6px;
  }
}
.wizlet.wizletRadioButtons form > ul > li [type=radio]:checked + span:before {
  border-color: #cc4a3d;
}
.wizlet.wizletRadioButtons form > ul > li [type=radio]:checked + span:after {
  border-color: #cc4a3d;
  background: #cc4a3d;
}
.wizlet.wizletRadioButtons form > ul > li [type=radio]:disabled + span:before {
  border-color: inherit;
}
.wizlet.wizletRadioButtons form > ul > li .card-panel {
  position: relative;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletRadioButtons form > ul > li .card-panel {
    padding: 12px;
  }
}
.wizlet.wizletRadioButtons form > ul > li .card-panel span.title > div.col {
  padding: 0;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletRadioButtons form > ul > li .card-panel span.title .col.image {
    display: none;
  }
  .wizlet.wizletRadioButtons form > ul > li .card-panel span.title .col.option {
    width: 100%;
  }
}
.wizlet.wizletRadioButtons form > ul > li .card-panel span.title.hide-radio {
  padding-left: 0px;
}
.wizlet.wizletRadioButtons form > ul > li .card-panel span.title.hide-radio:before, .wizlet.wizletRadioButtons form > ul > li .card-panel span.title.hide-radio:after {
  display: none;
}
.wizlet.wizletRadioButtons form > ul > li .card-panel i.check-icon {
  position: absolute;
  top: 0;
  z-index: 1;
}
.wizlet.wizletRadioButtons form > ul > li .card-panel i.check-icon.left {
  left: 0;
}
.wizlet.wizletRadioButtons form > ul > li .card-panel i.check-icon.right {
  right: 0;
}
.wizlet.wizletRadioButtons form > ul > li .card-panel i.check-icon.scaled-out {
  -webkit-transform: scale(0);
      -ms-transform: scale(0);
       -o-transform: scale(0);
          transform: scale(0);
}
.wizlet.wizletRadioButtons form > ul > li ul.collapsible > li {
  background-color: #FFFFFF;
}
.wizlet.wizletRadioButtons form > ul > li ul.collapsible > li .collapsible-header {
  font-size: 90%;
  border: none;
  padding: 0.5rem;
}
.wizlet.wizletRadioButtons form > ul > li ul.collapsible > li .collapsible-body {
  font-size: 95%;
  border: none;
  padding: 0 1rem 1rem 1rem;
}
@media only screen and (min-width : 601px) {
  .wizlet.wizletRadioButtons form > ul.inline > li:not(:last-child) {
    border-bottom: none;
  }
  .wizlet.wizletRadioButtons form > ul.inline > li [type=radio] + span {
    font-size: 1.25em;
    padding-left: 40px;
  }
  .wizlet.wizletRadioButtons form > ul.inline > li.big [type=radio] + span {
    font-size: 200%;
    padding-left: 50px;
  }
  .wizlet.wizletRadioButtons form > ul.inline > li.small .card-panel {
    padding: 12px;
  }
  .wizlet.wizletRadioButtons form > ul.inline > li.small [type=radio] + span i {
    font-size: 3rem;
  }
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletRadioButtons form > ul.inline > li.col {
    width: 100%;
  }
}
.wizlet.wizletRadioButtons form > ul.inline > li span.title,
.wizlet.wizletRadioButtons form > ul.inline > li span.title > div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
}
.wizlet.wizletRadioButtons .countDownTimerHolder {
  padding: 5px 10px;
  min-width: 150px;
  background-color: #24466b;
  text-align: center;
}
.wizlet.wizletRadioButtons .countDownTimerHolder.big {
  padding: 50px 20px 20px 20px;
  border-radius: 15px;
}
.wizlet.wizletRadioButtons .countDownTimerHolder.big .countDownTimer {
  font-size: 100px;
}
.wizlet.wizletRadioButtons .countDownTimerHolder.center {
  margin: 40vh 0 0 30vw;
  width: 30vw;
}
.wizlet.wizletRadioButtons .countDownTimerHolder .countDownTimer {
  font-size: 20px;
  font-weight: bold;
  background-color: transparent !important;
}
.wizlet.wizletRadioButtons .row.timer {
  clear: both;
}
.wizlet.wizletRadioButtons .row.timer .countDownTimerHolder.finished.big {
  padding-bottom: 30px;
}
.wizlet.wizletRadioButtons .row.timer .countDownTimerHolder.finished.big .countDownTimer {
  font-size: 50px;
}
.wizlet.wizletRadioButtons .row.timer .countDownTimerHolder.finished.center {
  margin: 40vh 0 0 5vw;
  width: 90vw;
}
.wizlet.wizletRadioButtons .row.timer .countDownTimerHolder.disabled {
  background-color: #cc4a3d;
}
.wizlet.wizletRadioButtons .row.submit {
  padding-top: 5px;
  margin-right: 0;
  text-align: right;
}
.wizlet.wizletRadioButtons .row.submit #checkBtn[hidden] {
  display: inline-block !important;
  visibility: hidden;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletRadioButtons .row.submit .btn {
    font-size: 0;
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
  .wizlet.wizletRadioButtons .row.submit .btn i {
    width: inherit;
    display: inline-block;
    text-align: center;
    color: #fff;
    font-size: 1.6rem;
    line-height: 40px;
  }
}
.wizlet.wizletRadioButtons #checkBtn {
  margin-left: 15px;
}
.wizlet.wizletRadioButtons #resultsBtn[href] {
  color: #ffffff;
}