.wizlet.wizletImageTooltip .card .card-content.card-content {
  background-color: #FFFFFF;
  border-radius: 10px;
}
.wizlet.wizletImageTooltip .card .card-content .card-title {
  font-family: clientBolder, clientBold, Arial;
  font-size: 150%;
  line-height: 100%;
  text-align: left;
  padding-bottom: 5px;
  border-bottom: 3px solid #cc4a3d;
  margin-bottom: 15px;
}
.wizlet.wizletImageTooltip .card .card-content .card-title.activator, .wizlet.wizletImageTooltip .card .card-content .card-title.reveal {
  position: relative;
}
.wizlet.wizletImageTooltip .card .card-content .card-title.activator i.material-icons.right, .wizlet.wizletImageTooltip .card .card-content .card-title.reveal i.material-icons.right {
  position: absolute;
  top: 0;
  right: -5px;
}
.wizlet.wizletImageTooltip .card .card-image {
  padding: 12px;
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip {
  cursor: pointer;
  position: absolute;
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip i {
  font-size: 1rem;
}
@media only screen and (min-width : 601px) {
  .wizlet.wizletImageTooltip .card .card-image a.btn-tooltip i {
    font-size: 2rem;
  }
}
@media only screen and (min-width : 993px) {
  .wizlet.wizletImageTooltip .card .card-image a.btn-tooltip i {
    font-size: 3rem;
  }
}
@media only screen and (min-width : 1201px) {
  .wizlet.wizletImageTooltip .card .card-image a.btn-tooltip i {
    font-size: 3.5rem;
  }
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip i.bounce {
  -o-animation-name: bounceInfinite;
  -webkit-animation-name: bounceInfinite;
  animation-name: bounceInfinite;
  -o-animation-duration: 10s;
  -webkit-animation-duration: 10s;
  animation-duration: 10s;
  -o-animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -o-animation-timing-function: ease;
  -webkit-animation-timing-function: ease;
  animation-timing-function: ease;
  -o-animation-fill-mode: forwards;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip:nth-child(0) i.bounce {
  -o-animation-delay: 1s;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip:nth-child(1) i.bounce {
  -o-animation-delay: 2s;
  -webkit-animation-delay: 2s;
  animation-delay: 2s;
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip:nth-child(2) i.bounce {
  -o-animation-delay: 3s;
  -webkit-animation-delay: 3s;
  animation-delay: 3s;
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip:nth-child(3) i.bounce {
  -o-animation-delay: 4s;
  -webkit-animation-delay: 4s;
  animation-delay: 4s;
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip:nth-child(4) i.bounce {
  -o-animation-delay: 5s;
  -webkit-animation-delay: 5s;
  animation-delay: 5s;
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip:nth-child(5) i.bounce {
  -o-animation-delay: 6s;
  -webkit-animation-delay: 6s;
  animation-delay: 6s;
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip:nth-child(6) i.bounce {
  -o-animation-delay: 7s;
  -webkit-animation-delay: 7s;
  animation-delay: 7s;
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip:nth-child(7) i.bounce {
  -o-animation-delay: 8s;
  -webkit-animation-delay: 8s;
  animation-delay: 8s;
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip:nth-child(8) i.bounce {
  -o-animation-delay: 9s;
  -webkit-animation-delay: 9s;
  animation-delay: 9s;
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip:nth-child(9) i.bounce {
  -o-animation-delay: 10s;
  -webkit-animation-delay: 10s;
  animation-delay: 10s;
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip .underline {
  position: absolute;
  height: 3px;
  background-color: #FFFFFF;
}
.wizlet.wizletImageTooltip .card .card-image a.btn-tooltip .underline.pulse::before {
  z-index: 1;
}
.wizlet.wizletImageTooltip .card.fit .card-image {
  max-height: 70vh;
  height: 100%;
}
.wizlet.wizletImageTooltip .card.fit .card-image img {
  -o-object-fit: contain;
     object-fit: contain;
  max-height: 70vh;
}
.wizlet.wizletImageTooltip .fullscreen .card-image {
  padding: 0;
}

.material-tooltip.timage {
  padding: 5px 20px 20px;
  border-radius: 10px;
  background: rgba(204, 74, 61, 0.9);
  overflow: visible;
  font-size: inherit;
  text-align: initial;
}
@media only screen and (min-width : 601px) {
  .material-tooltip.timage {
    max-width: 45% !important;
  }
}
.material-tooltip.timage.large {
  max-width: 96% !important;
}
@media only screen and (max-width : 600px) {
  .material-tooltip.timage {
    font-size: 1rem;
  }
}
.material-tooltip.timage .tooltip-content,
.material-tooltip.timage .tooltip-content li {
  font-size: 95%;
  line-height: 100%;
}
@media only screen and (min-width : 601px) {
  .material-tooltip.timage .tooltip-content {
    font-size: 100%;
  }
}
@media only screen and (min-width : 993px) {
  .material-tooltip.timage .tooltip-content {
    font-size: 110%;
  }
}
@media only screen and (min-width : 1201px) {
  .material-tooltip.timage .tooltip-content {
    font-size: 120%;
  }
}
.material-tooltip.timage:after {
  position: absolute;
  content: " ";
  margin-left: -8px;
  width: 0;
  height: 0;
  border-style: solid;
}
.material-tooltip.timage.bottom:after {
  top: 0%;
  left: 50%;
  border-width: 0 13px 15px 13px;
  border-color: transparent transparent rgba(204, 74, 61, 0.9) transparent;
  margin-top: -14px;
}
.material-tooltip.timage.top:after {
  top: 100%;
  left: 50%;
  border-width: 15px 13px 0 13px;
  border-color: rgba(204, 74, 61, 0.9) transparent transparent transparent;
  margin-top: -1px;
}
.material-tooltip.timage.right:after {
  top: 25%;
  left: 0%;
  border-width: 13px 15px 13px 0;
  border-color: transparent rgba(204, 74, 61, 0.9) transparent transparent;
  margin-left: -14px;
}
.material-tooltip.timage.left:after {
  top: 25%;
  left: 100%;
  border-width: 13px 0 13px 15px;
  border-color: transparent transparent transparent rgba(204, 74, 61, 0.9);
  margin-left: -1px;
}