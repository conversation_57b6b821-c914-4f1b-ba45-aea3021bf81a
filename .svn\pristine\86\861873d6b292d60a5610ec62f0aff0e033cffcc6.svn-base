<?xml version="1.0" encoding="utf-8" ?>
<Action>
    
  <Component type="Header" wizletName="Header_IntroFAC_R4" customJS="true"><![CDATA[{
    templateInEvent: "html/header.dot",
    css: "styles/header.css",
    logo:   {
        src: "!{<PERSON><PERSON>_logo}",
        alt: "logo"
    },
    user: {
      background: "",
      avatar: "!{Header_logo_menu}",
      my_avatar: "Q_My_Avatar"
    },
    
    myName: "Q_My_Name",
    myTeam: "",
      
    follower_suffix: " - !{Header_Follower}",
    trackTeam: "Team",
    isFollower: "Follower",

    scope: ["Q_My_Name","Q_My_Avatar","Follower"],

    links: [
      {
        section: true,
        sectionID: "dropdownInfo",
        sectionTitle: "!{Header_LinkSection_Info}",
        sectionLast: false,
        id: "link_logins",
        title: "!{Header_LinkLogins_R3}",
        icon: "schedule",
        popup: "R3_Logins_Table_menu",
        popupID: "modal-logins"
      },
        {
          divider: true
        },
      {
        section: true,
        sectionLast: true,
        id: "link_ranking",
        title: "!{Header_LinkLeague}",
        icon: "emoji_events",
        popup: "SIM_Ranking_menu",
        popupID: "modal-ranking",
          gdActionEmbed: "SIM_AGG",
          gdActionTrack: "GD"
      },
        {
          divider: true
        },
      {
        id: "link_refresh",
        title: "!{Header_LinkRefresh}",
        icon: "refresh",
        onclick: "window.location.reload(true)"
      },
        {
          divider: true
        },
      {
        id: "link_exit",
        title: "!{Header_LinkExit}",
        icon: "exit_to_app",
        modalID: "modal-logout"
      }
    ],
    
    close: "!{Header_LinkClose}",
    sectionsID: ["modal-logins", "modal-ranking"],


    _help: {
      modalID: "modal-help",
      text: "!{Header_Modal_Help_Text}",
      close: "!{Header_LinkClose}"
    },

    logout: {
      modalID: "modal-logout",
      header: "!{Header_Modal_Logout_Title}",
      text: "!{Header_Modal_Logout_Text}",
      close: "!{Header_Modal_Logout_Close}",
      logout: "!{Header_Modal_Logout_Logout}",
      onclick: "logout()"
    }    
    
  }]]></Component>


  <Component type="Vanilla" wizletName="Footer"><![CDATA[{
    templateInEvent: "html/footer.dot",
    css: "styles/footer.css",
    footer: {
      color: "dark-grey",
      banner: "!{Footer_Img}",
      _logo: {src:"", alt:""},
      title: "!{Footer_Title}",
      subtitle: "!{Footer_Subtitle}",
      copyright: "", link: ""
    }
  }]]></Component>
  

</Action>