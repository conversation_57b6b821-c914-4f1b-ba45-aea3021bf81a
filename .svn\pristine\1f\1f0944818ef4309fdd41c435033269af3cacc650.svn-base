
<div class="container{{?it.animate}} animate__animated animate__{{=it.animate}}{{?}}{{?it.isHiddenWhenSmall}} hide-on-med-and-down{{?}}{{?it.isHiddenWhenBig}} hide-on-large-only{{?}}"
{{?it.id}}id="{{=it.id}}"{{?}} {{?it.isHidden}}hidden{{?}}> 
            
    {{?it.header}}<h4 class="header">{{=it.header}}</h4>{{?}}
    {{?it.subheader}}<h5 class="header subtitle">{{=it.subheader}}</h5>{{?}}
    {{?it.instructions}}<h6 class="instructions flow-text">{{=it.instructions}}</h6>{{?}}
    

    {{?it.headerLeft || it.headerRight}}
    <div class="row table header">
        <ul class="col s{{=it.width.left}} header left-list">
            <li class="card-panel row client-colors secondary client-colors-text text-font2">                
                <div class="col s12"><span class="strong">{{?it.headerLeft}}{{=it.headerLeft}}{{??}}&nbsp;{{?}}</span></div>            
            </li>
        </ul>
        <ul class="col s{{=it.width.right}} header right-list">
            <li class="card-panel row client-colors secondary client-colors-text text-font2">                
                <div class="col s12"><span class="strong">{{?it.headerRight}}{{=it.headerRight}}{{??}}&nbsp;{{?}}</span></div>            
            </li>
        </ul>
    </div>
    {{?}}



    <div class="row {{?it.class}}{{=it.class}}{{?}}">
        <ul class="col s{{=it.width.left}} left-list length-{{=it.questions.length}}">
        {{~it.questions :question :idx}}
            <li class="left-item card-panel row client-colors {{?question.colorLeft}}{{=question.colorLeft}}{{??}}primary{{?}} client-colors-text text-font2 scale-transition scaled-out"
                {{?it.minHeight}}style="min-height: {{=it.minHeight}};"{{?}}>
                
                <div class="row {{?it.withHelp}}with-help{{?}}">
                    {{?question.image}}
                        <div class="option-image {{?question.title}}col {{?it.grid && it.grid.image}}{{=it.grid.image}}{{??}}s3{{?}}{{?}}">
                            <img class="" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=question.image}}"/>
                        </div>
                        {{?question.title}}
                        <div class="option-image col {{?it.grid && it.grid.title}}{{=it.grid.title}}{{??}}s9{{?}}">
                            <span class="title">{{=question.title}}</span>
                        </div>
                        {{?}}
                    {{??}}
                        <span class="title strong">{{=question.title}}</span>
                        {{?question.title2}}<span class="title">{{=question.title2}}</span>{{?}}
                    {{?}}

                    {{?question.help}}
                    <a class="btn-tooltip help-icon tooltipped timage" 
                            data-position="right" data-tooltip="{{=question.help.text}}">
                        <i class="material-icons {{?question.help.bounce}}bounce{{?}}">{{=question.help.icon}}</i>
                    </a>
                    {{?}}
                </div>
            
            </li>
        {{~}}
        </ul>

        <ul id="matchable" class="{{?it.isSolution}}solution{{?}} col s{{=it.width.right}} right-list length-{{=it.questions.length}}">
        {{~it.questions :question :idx}}
            <li class="right-item card-panel row hoverable matchable client-colors {{?question.colorRight}}{{=question.colorRight}}{{??}}secondary{{?}} client-colors-text text-font scale-transition scaled-out"
                data-bind="{{=question.bind}}" data-order="{{=idx+1}}"
                {{?it.minHeight}}style="min-height: {{=it.minHeight}};"{{?}}>
                
                <div class="col s11 matchable">
                    
                    {{?question.image2}}
                        <div class="{{?question.text}}col s3{{?}} option-image">
                            <img class="" src="/Wizer/Pages/Events/{{=wizerApi.eventName()}}{{=question.image2}}"/>
                        </div>
                        {{?question.text}}
                        <div class="col s9 option-image">
                            <span class="text">{{=question.text}}</span>
                        </div>
                        {{?}}
                    {{??}}
                        <span class="text">{{=question.text}}</span>
                    {{?}}

                </div> 


                <div class="col s1 icon">                    
                    {{? !it.isSolution}}
                    <i class="hide-on-small-only small material-icons right">swap_vert</i>
                    {{?}}
                    {{?it.isCheck}}
                        {{? it.isSolution}}
                        <i class="check-icon material-icons small right green-text">check_circle</i>
                        {{??}}
                        <i hidden class="check-icon material-icons small right"> </i>
                        {{?}}
                    {{?}}
                </div>

            </li>
        {{~}}
        </ul>
    
    </div>
    
    {{?it.submitBtn && it.submitBtn.label}}    
    <div class="row submit">
        <a id="submitBtn" class="btn pulse client-colors button"
            {{?it.DB[it.isFollower]>0}}disabled{{?}}>
            <i class="medium material-icons right">{{?it.submitBtn.icon}}{{=it.submitBtn.icon}}{{??}}send{{?}}</i>{{=it.submitBtn.label}}
        </a>
    </div>
    {{?}}
    
    
</div>
