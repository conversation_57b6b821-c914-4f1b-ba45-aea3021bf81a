.wizlet.wizletTableInputs .container.table .card-content {
  background-color: #FFFFFF;
  display: grid;
}
.wizlet.wizletTableInputs .container.table .card-content.card-content {
  background-color: #FFFFFF;
  border-radius: 10px;
}
.wizlet.wizletTableInputs .container.table .card-content .card-title {
  font-family: clientBolder, clientBold, Arial;
  font-size: 150%;
  line-height: 100%;
  text-align: left;
  padding-bottom: 5px;
  border-bottom: 3px solid #cc4a3d;
  margin-bottom: 15px;
}
.wizlet.wizletTableInputs .container.table .card-content .card-title.activator, .wizlet.wizletTableInputs .container.table .card-content .card-title.reveal {
  position: relative;
}
.wizlet.wizletTableInputs .container.table .card-content .card-title.activator i.material-icons.right, .wizlet.wizletTableInputs .container.table .card-content .card-title.reveal i.material-icons.right {
  position: absolute;
  top: 0;
  right: -5px;
}
.wizlet.wizletTableInputs .container.table .card-content .card-title {
  margin-bottom: 20px;
}
.wizlet.wizletTableInputs .container.table .card-content table {
  border-collapse: collapse;
  border: none;
}
.wizlet.wizletTableInputs .container.table .card-content table.striped tbody tr:nth-child(odd) {
  background-color: rgba(204, 74, 61, 0.15);
}
.wizlet.wizletTableInputs .container.table .card-content table thead tr {
  border-bottom: 2px solid #cc4a3d;
}
.wizlet.wizletTableInputs .container.table .card-content table thead tr.empty {
  border: none;
}
.wizlet.wizletTableInputs .container.table .card-content table thead.headerColor tr th {
  border-radius: 20px 20px 0 0;
  border: 2px white solid;
}
.wizlet.wizletTableInputs .container.table .card-content table thead th {
  padding: 5px 5px;
  text-align: center;
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr {
  pointer-events: none;
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr > td:not(:first-child) {
  border-left: 2px solid #cc4a3d;
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr td {
  padding: 5px 5px;
  vertical-align: middle;
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr td ol, .wizlet.wizletTableInputs .container.table .card-content table tbody tr td ul {
  padding: 0 0 0 20px;
  font-size: 90%;
  text-align: left;
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr td ol li span, .wizlet.wizletTableInputs .container.table .card-content table tbody tr td ul li span {
  display: inline-table;
  margin-left: -10px;
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr td .textImage {
  -webkit-box-align: center;
  -webkit-align-items: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr td .textImage img {
  max-width: 4rem;
  max-height: 4rem;
  -o-object-fit: contain;
     object-fit: contain;
  margin-right: 1rem;
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr td .input-cell {
  white-space: nowrap;
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr td .input-field {
  margin: 0;
  display: inline-block;
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr td .input-field > i {
  position: absolute;
  height: 3rem;
  line-height: 3rem;
  left: -1rem;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletTableInputs .container.table .card-content table tbody tr td .input-field > i {
    display: none;
  }
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr td .input-field > input {
  pointer-events: auto;
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr td .input-field > input[type=number]::-webkit-inner-spin-button, .wizlet.wizletTableInputs .container.table .card-content table tbody tr td .input-field > input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: "Always Show Up/Down Arrows";
  opacity: 1;
  margin-left: -20px;
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr td .input-field span.suffix {
  position: absolute;
  top: 0.75rem;
  right: 1rem;
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr td .input-field.disabled > i {
  display: none;
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr td .input-field.disabled > input {
  opacity: 1;
  pointer-events: none;
  -webkit-filter: grayscale(100%);
          filter: grayscale(100%);
}
.wizlet.wizletTableInputs .container.table .card-content table tbody tr td .input-field.disabled > input[type=number]::-webkit-inner-spin-button, .wizlet.wizletTableInputs .container.table .card-content table tbody tr td .input-field.disabled > input[type=number]::-webkit-outer-spin-button {
  display: none;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletTableInputs .container.table .card-content table tbody tr td {
    font-size: 90%;
  }
}
.wizlet.wizletTableInputs .container.table .card-content table caption {
  display: inline-block;
  margin-top: 10px;
  text-align: left;
  font-family: clientLight;
  font-size: 80%;
}
@media only screen and (min-width : 601px) {
  .wizlet.wizletTableInputs .container.table .card-content table.bigHeaders thead th {
    font-size: 120%;
  }
}
.wizlet.wizletTableInputs .container.table .card-content table.smallHeaders thead th {
  font-size: 90%;
}
@media only screen and (max-width : 992px) {
  .wizlet.wizletTableInputs .container.table .card-content table.responsive-table thead tr {
    border-bottom: none;
    border-right: 2px solid #cc4a3d;
  }
}
.wizlet.wizletTableInputs .container.table .card-content table.fixed {
  table-layout: fixed;
}
.wizlet.wizletTableInputs .container.table .card-content table.firstColumnBold tbody tr > td:first-child {
  font-weight: bold;
  font-size: 110%;
}