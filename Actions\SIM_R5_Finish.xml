<?xml version="1.0" encoding="utf-8" ?>

<Action mainAreaLayout="../../../layout/mainLayout_SIM">
  <Include name="Header_SIM"></Include>
  <Include name="KPIgauges"></Include>



  <Component type="Vanilla">
    <![CDATA[{
      templateInEvent: "html/card.dot",
      css: "styles/card.css",
      orientation: "vertical",
      header: "!{}",
      valign: false,
      animate: "fadeInUp",
      content: {
        _img_embed: {
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R5_Finish_Img}",  alt: "!{SIM_R5_Finish_Title}" ,
          position: "right large", style: ""
        },
        img: {
          materialboxed: false, borders: false, frame: "", nopadding: false,
          src: "!{SIM_R5_Finish_Img}",  alt: "!{SIM_R5_Finish_Title}" ,
          src_vert: "!{}"
        },
        position: "up",
        title: "!{SIM_R5_Finish_Title}",
        body: "!{SIM_R5_Finish_Text}"
      }
    }]]>
  </Component>


  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <!-- <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: false, _showDelay: "3000",
      hasModals: true,
      buttons: [
        {
          type: "modal",
          pulse: true,
          popupID: "Modal_menu",
          popup: "SIM_R5_Summary_modal",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_summary}",
          icon: "zoom_in"
        }
      ],

      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component> -->


</Action>