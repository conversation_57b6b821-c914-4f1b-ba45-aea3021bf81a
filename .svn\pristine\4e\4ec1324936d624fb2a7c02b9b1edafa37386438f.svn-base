<?xml version="1.0" encoding="utf-8" ?>
<Action disableDynamicVotes="false" mainAreaLayout="../../../layout/mainLayoutFAC" layout="../../../layout/tabsLayout">
  <Include name="HeaderFAC"></Include>

  <!-- Tabs component -->
  <Component type="Tabs" customJS="true"><![CDATA[{
      templateInEvent: "html/tabs.dot",
      css: "styles/tabs.css",
      header: "!{SIM_R2_FAC_dashboard_Header}",
      instructions: "!{}",
      swipeable: true,
      tabs: [
          "!{SIM_R2_FAC_dashboard_teams}",
          "!{SIM_R2_FAC_dashboard_tab1}",
          "!{SIM_R2_FAC_dashboard_tab2}",
          "!{SIM_R2_FAC_dashboard_ranking}"
      ],
      scope: null
  }]]></Component>

      

<!-- ********* -->
<!-- TAB TEAMS -->
<!-- ********* -->

  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    position: "",
    header: "!{}",
    userHeader: "!{TeamName_Table_User}",
    me: "",
    boundName: "",
    avatar: " ",
    defaultAvatar: "!{defaultAvatar}",
    boundAvatar: "Q_My_Avatar",
    isDataTables: false,
    questions: [
      {
        show: true,
        title: "!{TeamName_Table_Name}",
        binding: "Q_My_Name"
      }
    ],
    trackQuestion: "",
    rankQuestion: "",
    sortByQuestion: "Team",
    sortOrder: "asc",
    listSkip: 0,
    listLength:1000,
    showNear: 5,
    liveUpdate: true,
    markUpdate: false
  }]]></Component>

      

<!-- ***** -->
<!-- TAB 1 -->
<!-- ***** -->
    

  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    instructions: "!{CheckboxesInstructionsAnswers}",   
    position: "",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_responsive-table_ _myresponsive-table_ verticalMode _subheaders_ noBold _fixed_",
    headerMultiLines: false,
    headerEllipsis: true,
    questions: [
      {
        show: true,
        title: "!{Badge_Opt1}-!{SIM_Initiatives_Opt1}",
        binding: "Q_SIM_R2_Initiatives_1"
      },
      {
        show: true,
        title: "!{Badge_Opt2}-!{SIM_Initiatives_Opt2}",
        binding: "Q_SIM_R2_Initiatives_2"
      },
      {
        show: true,
        title: "!{Badge_Opt3}-!{SIM_Initiatives_Opt3}",
        binding: "Q_SIM_R2_Initiatives_3"
      },
      {
        show: true,
        title: "!{Badge_Opt4}-!{SIM_Initiatives_Opt4}",
        binding: "Q_SIM_R2_Initiatives_4"
      },
      {
        show: true,
        title: "!{Badge_Opt5}-!{SIM_Initiatives_Opt5}",
        binding: "Q_SIM_R2_Initiatives_5"
      },
      {
        show: true,
        title: "!{Badge_Opt6}-!{SIM_Initiatives_Opt6}",
        binding: "Q_SIM_R2_Initiatives_6"
      },
      {
        show: true,
        title: "!{Badge_Opt7}-!{SIM_Initiatives_Opt7}",
        binding: "Q_SIM_R2_Initiatives_7"
      },
      {
        show: true,
        title: "!{Badge_Opt8}-!{SIM_Initiatives_Opt8}",
        binding: "Q_SIM_R2_Initiatives_8"
      },
      {
        show: true,
        title: "!{Badge_Opt9}-!{SIM_Initiatives_Opt9}",
        binding: "Q_SIM_R2_Initiatives_9"
      },
      {
        show: true,
        title: "!{Badge_Opt10}-!{SIM_Initiatives_Opt10}",
        binding: "Q_SIM_R2_Initiatives_10"
      },
      {
        show: true,
        title: "!{Badge_Opt11}-!{SIM_Initiatives_Opt11}",
        binding: "Q_SIM_R2_Initiatives_11"
      },
      {
        show: true,
        title: "!{Badge_Opt12}-!{SIM_Initiatives_Opt12}",
        binding: "Q_SIM_R2_Initiatives_12"
      }
    ],
    _answervalidation: true,
    _subheaders: [
      "",
      "!{}",
      "!{}"
    ],
    trackQuestion: "",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: true,
    markUpdate: true
  }]]></Component>
  



<!-- ***** -->
<!-- TAB 2 -->
<!-- ***** -->

  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    header: "!{}",
    instructions: "!{CheckboxesInstructionsAnswers}",   
    position: "",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: false,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_responsive-table_ myresponsive-table _verticalMode_ subheaders noBold fixed",
    headerMultiLines: true,
    headerEllipsis: true,
    questions: [
      {
        show: true,
        _title: "!{SIM_R2_Scenario1_Question}",
        title: "!{SIM_R2_Scenario1_Header}: !{SIM_R2_Scenario1_Title}",
        binding: "Q_SIM_R2_Scenario1"
      },
      {
        show: true,
        _title: "!{SIM_R2_Scenario2_Question}",
        title: "!{SIM_R2_Scenario2_Header}: !{SIM_R2_Scenario2_Title}",
        binding: "Q_SIM_R2_Scenario2"
      }
    ],
    answervalidation: true,
    subheaders: [
      "",
      "!{}",
      "!{SIM_R2_Scenario1_FB_Solution}",
      "!{SIM_R2_Scenario2_FB_Solution}"
    ],
    trackQuestion: "",
    rankQuestion: "",
    sortByQuestion: "",
    sortOrder: "asc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: true,
    markUpdate: true
  }]]></Component>
  


<!-- ******* -->
<!-- RANKING -->
<!-- ******* -->

  <Component type="IndividualResults" customJS="true"><![CDATA[{
    templateInEvent: "html/rankingTable.dot",
    css: "styles/rankingTable.css",
    class: "",
    position: "#",
    _rank: 
      {
        title: "!{Ranking_Position}",
        binding: "Score_SIM_Total_R2_Rank"
      },
    header: "!{}",
    _subheader: "!{SIM_R2_FAC_debrief_ranking}",
    userHeader: "!{Ranking_User}",
    _me: "!{Me}",
    boundName: "Q_My_Name",
    showOriginalName: true,
    avatar: " ",
    boundAvatar: "Q_My_Avatar",
    defaultAvatar: "!{defaultAvatar}",
    isDataTables: false,
    class: "_verticalMode_ myresponsive-table _noBold _fixed",
    questions: [
      {
        show: true,
        title: "!{KPI_Metric1}",
        binding: "Score_SIM_Total_KPI1"
      },
      {
        show: true,
        title: "!{KPI_Metric2}",
        binding: "Score_SIM_Total_KPI2"
      },
      {
        show: true,
        title: "!{KPI_Metric3}",
        binding: "Score_SIM_Total_KPI3"
      },
      {
        show: true,
        title: "!{KPI_TOTAL}",
        binding: "Score_SIM_Total",
				format: "0.0"
      }
    ],
    _trackQuestion: "Team",
    rankQuestion: "",
    sortByQuestion: "Score_SIM_Total",
    sortOrder: "desc",
    listSkip: 0,
    listLength:100,
    showNear: 5,
    liveUpdate: true,
    markUpdate: true
  }]]></Component>
  






  <!-- Navigation component
       parameters:
        - aligned:
        - animate: kind of animation
        - id: to be located and shown when is hidden
        - isHidden: attribute to hide
        - buttons: array of buttons (previous, next or target)
   -->
  <Component type="Navigation" customJS="true">
    <![CDATA[{
      templateInEvent: "html/navigation.dot",
      css: "styles/navigation.css",
      aligned: "right",
      animate: "fadeInLeft animate__delay-2s",
      id: "navButton",
      isHidden: false, showDelay: "",
      hasModals: true,
      buttons: [
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_menu",
          popup: "FAC_Navigation",
          close: "!{Header_LinkClose}",
          label: "!{Navigation_menu}",
          icon: "list"
        },
        {
          type: "modal",
          pulse: false,
          popupID: "Modal_finish",
          popup: "SIM_R2_FAC_dashboard_finish",
          close: "!{Header_LinkClose}",
          label: "!{GD_SIM_R2_Finish}",
          icon: "cancel"
        }
      ],
      
      isFollower: "Follower",
      scope: ["Follower"]
    }]]>
  </Component>


  <Voting autoNext="false">
    <Score type="Vote" result="Q_FAC_Navigation_Tab" response="1"/> 
  </Voting>



</Action>