define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', 'evaluator', '../styles/materialize-src/js/materialize.min'], function ($, Q, WizerApi, WizletBase, doT, Evaluator) {

    var Navigation = function () {
        this.type = 'Navigation';
        this.level = 1;
    };

    Navigation.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;

        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];

        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });


        $(this.wizletContext).bind("wizer:action:wizletBase:scope-reFetch:complete", function () {
            self.reDraw();
        });

        //VideoPlayer.onLoad(null, info, content, wizerApi);
        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    Navigation.prototype.unloadHandler = function () {
        //Remove any tooltip before jumping to another screen
        $('.material-tooltip').remove();
        $(document).off("wizer:model:change", this.evaluation_event);
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        WizletBase.unloadHandler({ wizlet: this });
    };
    

    Navigation.prototype.evaluation = function () {
        var self = this;
            
        var isEnabled = Evaluator.parseAndEval(self.wizletInfo.enable_eval, self.wizletInfo);
        if (isEnabled) {
            //console.log('** Navigation Enabled **')
            self.wizletContext.find('#'+self.id+':not([disabled])').removeAttr('hidden invisible');
            //$(document).off("wizer:model:change."+self.id, self.evaluation_event);
        } else {
            //console.log('** Navigation Not-Enabled **')
            self.wizletContext.find('#'+self.id+':not([disabled])').attr('invisible',true);
        }
    }
    Navigation.prototype.evaluation_event = function (event) {
        var self = event.data.self;
        
            //Load the updated values in all the scoped variables
            var questionIds = [];
            $.each(self.wizletInfo.scope, function (idx, condition) {
                questionIds.push( self.wizerApi.getQuestionIdByName(condition) );
            });

            self.wizerApi.getMyVotes(questionIds).then(function (response) { 
                
                $.each(questionIds, function (idx, questionId) {

                    if ( response.votes[questionId] && response.votes[questionId][0] ) {

                        self.wizletInfo.DB[ self.wizletInfo.scope[idx] ] = response.votes[questionId][0];
    
                        //Check if the evaluation enables with the new updated values
                        var isEnabled = Evaluator.parseAndEval(self.wizletInfo.enable_eval, self.wizletInfo);
                        if (isEnabled) {
                            //console.log('** Navigation Enabled **')
                            self.wizletContext.find('#'+self.id+':not([disabled])').removeAttr('hidden invisible');
                            //$(document).off("wizer:model:change."+self.id, self.evaluation_event);
                        } else {
                            //console.log('** Navigation Not-Enabled **')
                            self.wizletContext.find('#'+self.id+':not([disabled])').attr('invisible',true);
                        }
    
                    }
                });

            });  

    }

    Navigation.prototype.render = function (options) {
        var self = this;
        return self.templateDefer.promise.then(function (template) {
            var fragment = template(options.wizletInfo);
            options.context.html(fragment);
            self.id = options.wizletInfo.id ? options.wizletInfo.id : '';
                        
            if (options.wizletInfo.enable_eval) {
                self.evaluation();
                $(document).on("wizer:model:change."+self.id, {self: self}, self.evaluation_event);
            }

            //Jump to previous screen
            options.context.find("[data-previousAction]").off("click").one("click", function (e) {
                if (typeof $(this).data("gdembed") == 'undefined') 
                    window.sendAndPrevious();
            });
            //Jump to next screen
            options.context.find("[data-nextAction]").off("click").one("click", function (e) {
                
                var $button = $(this);

                if (options.wizletInfo.updatemodel) {
                    self.wizerApi.calcBinderCache.setCalcValue(options.wizletInfo.updatemodel.model, options.wizletInfo.updatemodel.input, options.wizletInfo.updatemodel.inputval).then(function () {
                        console.log(options.wizletInfo.updatemodel.input, "updated to "+options.wizletInfo.updatemodel.inputval)
                        if (typeof $button.data("gdembed") == 'undefined') 
                            window.sendAndNext();
                    })
                } else {
                    if (typeof $button.data("gdembed") == 'undefined') 
                        window.sendAndNext();
                }

            });

            
            //Jump to an specific screen
            options.context.find("[data-action]").off("click").one("click", function (e) {                  


                // Remove all localStorage variables used to control some decisions before leaving
                if (options.wizletInfo.resetLocalStorage) {
                    $.each(options.wizletInfo.resetLocalStorage, function (idx, local) {
                        localStorage.removeItem(local);
                    });
                }
                // Remove all votes used to control the Quiz answers before start
                if (options.wizletInfo.resetVotes) {
                    self.removeVotes(options.wizletInfo.resetVotes);
                }
                
                var $button = $(this);

                if (options.wizletInfo.updatemodel) {
                    self.wizerApi.calcBinderCache.setCalcValue(options.wizletInfo.updatemodel.model, options.wizletInfo.updatemodel.input, options.wizletInfo.updatemodel.inputval).then(function () {
                        console.log(options.wizletInfo.updatemodel.input, "updated to "+options.wizletInfo.updatemodel.inputval)
                        if (typeof $button.data("gdembed") == 'undefined') 
                            wizerApi.jump($button.data("action"), false);
                    });
                } else {
                    if (typeof $button.data("gdembed") == 'undefined') 
                        wizerApi.jump($button.data("action"), false);                    
                }
            });

            
            //Groupd Director Action: jump participant to a section
            options.context.find("[data-gdaction]").on("click", function (e) {

                var $button = $(this);

                //in pause buttons, mark selected option
                if ($button.hasClass('pause')) {
                    $button.parent().parent().find("a.pause").removeClass('clicked');
                    $button.addClass('clicked');
                }

                
                var question = $button.data("gdtrack") ? $button.data("gdtrack") : 'GD';

                var waitForActionId = self.wizerApi.lookingUpActionIdIfNotNumeric( $button.data("gdaction") );
                waitForActionId.then(function (actionId) {
                    AjaxGetJson('Vote', 'SetGroupCurrentActionId', 'trackQuestionId=' + self.wizerApi.getQuestionIdByName(question) + '&actionId=' + actionId, function (result) {
                        if (!result.actionId) {
                            console.log('Fail in SetGroupCurrentActionId');
                        }
                    });
                });
                
            });

            //Groupd Director Action: Embed action (as aggregation)
            options.context.find("[data-gdembed]").one("click", function (e) {
                
                var $button = $(this);

                var embedFile = $button.data("gdembed");
                var embedIdx = $button.data("index");
                
                //if command, then run first the command
                if (typeof $button.data("gdcommand") == 'undefined')
                if (embedFile) {
                    
                    var optionsObj = {actionXML: embedFile};
                    var embedding = require('wizletEmbedding');
                    embedding.unembedWizletExcept('MenuComponent');
                    optionsObj.selector = '#embedding-'+embedIdx;
                    optionsObj.noscroll = true;
                    embedding.embedWizlet({
                        context: $(document),
                        actionScriptName: embedFile,
                        options: optionsObj
                    }).then(function() {
                        if (typeof $button.data("previousaction") !== 'undefined') 
                            window.sendAndPrevious();

                        if (typeof $button.data("nextaction") !== 'undefined') 
                            window.sendAndNext();
                                
                        if (typeof $button.data("action") !== 'undefined') 
                            wizerApi.jump($button.data("action"), false);
                    });
                }
                else {
                    self.wizerApi.showError('actionXML attribute missing from options');
                }
                
            });

            
            //Groupd Director Action: Command action (as EmbedWizlet)
            options.context.find("[data-gdcommand]").one("click", function (e) {
                
                var $button = $(this);

                var command = $(this).data("gdcommand");
                var embedFile = $(this).data("gdcommandxml");
                
                if (embedFile) {
                    var question = $(this).data("gdtrack") ? $(this).data("gdtrack") : 'GD';
                    var optionsObj = {actionName: embedFile};
                    AjaxGetJson('Vote', 'SendClientCommandToGroup', 'trackQuestionId=' + self.wizerApi.getQuestionIdByName(question) + '&cmd=' + command + '&options=' + JSON.stringify(optionsObj), function (result) {
                        if (!result.success) {
                            console.log('Fail in SendClientCommandToGroup');
                        } else {

                            //ensure embed object runs after the command                            
                            // if (typeof $button.data("gdcommand") != 'undefined') {
                            //     var embedFile = $button.data("gdembed");
                            //     var embedIdx = $button.data("index");
                                                            
                            //     var optionsObj = {actionXML: embedFile};
                            //     var embedding = require('wizletEmbedding');
                            //     embedding.unembedWizletExcept('MenuComponent');
                            //     optionsObj.selector = '#embedding-'+embedIdx;
                            //     optionsObj.noscroll = true;
                            //     embedding.embedWizlet({
                            //         context: $(document),
                            //         actionScriptName: embedFile,
                            //         options: optionsObj
                            //     }).then(function() {
            
                            //         if (typeof $button.data("previousaction") !== 'undefined') 
                            //             window.sendAndPrevious();
            
                            //         if (typeof $button.data("nextaction") !== 'undefined') 
                            //             window.sendAndNext();
                                            
                            //         if (typeof $button.data("action") !== 'undefined') 
                            //             wizerApi.jump($button.data("action"), false);
                            //     });
                            // }
                        }
                    });
                }
                else {
                    self.wizerApi.showError('actionName attribute missing from options');
                }
                              
            });


            //Load a screen into a modal window
            options.context.find("[data-modal]").off("click").one("click", function (e) {
                
                options.context.find('.modal').modal();

                self.loadPage(
                    $(this).data('modal'), 
                    options.context, 
                    self.unsedContext, 
                    $( $( $(this).attr('href')).find('.modal-content') ) 
                );
            });

            //Load the inner screen into the current action window
            options.context.find("[data-inner-action]").off("click").one("click", function (e) {
                
                // Remove all localStorage variables used to control some decisions before leaving
                if (options.wizletInfo.resetLocalStorage) {
                    $.each(options.wizletInfo.resetLocalStorage, function (idx, local) {
                        localStorage.removeItem(local);
                    });
                }
                // Remove all votes used to control the Quiz answers before start
                if (options.wizletInfo.resetVotes) {
                    self.removeVotes(options.wizletInfo.resetVotes);
                }

                self.loadPage(
                    $(this).data("inner-action"), 
                    options.context, 
                    self.unsedContext, 
                    $('.mainArea'));
            });
            
            //Load a different inner screen depending on whether the checked option is correct or not
            options.context.find("[data-inner-action-check]").off("click").one("click", function (e) {
                
                var isCorrect;
                var quizType = options.wizletInfo.quizType;

                //Single option
                if (quizType === "radio") {

                    isCorrect = $('[type="radio"]:checked').data('correct');

                } 
                //Multiple option
                else if (quizType === "checkbox") {

                    isCorrect = true;
                    $.each($('[type="checkbox"]'), function (idx, option) {

                        if ($(option).is(':checked')) {
                            //only if allCorrect
                            isCorrect = isCorrect && $(this).data('correct');
                        }else {    
                            //only if allCorrect
                            isCorrect = isCorrect && !$(this).data('correct');
    
                        }
                    });

                } else {

                    isCorrect = true;
                }

                self.loadPage(
                    isCorrect ? $(this).data("inner-action-correct") : $(this).data("inner-action-incorrect"), 
                    options.context, 
                    self.unsedContext, 
                    $('.mainArea'));
            });

            //Init materialize tooltip components
            options.context.find('.tooltipped.tnavigation').tooltip( {
                enterDelay: 1000
            });

            // When innerNavigation in operated events, check what screen to come back if the screen has been refreshed and we are at the first again
            if (options.wizletInfo.innerNavigation) {
                
                var nav = options.wizletInfo.innerNavigation;

                var questId = self.wizerApi.getQuestionIdByName(nav.bind);
                self.wizerApi.getMyVotes([questId]).then(function(response) {
                    if (response && response.votes && response.votes[questId]) {
                        var screenNumber = response.votes[questId][0];
                        if (screenNumber) {                            
                            self.loadPage(
                                nav.targetName+screenNumber,
                                options.context, 
                                self.unsedContext, 
                                $('.mainArea'));
                        }
                    }
                });

            };


            // When innerNavigation in operated events, Auto jump to the last Screen visited 
            if (options.wizletInfo.autoNavigation) {
                
                var nav = options.wizletInfo.autoNavigation;

                var questId = self.wizerApi.getQuestionIdByName(nav.bind);
                self.wizerApi.getMyVotes([questId]).then(function(response) {
                    if (response && response.votes && response.votes[questId]) {
                        var screenNumber = response.votes[questId][0];
                        if (screenNumber>0) {        
                            wizerApi.jump(nav.targetName+screenNumber, false);   
                        }
                    }
                });

            };

            
            //Show the hidden navigation button after a delay
            if (options.wizletInfo.showDelay)
                setTimeout(
                    function() { 
                        options.context.find('#'+self.id+':not([disabled])').removeAttr('hidden invisible');
                    }, 
                    options.wizletInfo.showDelay
                );

            return true;
        })
        .fail(this.wizerApi.showError);
    };



    /**
     * Load an action screen inside a modal window
     */
    Navigation.prototype.loadPage = function (actionXMLName, context, unusedContext, tabContainer) {
        var self = this;       

        //unload previous 
        if (self.currentWizletModule && self.currentWizletModule.length > 0) {
            $.each(self.currentWizletModule, function (index, module) {
                if (module.wizletInstance.unloadHandler) {
                    module.wizletInstance.unloadHandler();
                }
            });
        }
        
        $('.material-tooltip').remove();
        
        var loading = self.wizerApi.loadActionInContainer(actionXMLName, context, unusedContext, tabContainer);
        loading.then(function (loads) {
           
            self.currentWizletModule = loads;
            
            var page = "wizer:action:init.mainArea";
                        
            $(document).trigger(page, actionXMLName);
        });
        
    };


    Navigation.prototype.reDraw = function () {
        var self = this;
        WizletBase.reEvaluateNavigation({ navigationItems: self.wizletInfo.navigationItems, scope: self.wizletInfo, wizerApi: self.wizerApi, wizletContext: self.wizletContext });
    };

    
    Navigation.prototype.removeVotes = function (questionName) {
        var self = this; 
        var myVotes = [];
        questionName.forEach(function(q) { 
            myVotes.push( { questionId: self.wizerApi.getQuestionIdByName(q) } ); 
        });        
        self.wizerApi.removeVotes({ votes: myVotes });
    };

    Navigation.getRegistration = function () {
        return new Navigation();
    };

    return Navigation;

});