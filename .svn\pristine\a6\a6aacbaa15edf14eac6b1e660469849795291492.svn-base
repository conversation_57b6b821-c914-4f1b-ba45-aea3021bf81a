.bubbles-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
}
.bubbles-wrapper .bubble {
  border-radius: 50%;
  -webkit-background-clip: padding-box;
  -moz-background-clip: padding;
  background-clip: padding-box;
  -webkit-box-shadow: 0 20px 30px rgba(0, 0, 0, 0.2), inset 0px 10px 30px 5px #cc4a3d;
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.2), inset 0px 10px 30px 5px #cc4a3d;
  position: absolute;
  height: 200px;
  width: 200px;
  -o-animation-name: animateBubble, sideWays;
  -webkit-animation-name: animateBubble, sideWays;
  animation-name: animateBubble, sideWays;
  -o-animation-iteration-count: infinite, infinite;
  -webkit-animation-iteration-count: infinite, infinite;
  animation-iteration-count: infinite, infinite;
  -o-animation-timing-function: linear, ease-in-out;
  -webkit-animation-timing-function: linear, ease-in-out;
  animation-timing-function: linear, ease-in-out;
  -o-animation-direction: normal, alternate;
  -webkit-animation-direction: normal, alternate;
  animation-direction: normal, alternate;
}
.bubbles-wrapper .bubble.doubleBubble:after {
  background: rgba(204, 74, 61, 0.5);
  background: -webkit-gradient(radial, center center, 0px, center center, 100%, color-stop(0%, rgba(204, 74, 61, 0.5)), color-stop(70%, rgba(204, 74, 61, 0)), color-stop(100%, rgba(204, 74, 61, 0)));
  background: -webkit-radial-gradient(center, ellipse farthest-corner, rgba(204, 74, 61, 0.5) 0%, rgba(204, 74, 61, 0) 70%, rgba(204, 74, 61, 0) 100%);
  background: -o-radial-gradient(center, ellipse farthest-corner, rgba(204, 74, 61, 0.5) 0%, rgba(204, 74, 61, 0) 70%, rgba(204, 74, 61, 0) 100%);
  background: -webkit-radial-gradient(center, ellipse, rgba(204, 74, 61, 0.5) 0%, rgba(204, 74, 61, 0) 70%, rgba(204, 74, 61, 0) 100%);
  background: -o-radial-gradient(center, ellipse, rgba(204, 74, 61, 0.5) 0%, rgba(204, 74, 61, 0) 70%, rgba(204, 74, 61, 0) 100%);
  background: radial-gradient(ellipse at center, rgba(204, 74, 61, 0.5) 0%, rgba(204, 74, 61, 0) 70%, rgba(204, 74, 61, 0) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=$color1, endColorstr=$color3,GradientType=1 );
  border-radius: 50%;
  -webkit-background-clip: padding-box;
  -moz-background-clip: padding;
  background-clip: padding-box;
  -webkit-box-shadow: inset 0 20px 30px rgba(204, 74, 61, 0.3);
  box-shadow: inset 0 20px 30px rgba(204, 74, 61, 0.3);
  content: "";
  height: 180px;
  width: 180px;
  left: 10px;
  top: 10px;
  position: absolute;
}
.bubbles-wrapper .bubble.x0 {
  -o-animation-duration: 27s, 4s;
  -webkit-animation-duration: 27s, 4s;
  animation-duration: 27s, 4s;
  -o-transform: scale(0.7);
  -ms-transform: scale(0.7);
  -webkit-transform: scale(0.7);
  transform: scale(0.7);
  left: -10%;
  top: 40%;
}
.bubbles-wrapper .bubble.x1 {
  -o-animation-duration: 24s, 4s;
  -webkit-animation-duration: 24s, 4s;
  animation-duration: 24s, 4s;
  -o-transform: scale(0.8);
  -ms-transform: scale(0.8);
  -webkit-transform: scale(0.8);
  transform: scale(0.8);
  left: 0%;
  top: 30%;
}
.bubbles-wrapper .bubble.x2 {
  -o-animation-duration: 22s, 2s;
  -webkit-animation-duration: 22s, 2s;
  animation-duration: 22s, 2s;
  -o-transform: scale(0.8);
  -ms-transform: scale(0.8);
  -webkit-transform: scale(0.8);
  transform: scale(0.8);
  left: 10%;
  top: 70%;
}
.bubbles-wrapper .bubble.x3 {
  -o-animation-duration: 26s, 3s;
  -webkit-animation-duration: 26s, 3s;
  animation-duration: 26s, 3s;
  -o-transform: scale(0.7);
  -ms-transform: scale(0.7);
  -webkit-transform: scale(0.7);
  transform: scale(0.7);
  left: 20%;
  top: 0%;
}
.bubbles-wrapper .bubble.x4 {
  -o-animation-duration: 27s, 4s;
  -webkit-animation-duration: 27s, 4s;
  animation-duration: 27s, 4s;
  -o-transform: scale(0.8);
  -ms-transform: scale(0.8);
  -webkit-transform: scale(0.8);
  transform: scale(0.8);
  left: 30%;
  top: 50%;
}
.bubbles-wrapper .bubble.x5 {
  -o-animation-duration: 30s, 3s;
  -webkit-animation-duration: 30s, 3s;
  animation-duration: 30s, 3s;
  -o-transform: scale(0.8);
  -ms-transform: scale(0.8);
  -webkit-transform: scale(0.8);
  transform: scale(0.8);
  left: 40%;
  top: 30%;
}
.bubbles-wrapper .bubble.x6 {
  -o-animation-duration: 25s, 4s;
  -webkit-animation-duration: 25s, 4s;
  animation-duration: 25s, 4s;
  -o-transform: scale(0.4);
  -ms-transform: scale(0.4);
  -webkit-transform: scale(0.4);
  transform: scale(0.4);
  left: 50%;
  top: 60%;
}
.bubbles-wrapper .bubble.x7 {
  -o-animation-duration: 24s, 2s;
  -webkit-animation-duration: 24s, 2s;
  animation-duration: 24s, 2s;
  -o-transform: scale(0.6);
  -ms-transform: scale(0.6);
  -webkit-transform: scale(0.6);
  transform: scale(0.6);
  left: 60%;
  top: 60%;
}
.bubbles-wrapper .bubble.x8 {
  -o-animation-duration: 25s, 4s;
  -webkit-animation-duration: 25s, 4s;
  animation-duration: 25s, 4s;
  -o-transform: scale(0.6);
  -ms-transform: scale(0.6);
  -webkit-transform: scale(0.6);
  transform: scale(0.6);
  left: 70%;
  top: 0%;
}
.bubbles-wrapper .bubble.x9 {
  -o-animation-duration: 29s, 2s;
  -webkit-animation-duration: 29s, 2s;
  animation-duration: 29s, 2s;
  -o-transform: scale(0.4);
  -ms-transform: scale(0.4);
  -webkit-transform: scale(0.4);
  transform: scale(0.4);
  left: 80%;
  top: 10%;
}
.bubbles-wrapper .bubble.x10 {
  -o-animation-duration: 25s, 4s;
  -webkit-animation-duration: 25s, 4s;
  animation-duration: 25s, 4s;
  -o-transform: scale(0.8);
  -ms-transform: scale(0.8);
  -webkit-transform: scale(0.8);
  transform: scale(0.8);
  left: 90%;
  top: 60%;
}
.bubbles-wrapper .bubble.x11 {
  -o-animation-duration: 22s, 2s;
  -webkit-animation-duration: 22s, 2s;
  animation-duration: 22s, 2s;
  -o-transform: scale(0.3);
  -ms-transform: scale(0.3);
  -webkit-transform: scale(0.3);
  transform: scale(0.3);
  left: 100%;
  top: 70%;
}
.bubbles-wrapper .bubble.x12 {
  -o-animation-duration: 27s, 4s;
  -webkit-animation-duration: 27s, 4s;
  animation-duration: 27s, 4s;
  -o-transform: scale(0.7);
  -ms-transform: scale(0.7);
  -webkit-transform: scale(0.7);
  transform: scale(0.7);
  left: 110%;
  top: 40%;
}
@-webkit-keyframes animateBubble {
  0% {
    margin-top: 100%;
  }
  100% {
    margin-top: -100%;
  }
}
@-o-keyframes animateBubble {
  0% {
    margin-top: 100%;
  }
  100% {
    margin-top: -100%;
  }
}
@keyframes animateBubble {
  0% {
    margin-top: 100%;
  }
  100% {
    margin-top: -100%;
  }
}
@-webkit-keyframes sideWays {
  0% {
    margin-left: 0px;
  }
  100% {
    margin-left: 50px;
  }
}
@-o-keyframes sideWays {
  0% {
    margin-left: 0px;
  }
  100% {
    margin-left: 50px;
  }
}
@keyframes sideWays {
  0% {
    margin-left: 0px;
  }
  100% {
    margin-left: 50px;
  }
}