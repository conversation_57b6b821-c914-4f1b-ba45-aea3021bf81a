﻿
    {{?it.fixedCountLabel}}
    <div class="voteCountLabel fixed">
        <span class="new badge" data-groupdirectorvotecount
              data-badge-caption="{{?it.voteCountLabel}}{{=it.voteCountLabel}}{{??}}Vote count :{{?}}">0</span>
    </div>
    {{?}}


<div class="container"> 
    <h4 class="header">{{=it.header}}</h4>
    
    {{?!it.hideCountLabel && !it.fixedCountLabel}}
    <div class="voteCountLabel row center">
        <span class="new badge" data-groupdirectorvotecount
              data-badge-caption="{{?it.voteCountLabel}}{{=it.voteCountLabel}}{{??}}Vote count :{{?}}">0</span>
    </div>
    {{?}}

    <div class="row">
        <div class="col s12">
            <ul class="tabs tabs-fixed-width z-depth-1"> 
            {{?it.tabs}}
            {{~it.tabs :tab:idx}}
                <li class="tab">
                    <a href="#tab{{=idx}}" {{?idx===0}}class="active"{{?}}>
                        <span class="strong">{{=tab.title}}</span>                        
                    </a>
                </li>
            {{~}}
            {{?}}
            </ul>
        </div>
        
        {{?it.tabs}}
        {{~it.tabs :tab:tabIndex}}
            <div id="tab{{=tabIndex}}" class="col s12">
                <div class="card-panel client-colors {{=tab.bgColor}}">
                    <span class="client-colors-text text-font2 strong">{{=tab.body}}</span>

                        <div class="collection" data-groupdirectormainmenu>
                            {{~tab.actions: option:optionIndex}}
                                <a  class="collection-item" name="{{=option.type}}{{=optionIndex}}" id="{{=option.type}}{{=optionIndex}}" value="{{=option.title}}"
                                    data-tab="{{=tabIndex}}" data-index="{{=optionIndex}}" data-type="{{=option.type}}" data-groupdirectormainoption>
                                    <span class="word-wrap">{{=option.title}}</span>
                                    <i class="material-icons right">{{=option.icon}}</i>
                                </a>
                                
                                {{?option.type=='Embed'}}
                                <div class="embeddingWrapper" id="embedding-{{=optionIndex}}" data-embed></div>
                                {{?}}
                            {{~}}
                        </div>

                </div>
            </div>
        {{~}}
        {{?}}

        
    </div>

    {{?it.showReset}}
    <div class="row">
        <a href="#resetModal" class="btn modal-trigger client-colors red client-colors-text text-white">
            <i class="medium material-icons right">delete_forever</i>{{=it.resetBtn.label}}
        </a>
    </div>

    
    <div id="resetModal" class="modal">
        <div class="modal-content">
            <h4>{{=it.resetBtn.modal.header}}</h4>
            <p>{{=it.resetBtn.modal.text}}</p>
        </div>
        <div class="modal-footer">
            <a class="btn modal-close  client-colors button2">
                <i class="small material-icons right">close</i>{{=it.resetBtn.modal.close}}
            </a>
            <a id="resetBtn" class="btn   client-colors red">
                <i class="small material-icons right">delete_forever</i>{{=it.resetBtn.modal.reset}}
            </a>
        </div>
    </div>
    {{?}}

</div>
    
    