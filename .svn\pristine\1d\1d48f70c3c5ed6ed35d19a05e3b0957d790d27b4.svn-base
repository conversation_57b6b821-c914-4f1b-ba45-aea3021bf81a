﻿define(['jquery', 'Q', 'wizer-api', 'wizletBase', 'doT', '../styles/materialize-src/js/materialize.min', './rangeSlider/rangeSlider.min'], function ($, Q, WizerApi, WizletBase, doT) {

    var Range = function () {
        this.type = 'Range';
        this.level = 1;
        this.lastValues = [];
    };

    Range.prototype.loadHandler = function (unused, wizletInfo, wizletContext, wizerApi) {
        this.wizletInfo = wizletInfo;
        this.wizletContext = wizletContext;
        this.wizerApi = wizerApi;
        
        this.templateDefer = Q.defer();
        var self = this;
        var requirements = [];
        
        requirements.push('doT!' + 'events/' + wizerApi.eventName() + '/' + wizletInfo.templateInEvent);
        

        if (wizletInfo.css) {
            requirements.push(WizletBase.loadCss(wizletInfo));
        }
        require(requirements, function (doTTemplate, css) {
            self.templateDefer.resolve(doTTemplate);
        });

        return WizletBase.loadHandler({ wizlet: this, render: this.render });

    };

    Range.prototype.unloadHandler = function () {
        $(this.wizletContext).unbind("wizer:action:wizletBase:scope-reFetch:complete");
        WizletBase.unloadHandler({ wizlet: this });
    };

    Range.prototype.render = function (options) {
        var self = this;

        if (self.rendering) {
            return self.rendering.promise;
        }
        self.rendering = new Q.defer();

        
        var validating = self.validate();
        var validating2 = self.validate2();

        validating.then(function () {
        validating2.then(function () {

            self.templateDefer.promise.then(function (template) {
                var fragment = template(options.wizletInfo);
                options.context.html(fragment);
                    
                var inputs = options.context.find('input[type="text"]');
                //self.reset(options.context.find('input[type="text"]'));

                if (!options.wizletInfo.submitBtn && !options.wizletInfo.noInitValues && !self.initValues) {                
                    $(document).one('wizer:action:init', function(e, currentAction) {
                        self.init(inputs);
                    });
                }

                //if (options.wizletInfo.distinctValues)
                    self.lastValues=inputs.filter('[data-value]').map(function (idx,ele) {
                                            return $(ele).data('value');
                                    }).get();
                


                if (options.wizletInfo.total && options.wizletInfo.total.binding) 
                    self.updateTotal(inputs, options.wizletInfo.total.binding, options.wizletInfo.total.maxValue);

                

                if (options.wizletInfo.loadStoredValues && options.wizletInfo.saveInModel) {
                    
                    if (options.wizletInfo.saveInModelLater) {
                        $.each(options.wizletInfo.sliders, function (idx, slider) {
                            options.context.find('#'+slider.id).addClass('saveInModelLater');
                        });
                    } else {
                        //after WIZER MODEL is loaded, we can update renders
                        $(document).one('wizer:action:init', function(e, currentAction) {
                            $.each(options.wizletInfo.sliders, function (idx, slider) {
                                options.context.find('#'+slider.id).trigger('updateModelInput');
                            });
                        });

                    }
                }


                $.each(options.wizletInfo.sliders, function (idx, slider) {
                    
                    var rangeoptions = slider.options;


                    rangeoptions.onFinish = function (data) {
                        var bind = $(data.input).data('bind') || $(data.input).data('binding');
                        
                        if (bind) {
                            var value = (slider.saveValues && data.from_value) ? data.from_value : 
                                        ( data.from_value ? (data.from + slider.options.min) : data.from );
                            
                            if (rangeoptions.type=="double") {
                                value = value + ';' + (data.to_value ? data.to_value : data.to);
                            }
                            
                            $(data.input).attr('data-value',value);
                            $(data.input).data('value',value);
                            
                            if (options.wizletInfo.submitBtn) {
                                if (options.wizletInfo.submitBtn.hidden) options.context.find("#submitBtn").removeAttr('hidden');
                            } else {

                                self.addVote(bind, value)
                                if (slider.calcDifference) {
                                    self.addVote(slider.calcDifference, (slider.options.max-value+1))
                                }

                            }

                            if (options.wizletInfo.total && options.wizletInfo.total.binding) {
                                self.updateTotal(inputs, options.wizletInfo.total.binding, options.wizletInfo.total.maxValue, $(data.input));
                            } else {
                                if (options.wizletInfo.saveInModel) {
                                    
                                    if (options.wizletInfo.saveInModelLater) {
                                        data.input.addClass('saveInModelLater')
                                    } else {
                                        data.input.trigger('updateModelInput');
                                    }
                                }
                            }

                            if (options.wizletInfo.distinctValues) {
                                self.distinctValues(value, $(data.input).data('idx'), inputs);
                            }
                        }
                    };

                    // rangeoptions.onStart = function (data) {                    
                    //     if (slider.icon) {
                    //         options.context.find('#'+slider.id).prev().removeClass('with-icon');
                    //     }
                    // };
                    rangeoptions.onChange = function (data) {                    
                        if (slider.icon) {
                            options.context.find('#'+slider.id).prev().removeClass('with-icon').find('.irs-bar').
                                css( 'left', '-=6px');    
                        }
                    };


                    if (slider.options.prettify) {
                        rangeoptions.prettify = self[slider.options.prettify];
                    } else {
                        if (slider.custom_labels) {
                            rangeoptions.prettify = function (num) {
                                if (num === rangeoptions.min) {
                                        return slider.custom_labels.min_label.replace(/ %\s*$/, "");
                                } else if (num === rangeoptions.max) {
                                        return slider.custom_labels.max_label.replace(/ %\s*$/, "");
                                } else {
                                    return num;
                                }
                            }
                        }

                    }

                    options.context.find('#'+slider.id).ionRangeSlider(rangeoptions);

                    
                    if (slider.icon) {
                                   
                        options.context.find('#'+slider.id).prev().addClass('with-icon').find('.irs-slider').
                            css({'background-image': 'url(Wizer/Pages/Events/' + wizerApi.eventName() + '/' + slider.icon+')',
                                    'background-size': '100% 100%' ,
                                    'width': '40px',
                                    'height': '40px',
                                    'top': '20px',
                                    'left': '-=6px'});

                    }

                });




                if (self.initValues && !self.wizletInfo.noBlockAnswer) {
                    $(self.wizletContext.find('#submitBtn')).closest('.row').prop('hidden', true);
                    $(self.wizletContext.find('#submitBtn')).prop('hidden', true);
                             
                    $.each(inputs, function (idx, input) {   
                        $(input).closest('div.range').addClass('disabled');
                    });
                        
                }


                //Save text from inputs
                options.context.find('#submitBtn').on('click', function (event) {

                    if (options.wizletInfo.noBlockAnswer) {
                        $(this).removeClass('pulse');
                    } else {
                        $(this).closest('.row').prop('hidden', true);
                        $(this).prop('hidden', true);
                    }

                    var points=0;
                    $.each(inputs, function (idx, input) {
                    
                        if (!options.wizletInfo.noBlockAnswer) $(input).closest('div.range').addClass('disabled');
                        
                        //var slider = options.wizletInfo.sliders[idx];
            
                        var bind = $(input).data('bind') || $(data.input).data('binding');
                        if (bind) {
                            //var value = slider.saveValues ? $(input).data('value') : ($(input).data('value') + slider.options.min) ;
                                var value = $(input).data('value');
                                self.addVote(bind, value );

                            if (options.wizletInfo.total && options.wizletInfo.total.binding) 
                                self.updateTotal(inputs, options.wizletInfo.total.binding, options.wizletInfo.total.maxValue);


                                //if (options.wizletInfo.score && options.wizletInfo.score.points) {
                                    var correctRange = options.wizletInfo.sliders[idx].correctRange;
                                    if (correctRange) {
                                        var isCorrect = false;
                                        if ( (value >= correctRange.from) && (value <= correctRange.to) ) {
                                            isCorrect = true;
                                            if (options.wizletInfo.score && options.wizletInfo.score.points)
                                                points += parseInt(options.wizletInfo.score.points);
                                        }
                                        
                                        $(input).closest('.row').find('i.check-icon').removeAttr('hidden').
                                            html(isCorrect ? 'check_circle' : 'cancel').
                                            removeClass('green-text red-text').
                                            addClass(isCorrect ? 'green-text' : 'red-text');
                                    }
                                //}
                                

                        }    
        
                    });

                    if (options.wizletInfo.score && options.wizletInfo.score.question) {          
                        self.addVote(options.wizletInfo.score.question, points);
                    }

                    if (options.wizletInfo.submitBtn.toast)
                        M.toast({
                            html: options.wizletInfo.submitBtn.toast,
                            classes: 'rounded'
                        });

                    $(this).removeClass('pulse');

                    if (options.wizletInfo.submitBtn.idToShow)
                        $(options.wizletInfo.submitBtn.idToShow).removeAttr('hidden');

                });

                if (options.wizletInfo.moreInfo)
                options.context.find('ul.collapsible').collapsible({
                    onOpenStart : function(current_item) {
                        $(current_item).find('i.expand').html('expand_less');
                    },
                    
                    onCloseStart : function(current_item) {
                        $(current_item).find('i.expand').html('expand_more');                        
                    }
                });

                self.rendering.resolve();
                    
            
            });
        

        });
        });


        return self.rendering.promise;
        
    };

    /**
     * Custom functions called by their names to prettify the output values
     */
    Range.prototype.my_prettify_percentage = function (n) {
        return n + " %";
    };
    Range.prototype.my_prettify_percentage2 = function (n) {
        return n + " %" + " - " + (100-n) + " %";
    };



    Range.prototype.validate = function () {
        var self = this;
        var validating = new Q.defer();

        
        //Sliders options loaded from a braisntorm votes, in order to give a value to each participant's opinion/idea
        if (self.wizletInfo.optionsFromVote) {
            var binding = self.wizerApi.getQuestionIdByName(self.wizletInfo.optionsFromVote);
            
            self.wizerApi.getVotes({questionIds: [binding]}).then(function(response){
                
                if (self.wizletInfo.votesInOrder){
                    response.votes.sort(function(a, b){return a.participantId-b.participantId});
                } else {
                    response.votes = response.votes.reverse();
                }
                var sliders = [];
                var val;

                var cont=0;
                response.votes.forEach(function(vote,i) {
                    
                    val = vote.responseText;
                    cont ++;
                    if (self.wizletInfo.includeMyVote || (Wizer.ParticipantEmail!==vote.participantEmail) ) {
                        
                        var slider = $.extend(true, {}, self.wizletInfo.slider, 
                            {
                              id: "slider"+cont,
                              cardTitle: "",
                              sliderTitle: val,
                              bind: self.wizletInfo.slider.bind+cont,
                            });

                        sliders.push(slider);
                    }
                });

                self.wizletInfo.sliders = sliders;

                validating.resolve(true);
                
            });
        } else {
            
            if (self.wizletInfo.total && self.wizletInfo.total.maxValue) {

                var total = self.wizletInfo.total;

                if (total.maxValue.val && !total.maxValue.restrictionBind) {
                    self.maxVal = total.maxValue.val;
                    validating.resolve();
                } else if (total.maxValue.restrictionBind) {

                    if (total.maxValue.getFromModel) {
            
                                                        
                        self.wizerApi.calcBinderCache.getCalcValue(total.maxValue.getFromModel, total.maxValue.restrictionBind).then(function(value){
                            self.maxVal = value ? parseInt(value) : total.maxValue.val;
                            validating.resolve(true);
                        });


                    } else {

                        var questionId = self.wizerApi.getQuestionIdByName(total.maxValue.restrictionBind);
                        self.wizerApi.getMyVotes([questionId]).then(function (votes) {
                            if (votes.votes && votes.votes[questionId] && votes.votes[questionId][0]) {
                                self.maxVal = parseInt(votes.votes[questionId][0]);
                            } else {
                                self.maxVal = total.maxValue.val;
                            }
                            validating.resolve(true);
                        });
                        self.maxVal = 0;

                    }

                } else {
                    self.maxVal = 0;
                    validating.resolve(true);
                }

            } else {
                validating.resolve(true);
            }
        }
        
        return validating.promise;
    };



    Range.prototype.validate2 = function () {
        var self = this;
        var validating = new Q.defer();

        
        self.initValues = false;

        //Sliders options loaded from a braisntorm votes, in order to give a value to each participant's opinion/idea
        if (self.wizletInfo.loadStoredValues) {

            var questionIds = [];
            $.each(self.wizletInfo.sliders, function (key, slider) {                
                questionIds[key] = self.wizerApi.getQuestionIdByName(slider.bind ? slider.bind : slider.binding);
            });

            self.wizerApi.getMyVotes(questionIds).then(function (response) {
                
                var idx=0;
                $.each(response.votes, function (key, vote) { 
                    if (vote && vote[0]) {
                        self.initValues = true;

                        if (self.wizletInfo.sliders[idx].saveValues) {
                            self.wizletInfo.sliders[idx].options.from = 
                                (self.wizletInfo.sliders[idx].options.values && self.wizletInfo.sliders[idx].options.values.length>0) ? self.wizletInfo.sliders[idx].options.values.indexOf(vote[0])  : 0;
                        } else {
                            self.wizletInfo.sliders[idx].options.from = 
                                (self.wizletInfo.sliders[idx].options.values && self.wizletInfo.sliders[idx].options.values.length>0) ? vote[0]-self.wizletInfo.sliders[idx].options.min : vote[0];
                        }

                    } else {
                        self.initValues = false;
                    }
                    idx ++;
                });
                validating.resolve(true);
            });

        } else {
            
            validating.resolve(true);
        }

        return validating.promise;
    };



    Range.prototype.reset = function (inputs) {
        var self = this; 

        var binds = _.unique(
                        inputs.filter('[data-bind]').map(function (idx,ele) {
                            return $(ele).data('bind');
                        }).get()
                    );
        self.removeVotes(binds);
    };
    
    Range.prototype.init = function (inputs) {
        var self = this; 

        var binds = inputs.filter('[data-bind]').map(function (idx,ele) {
                        return $(ele).data('bind');
                    }).get();

        if (binds.length == 0)
            binds = inputs.filter('[data-binding]').map(function (idx,ele) {
                return $(ele).data('binding');
            }).get();
        
        var values = inputs.filter('[data-value]').map(function (idx,ele) {
                            return $(ele).data('value');
                    }).get();

        self.addVotes(binds,values);

        

        var binddifs = inputs.filter('[data-binddif]').map(function (idx,ele) {
                        return $(ele).data('binddif');
                    }).get();  
        if (binddifs.length > 0)
            self.addVotes(binddifs,values);


        if (self.wizletInfo.saveInModel) {
            $.each(inputs, function (idx, input) {
                $(input).trigger('updateModelInput');
            });
        }

        self.lastValues=values;
    };

    Range.prototype.removeVotes = function (questionName) {
        var self = this; 

        var myVotes = [];
        if (Array.isArray(questionName)) {
            questionName.forEach(function(q) { myVotes.push( { questionId: self.wizerApi.getQuestionIdByName(q) } ); });
        } else {
            myVotes.push({ questionId: self.wizerApi.getQuestionIdByName(questionName) });
        }
        self.wizerApi.removeVotes({ votes: myVotes });
    };
    

    Range.prototype.updateTotal = function (inputs, total, maxValue, $updatedInput) {
        var self = this; 

        var values = inputs.filter('[data-value]').map(function (idx,ele) {
                            return $(ele).data('value');
                    }).get();

        var totalValue = 0;
        $.each(inputs, function (idx, input) {
            totalValue += $(input).data('value');
        });

        console.log(totalValue + " / " + self.maxVal)
        
        if (self.maxVal && $updatedInput) {
            //console.log(totalValue+' / '+self.maxVal);
            if (totalValue > self.maxVal) {

                var val, extra=0;
                //$.each(inputs, function (idx, input) {

                var recalcTotal = false;
                var index = $updatedInput.data('idx');

                    if (maxValue.setMaximumVal) {
                        extra = totalValue -self.maxVal;
                        val =  $updatedInput.data('value') - extra;
                        totalValue = self.maxVal;
                    } else {
                        val = self.lastValues[index];
                        recalcTotal = true;
                    }

                    if (self.wizletInfo.sliders[index].saveValues) {
                        var position = self.wizletInfo.sliders[index].options.values.indexOf(val);
                        if (position < 0) {
                            recalcTotal = true;
                            val = self.lastValues[index];
                            position = self.wizletInfo.sliders[index].options.values.indexOf(val);                        
                        }
                        $updatedInput.data("ionRangeSlider").update({from: position })
                    } else {
                        $updatedInput.data("ionRangeSlider").update({from:val})
                    }

                    $updatedInput.attr('data-value', val);
                    $updatedInput.data('value', val);

                    self.addVote($updatedInput.data('bind') ? $updatedInput.data('bind') : $updatedInput.data('binding'),val);


                    if (recalcTotal) {
                        totalValue = 0;
                        $.each(inputs, function (idx, input) {
                            totalValue += $(input).data('value');
                        });
                    }
                    
                //});
                self.wizletContext.find('.gauge.total').removeClass(maxValue.color).addClass(maxValue.colorMax);
                

            } else {
                self.lastValues = values;
                self.wizletContext.find('.gauge.total').removeClass(maxValue.colorMax).addClass(maxValue.color);
            }
        }

        if (maxValue.idToShow)
            if (totalValue == self.maxVal)
                $(maxValue.idToShow).removeAttr('hidden');
            else
                $(maxValue.idToShow).prop('hidden',true);
                    
        

        
        if (self.wizletInfo.saveInModel && $updatedInput) {
         
            if (self.wizletInfo.saveInModelLater) {
                $updatedInput.addClass('saveInModelLater');
            } else {
                $updatedInput.trigger('updateModelInput');
            }
        }
        
        if (!self.wizletInfo.submitBtn) 
            self.addVote(total,totalValue);

    };

    Range.prototype.distinctValues = function (value, index, inputs) {
        var self = this; 

        var values = inputs.filter('[data-value]').map(function (idx,ele) {
                            return $(ele).data('value');
                    }).get();

        var distinctValue = true;
        $.each(values, function (idx, val) {
            if (idx != index) {
                if (value == val) {
                    distinctValue = false;
                    lastval = self.lastValues[index];
                    $(inputs[index]).data('value', lastval);
                    $(inputs[index]).data("ionRangeSlider").update({from:lastval})
                    M.toast({
                        html: self.wizletInfo.distinctValuesMsg,
                        classes: 'rounded'
                    });
                }
            }
        });

        if (distinctValue)
            self.lastValues = values;

    };


    Range.prototype.addVote = function (questionName, val) {
        var self = this;
        var questionId = self.wizerApi.getQuestionIdByName(questionName);
        self.wizerApi.addVotes({
            votes:[{
                questionId:   questionId, 
                responseText: val
            }]});
    };

    Range.prototype.addVotes = function (questionNames, values) {
        var self = this; 

        var myVotes = [];
        questionNames.forEach(function(q,pos) { 
            myVotes.push( { 
                questionId: self.wizerApi.getQuestionIdByName(q), 
                responseText: values[pos] } ); 
        });
        self.wizerApi.addVotes({ votes: myVotes });
    };
    
    Range.getRegistration = function () {
        return new Range();
    };

    return Range;

});