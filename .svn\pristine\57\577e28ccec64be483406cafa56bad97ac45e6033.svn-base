.wizlet.wizletPassword .card .card-content .card-title {
  font-family: client<PERSON><PERSON>, Arial;
  border-bottom: 3px solid #cc4a3d;
}
.wizlet.wizletPassword .card .card-content .row {
  margin: 0;
}
.wizlet.wizletPassword .card .card-content form {
  padding: 0;
}
.wizlet.wizletPassword .card .card-content form textarea {
  font-size: 20px;
  height: 50px;
}
.wizlet.wizletPassword .card .card-content form textarea ~ label[for] {
  -webkit-transform: translateY(0px);
      -ms-transform: translateY(0px);
       -o-transform: translateY(0px);
          transform: translateY(0px);
}
.wizlet.wizletPassword .card .card-content form textarea ~ label[for].active {
  -webkit-transform: translateY(-10px);
      -ms-transform: translateY(-10px);
       -o-transform: translateY(-10px);
          transform: translateY(-10px);
}
.wizlet.wizletPassword .card .card-content form .subtitle {
  margin-top: 2rem;
  color: #cc4a3d;
  font-family: clientB<PERSON>, Arial;
}
.wizlet.wizletPassword .row.submit {
  padding-top: 5px;
  margin-right: 0;
  text-align: right;
}
.wizlet.wizletPassword .row.submit #checkBtn[hidden] {
  display: inline-block !important;
  visibility: hidden;
}
@media only screen and (max-width : 600px) {
  .wizlet.wizletPassword .row.submit .btn {
    font-size: 0;
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
  .wizlet.wizletPassword .row.submit .btn i {
    width: inherit;
    display: inline-block;
    text-align: center;
    color: #fff;
    font-size: 1.6rem;
    line-height: 40px;
  }
}