.wizlet.wizletHCPieChartModel .card .card-content {
  padding: 6px 12px;
}
.wizlet.wizletHCPieChartModel .card .card-content .row {
  margin: 0;
}
.wizlet.wizletHCPieChartModel .highcharts-container > svg {
  width: 100% !important;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-background {
  fill: transparent;
}
@media only screen and (min-width : 993px) {
  .wizlet.wizletHCPieChartModel .highcharts-container .highcharts-data-label {
    font-size: 120%;
  }
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-data-label span {
  color: #ffffff;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-data-label tspan {
  fill: #ffffff;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-data-labels .highcharts-label span {
  text-align: center;
  top: -2px !important;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-data-labels .highcharts-label span img {
  display: block;
  margin-bottom: -8px;
  padding: 5px;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-0 {
  fill: #cc4a3d;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-0.highcharts-legend-item > .highcharts-point {
  stroke: #cc4a3d;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-1 {
  fill: #24466b;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-1.highcharts-legend-item > .highcharts-point {
  stroke: #24466b;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-2 {
  fill: #107ab0;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-2.highcharts-legend-item > .highcharts-point {
  stroke: #107ab0;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-3 {
  fill: #363737;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-3.highcharts-legend-item > .highcharts-point {
  stroke: #363737;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-4 {
  fill: #727FAE;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-4.highcharts-legend-item > .highcharts-point {
  stroke: #727FAE;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-5 {
  fill: #72BF44;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-5.highcharts-legend-item > .highcharts-point {
  stroke: #72BF44;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-6 {
  fill: #0AB9F0;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-6.highcharts-legend-item > .highcharts-point {
  stroke: #0AB9F0;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-7 {
  fill: #2E739E;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-7.highcharts-legend-item > .highcharts-point {
  stroke: #2E739E;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-8 {
  fill: #E8CD41;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-8.highcharts-legend-item > .highcharts-point {
  stroke: #E8CD41;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-9 {
  fill: #16B2AB;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-9.highcharts-legend-item > .highcharts-point {
  stroke: #16B2AB;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-10 {
  fill: #727FAE;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-10.highcharts-legend-item > .highcharts-point {
  stroke: #727FAE;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-11 {
  fill: #72BF44;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-color-11.highcharts-legend-item > .highcharts-point {
  stroke: #72BF44;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-data-label-connector {
  fill: none;
}
.wizlet.wizletHCPieChartModel .highcharts-container .highcharts-tooltip table tr, .wizlet.wizletHCPieChartModel .highcharts-container .highcharts-tooltip table td {
  padding: 0;
}
@media only screen and (min-width : 601px) {
  .wizlet.wizletHCPieChartModel .bach-content-pieChart--container.labels.medium .highcharts-data-label {
    font-size: 125%;
  }
}
@media only screen and (min-width : 601px) {
  .wizlet.wizletHCPieChartModel .bach-content-pieChart--container.labels.small .highcharts-data-label {
    font-size: 100%;
  }
}
.wizlet.wizletHCPieChartModel .bach-content-pieChart--container.color.categoria1 .highcharts-color-0,
.wizlet.wizletHCPieChartModel .bach-content-pieChart--container.color.categoria1 .highcharts-color-0 .highcharts-point {
  fill: #16B2AB;
  stroke: #16B2AB;
}
.wizlet.wizletHCPieChartModel .bach-content-pieChart--container.color.categoria2 .highcharts-color-0,
.wizlet.wizletHCPieChartModel .bach-content-pieChart--container.color.categoria2 .highcharts-color-0 .highcharts-point {
  fill: #727FAE;
  stroke: #727FAE;
}
.wizlet.wizletHCPieChartModel .bach-content-pieChart--container.color.categoria3 .highcharts-color-0,
.wizlet.wizletHCPieChartModel .bach-content-pieChart--container.color.categoria3 .highcharts-color-0 .highcharts-point {
  fill: #72BF44;
  stroke: #72BF44;
}
.wizlet.wizletHCPieChartModel .bach-content-pieChart--container.color.categoria4 .highcharts-color-0,
.wizlet.wizletHCPieChartModel .bach-content-pieChart--container.color.categoria4 .highcharts-color-0 .highcharts-point {
  fill: #0AB9F0;
  stroke: #0AB9F0;
}
.wizlet.wizletHCPieChartModel .bach-content-pieChart--container.color.utilizacion .highcharts-color-5,
.wizlet.wizletHCPieChartModel .bach-content-pieChart--container.color.utilizacion .highcharts-color-5 .highcharts-point {
  fill: #FFFFFF;
  stroke: #FFFFFF;
}
.wizlet.wizletHCPieChartModel .bach-content-pieChart--container.color.utilizacion .highcharts-color-5.highcharts-legend-item .highcharts-point {
  stroke: #6d747e;
}
.wizlet.wizletHCPieChartModel .bach-content-pieChart--container.withIcons .highcharts-data-labels {
  margin-top: -1rem;
}
.wizlet.wizletHCPieChartModel .bach-content-pieChart--container.noclickablelegend .highcharts-legend {
  pointer-events: none;
}